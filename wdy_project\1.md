以下是实现批量填写Word文档中表格的Python脚本，使用python-docx库处理Word文档。脚本按照要求处理两种表格类型：表一/表二（直接替换）和表三（跨表关联替换）。

Python
from docx import Document
import re

def fill_word_tables(doc_path, output_path):
    # 打开Word文档
    doc = Document(doc_path)
    
    # 第一步：处理表一和表二（替换为左侧列的值）
    for table in doc.tables:
        for row in table.rows:
            cells = row.cells
            for i in range(len(cells) - 1, 0, -1):  # 从右向左遍历（避免覆盖）
                if "(投标人填写)" in cells[i].text:
                    # 取左侧单元格的值
                    left_value = cells[i-1].text.strip()
                    # 清空当前单元格并写入新值（保留格式）
                    clear_cell(cells[i])
                    cells[i].text = left_value
    
    # 第二步：构建表一的映射字典（第二列 -> 最后一列）
    mapping_dict = {}
    if len(doc.tables) > 0:
        table1 = doc.tables[0]  # 假设第一个表格是表一
        for row in table1.rows:
            cols = row.cells
            if len(cols) >= 2:
                key = cols[1].text.strip()  # 第二列作为键
                value = cols[-1].text.strip()  # 最后一列作为值
                if key:
                    mapping_dict[key] = value
    
    # 第三步：处理表三（根据映射字典替换）
    if len(doc.tables) > 2:  # 假设第三个表格是表三
        table3 = doc.tables[2]
        for row in table3.rows:
            cols = row.cells
            if len(cols) >= 2:
                # 获取第二列的关键字（如"五金件"）
                keyword = cols[1].text.strip()
                # 查找最后一列中的"(投标人填写)"
                if keyword and "(投标人填写)" in cols[-1].text:
                    # 从字典获取表一的值（若无则留空）
                    fill_value = mapping_dict.get(keyword, "")
                    # 清空并写入新值
                    clear_cell(cols[-1])
                    cols[-1].text = fill_value
    
    # 保存修改后的文档
    doc.save(output_path)

def clear_cell(cell):
    """清空单元格内容（保留段落格式）"""
    for paragraph in cell.paragraphs:
        for run in paragraph.runs:
            run.text = ""
    cell.text = ''  # 确保清空所有内容

# 使用示例
input_file = "input.docx"
output_file = "output.docx"
fill_word_tables(input_file, output_file)
功能说明：
表一/表二处理：

遍历所有表格的每个单元格
发现(投标人填写)时，用左侧单元格的值替换
从右向左遍历避免覆盖问题
表三处理：

从第一个表格（表一）构建映射字典：{第二列内容: 最后一列内容}
在第三个表格（表三）中：
读取第二列作为关键字（如五金件）
用表一映射值替换最后一列的(投标人填写)
使用注意事项：
表格位置假设：

表一：文档的第一个表格
表三：文档的第三个表格
如实际位置不同，需调整代码中的表格索引
格式保留：

使用clear_cell()函数清空内容时保留段落格式
替换操作不影响单元格样式（字体/颜色等）
特殊场景：

表三的关键字在表一中不存在时，留空处理
支持单元格内多段落文本处理
扩展建议：
如果文档中有多个表三类型的表格，可扩展映射关系（需调整表格识别逻辑）
如需处理合并单元格，需增加cell.merge相关逻辑
关键文本(投标人填写)可按需修改为其他标识符
执行脚本后，输出文档中的表格将按规则自动填充内容

