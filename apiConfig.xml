<?xml version="1.0" encoding="utf-8"?>
<configuration>

	<!--统一的api或公共参数配置-->
	<ApiConfigs>
		
		<!--请求连接url-->
        <Api nodeCode="dataApiPrefixUrl" value="1a69cbc37e35134e881eb187ae91b00d11543a32a1400ac623ece76b2d7147efc69bb500f7ea3ca7eef168544b89e77df0e195ab0e1e6388e9d46ee0356857e2"></Api>
		<Api nodeCode="dataApiPrefixUrl2" value="1a69cbc37e35134e881eb187ae91b00d11543a32a1400ac623ece76b2d7147efc69bb500f7ea3ca7eef168544b89e77df0e195ab0e1e6388e9d46ee0356857e2"></Api>
		<!--线上版本对比请求接口-->
		<Api nodeCode="onlineVersion" value="getLasterVerInfo"></Api>
		
		<!--数据库位置-->
		<Api nodeCode="sqliteDBLocation" value="database"></Api>
		
		<!--数据库名称-->
		<Api nodeCode="sqliteDBName" value="zbfz.sqlite"></Api>

		<!--基础配置-->
		<Api nodeCode="baseDBName" value="base.sqlite" />
		
		<!--版权-->
		<Api nodeCode="copyright" value="技术支持(工作时间:工作日08:30-11:30,13:30-17:30) 电话：0551-62628009，0551-62628100 电话未接通再加运维QQ（高工QQ：1815290022 焦工QQ:1650224235 钱工QQ：272740950） " />
		
		<Api nodeCode="version" value="3.0" />

		<!--其他的xml配置-->
		<Api nodeCode="myConfigXml" value="myconfig.xml" />

		<!--上架商品信息导出 按钮-->
		<Api nodeCode="goodsImpBtn" value="no" />

		<!--是否显示数据合并页面-->
		<Api nodeCode="monitorToolStripMenuItem" value="0" />

		<!--价格压缩文件的维度  jishu-技术 shangwu-商务-->
		<Api nodeCode="priceZipWd" value="jishu" />

		<!--技术支持文件大小 以M为单位-->
		<Api nodeCode="techFileSize" value="800" />
		
		<!--商务支持文件大小 以M为单位-->
		<Api nodeCode="businessFileSize" value="500" />

		<!--技术压缩大小 以M为单位-->
		<Api nodeCode="techZipFileSize" value="500" />
		<!--商务压缩大小 以M为单位-->
		<Api nodeCode="businessZipFileSize" value="500" />

		<!--技术响应一览表大小 以M为单位-->
		<Api nodeCode="techResponseFileSize" value="5" />

		<!--商务响应一览表大小 以M为单位-->
		<Api nodeCode="businessResponseFileSize" value="5" />

		<!--技术价格文件大小 以M为单位-->
		<Api nodeCode="techPriceFileSize" value="30" />

		<!--商务价格文件大小 以M为单位-->
		<Api nodeCode="businessPriceFileSize" value="30" />

		<!--上架商品压缩包大小 以M为单位-->
		<Api nodeCode="techGoodsFileSize" value="200" />

		<!--技术支持文件格式 不区分物资（一般为pdf）和服务-->
		<Api nodeCode="techFileExtension" value="*pdf;" />

		<!--商务支持文件格式 不区分物资（一般为pdf）和服务-->
		<Api nodeCode="businessFileExtension" value="*pdf;" />
		
		
		<!--资格预审文件大小 以M为单位-->
		<Api nodeCode="zgysFileSize" value="200" />
		
		<!--资格预审文件格式-->
		<Api nodeCode="zgysFileExtension" value="*pdf;" />

		<!--01不校验-->
		<Api nodeCode="noBookMark" value="01" />
		
		<!--默认为full 包含full和onlyWarn-->
		<Api nodeCode="verifyWay" value="onlyWarn" />
		
		<!--升级程序的数据库-->
		<Api nodeCode="upgradeDb" value="zbfz.db" />
		
		<!--是否控制台输出日志-->
		<Api nodeCode="ConsoleWrite" value="true" />

		<!--启动升级-->
		<Api nodeCode="upgradeEnable" value="true" />

		<!--是否开启多线路/多通道-->
		<Api nodeCode="mulLines" value="FALSE"></Api>
		
		<!--是否校验技术规范书（工程量清单）PDF-->
		<Api nodeCode="verifySpecifyBookPdf" value="false"></Api>		
		
		<!--是否校验价格文件系统生成-->
		<Api nodeCode="verifySpecifyBookSysCreate" value="false"></Api>
	</ApiConfigs>

</configuration>