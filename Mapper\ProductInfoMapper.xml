<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	
	<!--
		检查标段主图是否填写 
	-->
	<select id="checkProductInfo"  >
		select ('网省申请号：'||OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO||',未上传主图') AS MSG from OS_ZB_SUPPLIER_PRODUCT_INFO
		LEFT join SYS_FILE_INFO on OS_ZB_SUPPLIER_PRODUCT_INFO.ID = SYS_FILE_INFO.RELATED_ID AND SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_PRODUCT_INFO'
		AND SYS_FILE_INFO.RELATED_KEY = 'main_image'
		WHERE  OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO = '{markNo}'  and SYS_FILE_INFO.ID is null	
		union
		SELECT ('网省申请号：' || OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO || ',未上传副图') AS MSG
		FROM OS_ZB_SUPPLIER_PRODUCT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON OS_ZB_SUPPLIER_PRODUCT_INFO.ID = SYS_FILE_INFO.RELATED_ID AND
		SYS_FILE_INFO.RELATED_PAGE = 'OS_ZB_SUPPLIER_PRODUCT_INFO' AND
		SYS_FILE_INFO.RELATED_KEY = 'other_image'
		WHERE OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO = '{markNo}' AND
		SYS_FILE_INFO.ID IS NULL;
	</select>


	<select id="selectMainImages"  >
		SELECT
		FILE_DISP_NAME,FILE_FORMAT,FILE_PATH,
		('上架商品/'||'/{entName}_'||OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO||'_'||OS_ZB_MATERIAL_LIST.MATERIAL_NAME||'_'||OS_ZB_MATERIAL_LIST.UNIT) as savePath,
		OS_ZB_SUPPLIER_PRODUCT_INFO.PACK_NAME,OS_ZB_MATERIAL_LIST.MATERIAL_NAME,OS_ZB_MATERIAL_LIST.UNIT
		FROM OS_ZB_MATERIAL_LIST
		INNER JOIN
		OS_ZB_PURCHASE_PROJECT_INFO ON OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_MATERIAL_LIST.MARK_NO AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		OS_ZB_MATERIAL_LIST.PACK_NAME = OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		INNER JOIN
		OS_ZB_SUPPLIER_PRODUCT_INFO ON OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO AND
		OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO = OS_ZB_MATERIAL_LIST.PROVINCE_APPLY_NO

		inner join SYS_FILE_INFO  on OS_ZB_SUPPLIER_PRODUCT_INFO.ID = SYS_FILE_INFO.RELATED_ID AND SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_PRODUCT_INFO' and SYS_FILE_INFO.RELATED_KEY='main_image'
		WHERE OS_ZB_MATERIAL_LIST.MARK_NO = '{markNo}'
		group by OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO || OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO ||SYS_FILE_INFO.ID
	</select>


	<select id="selectOtherImages"  >
		SELECT
		FILE_DISP_NAME,FILE_FORMAT,FILE_PATH,
		('上架商品/'||'/{entName}_'||OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO||'_'||OS_ZB_MATERIAL_LIST.MATERIAL_NAME||'_'||OS_ZB_MATERIAL_LIST.UNIT) as savePath,
		OS_ZB_SUPPLIER_PRODUCT_INFO.PACK_NAME,OS_ZB_MATERIAL_LIST.MATERIAL_NAME,OS_ZB_MATERIAL_LIST.UNIT
		FROM OS_ZB_MATERIAL_LIST
		INNER JOIN
		OS_ZB_PURCHASE_PROJECT_INFO ON OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_MATERIAL_LIST.MARK_NO AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		OS_ZB_MATERIAL_LIST.PACK_NAME = OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		INNER JOIN
		OS_ZB_SUPPLIER_PRODUCT_INFO ON OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO AND
		OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO = OS_ZB_MATERIAL_LIST.PROVINCE_APPLY_NO

		inner join SYS_FILE_INFO  on OS_ZB_SUPPLIER_PRODUCT_INFO.ID = SYS_FILE_INFO.RELATED_ID AND SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_PRODUCT_INFO' and SYS_FILE_INFO.RELATED_KEY='other_image'
		WHERE OS_ZB_MATERIAL_LIST.MARK_NO = '{markNo}'
		group by OS_ZB_SUPPLIER_PRODUCT_INFO.PROVINCE_APPLY_NO || OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO ||SYS_FILE_INFO.ID
	</select>


	<select id="checkMainImages">
		SELECT OS_ZB_SUPPLIER_PRODUCT_INFO.ID
		FROM OS_ZB_SUPPLIER_PRODUCT_INFO
		LEFT JOIN
		sys_file_info  ON OS_ZB_SUPPLIER_PRODUCT_INFO.ID = sys_file_info.RELATED_ID AND
		sys_file_info.RELATED_PAGE = 'OS_ZB_SUPPLIER_PRODUCT_INFO' AND
		sys_file_info.RELATED_KEY = 'main_image'
		WHERE 1 = 1 AND
		OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO = '{markNo}'  and  sys_file_info.ID is null
		limit 0,1
	</select>
	
</mapper>