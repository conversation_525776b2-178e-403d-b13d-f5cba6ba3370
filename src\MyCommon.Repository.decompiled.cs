using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Text;
using System.Text.RegularExpressions;
using Aspose.Cells;
using Aspose.Pdf;
using Microsoft.CodeAnalysis;
using MyCommon.Model.dto;
using MyCommon.Model.entity;
using MyCommon.Model.entity.service;
using MyCommon.Model.enums;
using MyCommon.Model.vo;
using MyCommon.Repository.Repository.ibase;
using MyCommon.Repository.Repository.impl;
using MyCommon.Repository.common;
using MyCommon.Repository.dataContext;
using MyCommon.Repository.dataSource;
using MyCommon.Repository.dataSource.Base;
using MyCommon.Repository.enums;
using MyCommon.Repository.ibase;
using MyCommon.Repository.impl;
using MyCommon.adapter;
using MyCommon.attr;
using MyCommon.condition;
using MyCommon.condition.factory;
using MyCommon.dto;
using MyCommon.entity;
using MyCommon.enums;
using MyCommon.extendmethods;
using MyCommon.options;
using MyCommon.util;
using MyCommon.vo;
using Newtonsoft.Json;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("MyCommon.Repository")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("MyCommon.Repository")]
[assembly: AssemblyCopyright("Copyright ©  2024")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("6ea81452-a0d8-4965-8c22-bf6db8d470cd")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace Microsoft.CodeAnalysis
{
	[CompilerGenerated]
	[Microsoft.CodeAnalysis.Embedded]
	internal sealed class EmbeddedAttribute : Attribute
	{
	}
}
namespace System.Runtime.CompilerServices
{
	[CompilerGenerated]
	[Microsoft.CodeAnalysis.Embedded]
	internal sealed class IsReadOnlyAttribute : Attribute
	{
	}
}
namespace MyCommon.Repository
{
	public interface IBaseRepository<TEntity> where TEntity : CommonEntity
	{
		IList<TEntity> Search(SqlWrapper<TEntity> sqlWrapper);

		BendPageInfo<TEntity> Search(SqlWrapper<TEntity> sqlWrapper, BendPageInfo<TEntity> pageInfo);

		BendPageInfo<TEntity> SearchByOtherEntity<OtherEntity>(SqlWrapper<OtherEntity> sqlWrapper, BendPageInfo<TEntity> pageInfo) where OtherEntity : CommonEntity;

		IList<TEntity> Search(TEntity query, string selectColumnSql = null, Expression<Func<TEntity, object>>[] columns = null, string joinTableSql = null, Expression<Func<TEntity, object>>[] whereColums = null, string groupby = null);

		BendPageInfo<TEntity> Search(BendPageInfo<TEntity> pageInfo, string selectColumnSql = null, string joinTableSql = null, Expression<Func<TEntity, object>>[] whereColums = null, string groupby = null, string whereSqlFragment = null);

		void ExportToExcel(string _fileName, SqlWrapper<TEntity> sqlWrapper, ExpertOption expertOption = null);

		void DownloadExcelTemplate(string _fileName, IList<TEntity> list, ExpertOption expertOption = null);

		bool ImportExcel(out IList<TEntity> entities, int titleRow = 0, Func<TEntity, int, string> CheckAction = null, string businessId = null, int startSheet = 0);

		TEntity AddOrUpdate(TEntity entity, Func<TEntity, string> primaryKeyAction, string primaryPropName = "Id", Expression<Func<TEntity, object>>[] whereColums = null, string aliasPrimaryColumn = null);

		bool DeleteByPrimaryKeysWithMakeBook(OsZbPurchaseProjectInfo project = null, BidBookType bidBookType = BidBookType.BusinessBidBook, params string[] primaryKeys);

		bool DeleteByPrimaryKeys(params string[] primaryKeys);

		bool ExecuteSqlByTransaction(params string[] sqls);

		bool VacuumWithSqlite();

		bool AddBatch(IList<TEntity> entities);

		bool AddBatch(SqlWrapper<TEntity> sqlWrapper, IEnumerable<TEntity> collections, Expression<Func<TEntity, object>>[] ignoreColumns = null);

		bool UpdateBatch(List<TEntity> entities, Expression<Func<TEntity, object>>[] columns = null, params Expression<Func<TEntity, object>>[] whereColums);

		bool DeleteBatch(List<TEntity> list, params Expression<Func<TEntity, object>>[] whereColums);

		bool AddItem(TEntity tEntity);

		bool UpdateItem(SqlWrapper<TEntity> sqlWrapper);

		bool DeleteItem(SqlWrapper<TEntity> sqlWrapper);

		bool DeleteNormalItem(SqlWrapper<TEntity> sqlWrapper);

		int SearchCount(SqlWrapper<TEntity> sqlWrapper);

		TEntity SearchOne(SqlWrapper<TEntity> sqlWrapper);

		IList<TOtherEntity> SearchCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity;

		TOtherEntity SearchOneCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity;

		IList<TResult> SearchOther<TOtherEntity, TResult>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity where TResult : CommonEntity;

		int SearchCountCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity;

		void UpdateAndStoreFile(IEnumerable<TEntity> list, Expression<Func<TEntity, object>>[] updateCols, Expression<Func<TEntity, object>>[] whereCols);
	}
}
namespace MyCommon.Repository.strategy
{
	public interface BaseVerifyStrategy
	{
		bool Verify(object obj1, object obj2, object obj3);
	}
	public class VerifyChain
	{
		internal List<BaseVerifyStrategy> VerifyStragegies { get; set; }

		public bool Verify(object obj1, object obj2, object obj3)
		{
			VerifyStragegies?.ForEach(delegate(BaseVerifyStrategy x)
			{
				x.Verify(obj1, obj2, obj3);
			});
			return true;
		}
	}
}
namespace MyCommon.Repository.Repository.impl
{
	public class OsZbSupplierAuthPersonInfoRepository : BaseRepository<OsZbSupplierAuthPersonInfo>, IOsZbSupplierAuthPersonInfoRepository, IBaseRepository<OsZbSupplierAuthPersonInfo>
	{
	}
	public class OsZbSupplierBiaoFinanceInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoFinanceInfo>, IOsZbSupplierBiaoFinanceInfoRepository, IBaseRepository<OsZbSupplierBiaoFinanceInfo>
	{
		private const string relTable = "OS_ZB_SUPPLIER_FINANCE_INFO";

		public List<OsZbSupplierBiaoFinanceInfo> SearchAndBindingAttach(OsZbPurchaseProjectInfo currentProjectInfo, string modelName)
		{
			ISysModelConfigRepository sysModelConfigRepository = new SysModelConfigRepository(new DefaultDbDataSource(sqliteDbLocation, sqliteDbName));
			ISysFileInfoRepository sysFileInfoRepository = new SysFileInfoRepository(new DefaultDbDataSource(sqliteDbLocation, sqliteDbName));
			SqlWrapper<SysModelConfig> sqlWrapper = SqlWrapperFactory.Instance<SysModelConfig>().Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.MarkNo), WhereCondition.AND, (object)currentProjectInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.PackName), WhereCondition.AND, (object)currentProjectInfo.PackName, SqlSymbol.None)
				.Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.ModelCode), WhereCondition.AND, (object)"FINANCE_REPORT_YEAR", SqlSymbol.None);
			SysModelConfig sysModelConfig = sysModelConfigRepository.SearchOne(sqlWrapper);
			List<string> source = (from v in sysModelConfig?.Value?.Replace("年", "").Replace("，", ",").Split(',', '，')
				orderby v
				select v).ToList();
			Dictionary<string, string> dictionary = source.ToDictionaryByLocal<string, string, string>((string v) => v.Trim(), (string v) => v);
			Dictionary<string, string> dictionary2 = source.ToDictionaryByLocal<string, string, string>((string v) => v.Trim(), (string v) => v);
			int num = Convert.ToInt32(source.First());
			int num2 = Convert.ToInt32(source.Last());
			string value = PublicVo.EntRegTime.Split('/', '-')[0];
			int num3 = Convert.ToInt32(value);
			bool flag = num3 >= num2;
			bool flag2 = num2 >= num3 && num <= num3;
			bool flag3 = dictionary2.Count > 3;
			int count = dictionary2.Count;
			bool flag4 = num > num3;
			if (flag2)
			{
				int num4 = num3;
				while (num4 >= num)
				{
					dictionary2.Remove(num4--.ToString());
				}
			}
			StringBuilder stringBuilder = new StringBuilder();
			SqlWrapper<OsZbSupplierBiaoFinanceInfo> sqlWrapper2 = SqlWrapperFactory.Instance<OsZbSupplierBiaoFinanceInfo>().Eq((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.ProjectNo), WhereCondition.AND, (object)currentProjectInfo.ProjectNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.MarkNo), WhereCondition.AND, (object)currentProjectInfo.MarkNo, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.PackName), WhereCondition.AND, (object)currentProjectInfo.PackName, SqlSymbol.None);
			List<OsZbSupplierBiaoFinanceInfo> list = Search(sqlWrapper2)?.OrderBy<OsZbSupplierBiaoFinanceInfo, string>((OsZbSupplierBiaoFinanceInfo v) => v.YearSj).ToList() ?? new List<OsZbSupplierBiaoFinanceInfo>();
			if (list != null && list.Count > 1)
			{
				List<int> source2 = ((IEnumerable<OsZbSupplierBiaoFinanceInfo>)list).Select<OsZbSupplierBiaoFinanceInfo, int>((Func<OsZbSupplierBiaoFinanceInfo, int>)((OsZbSupplierBiaoFinanceInfo v) => Convert.ToInt32(v.YearSj))).ToList();
				int num5 = Math.Abs(source2.First() - source2.Last());
				if (num5 != list.Count - 1)
				{
					throw new Exception(modelName + "选择的数据年份不连续");
				}
			}
			List<string> list2 = ((IEnumerable<OsZbSupplierBiaoFinanceInfo>)list).Select<OsZbSupplierBiaoFinanceInfo, string>((Func<OsZbSupplierBiaoFinanceInfo, string>)((OsZbSupplierBiaoFinanceInfo v) => v.RelateId)).ToList();
			string text = null;
			if (list2 != null && list2.Any())
			{
				int num6 = Convert.ToInt32(list.Last().YearSj.Replace("年", "").Trim());
				foreach (OsZbSupplierBiaoFinanceInfo item in list)
				{
					if (flag2)
					{
						if (num3 > Convert.ToInt32(item.YearSj.Replace("年", "").Trim()))
						{
							throw new Exception(modelName + "成立年份在发标时指定年份内,引用数据年份不得早于或等于成立时间，指定年份:" + sysModelConfig.Value);
						}
						dictionary2.Remove(item.YearSj.Replace("年", "").Trim());
						text = "rel";
					}
					else if (flag4)
					{
						dictionary.Remove(item.YearSj.Replace("年", "").Trim());
						text = "abs";
					}
					else if (num6 != num3)
					{
						throw new Exception(modelName + "缺少公司成立年份的数据，成立年份为:" + num3);
					}
				}
				if (text == "abs")
				{
					foreach (KeyValuePair<string, string> item2 in dictionary)
					{
						stringBuilder.AppendLine(modelName + "未引用" + item2.Key + "年份数据");
					}
				}
				else if (text == "rel")
				{
					foreach (KeyValuePair<string, string> item3 in dictionary2)
					{
						stringBuilder.AppendLine(modelName + "未引用" + item3.Key + "年份数据");
					}
				}
				if (stringBuilder.Length > 0 && (!flag3 || dictionary.Count > 1))
				{
					throw new Exception(stringBuilder.ToString());
				}
				SqlWrapper<SysFileInfo> sqlWrapper3 = SqlWrapperFactory.Instance<SysFileInfo>().Eq((Expression<Func<SysFileInfo, object>>)((SysFileInfo v) => v.RelatedPage), WhereCondition.AND, (object)"OS_ZB_SUPPLIER_FINANCE_INFO", SqlSymbol.None).In((Expression<Func<SysFileInfo, object>>)((SysFileInfo v) => v.RelatedId), (IEnumerable<object>)list2, WhereCondition.AND);
				IList<SysFileInfo> source3 = sysFileInfoRepository.Search(sqlWrapper3);
				Dictionary<string, List<SysFileInfo>> dictionary3 = (from p in source3
					group p by p.RelatedId + p.RelatedKey).ToDictionary<IGrouping<string, SysFileInfo>, string, List<SysFileInfo>>((IGrouping<string, SysFileInfo> v) => v.Key, (IGrouping<string, SysFileInfo> v) => v.ToList());
				IDictionary<string, string> finatinceFileTypeDict = ConstantVo.FinatinceFileTypeDict;
				foreach (OsZbSupplierBiaoFinanceInfo item4 in list)
				{
					if (dictionary3.ContainsKey(item4.RelateId + "cwsjbg"))
					{
						item4.AttachFiles1 = dictionary3[item4.RelateId + "cwsjbg"];
					}
					if (dictionary3.ContainsKey(item4.RelateId + "zcfzbiao"))
					{
						item4.AttachFiles2 = dictionary3[item4.RelateId + "zcfzbiao"];
					}
					if (dictionary3.ContainsKey(item4.RelateId + "xjllbiao"))
					{
						item4.AttachFiles3 = dictionary3[item4.RelateId + "xjllbiao"];
					}
					if (dictionary3.ContainsKey(item4.RelateId + "lirunbiao"))
					{
						item4.AttachFiles4 = dictionary3[item4.RelateId + "lirunbiao"];
					}
					if (dictionary3.ContainsKey(item4.RelateId + "cwqksm"))
					{
						item4.CwqksmFileList = dictionary3[item4.RelateId + "cwqksm"];
					}
					if (dictionary3.ContainsKey(item4.RelateId + "zscwbiao"))
					{
						item4.ZscwbiaoFileList = dictionary3[item4.RelateId + "zscwbiao"];
					}
				}
			}
			else if (num3 < DateTime.Now.Year && !flag)
			{
				throw new Exception(modelName + "未引用任何年份数据");
			}
			return list;
		}
	}
}
namespace MyCommon.Repository.Repository.ibase
{
	public interface IOsZbSupplierBiaoEquityInfoRepository : IBaseRepository<OsZbSupplierBiaoEquityInfo>
	{
	}
	public interface IOsZbSupplierAuthPersonInfoRepository : IBaseRepository<OsZbSupplierAuthPersonInfo>
	{
	}
	public interface IOsZbSupplierBiaoFinanceInfoRepository : IBaseRepository<OsZbSupplierBiaoFinanceInfo>
	{
		List<OsZbSupplierBiaoFinanceInfo> SearchAndBindingAttach(OsZbPurchaseProjectInfo currentProjectInfo, string modelName);
	}
}
namespace MyCommon.Repository.impl
{
	public class OsModelFileOwerInfoRepository : BaseAbstractRepository<OsModelFileOwerInfo>, IOsModelFileOwerInfoRepository, IBaseRepository<OsModelFileOwerInfo>
	{
		public OsModelFileOwerInfoRepository()
		{
		}

		public OsModelFileOwerInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbSupplierCommonResponseInfoRepository : BaseAbstractRepository<OsZbSupplierCommonResponseInfo>, IOsZbSupplierCommonResponseInfoRepository, IBaseRepository<OsZbSupplierCommonResponseInfo>
	{
		public OsZbSupplierCommonResponseInfoRepository()
		{
		}

		public OsZbSupplierCommonResponseInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}

		public void GenResposeData(OsZbSupplierCommonResponseInfo resInfo)
		{
			Dictionary<string, object> jsonData = new Dictionary<string, object>();
			jsonData["projectCode"] = resInfo.ProjectNo;
			jsonData["bidCode"] = resInfo.MarkNo;
			jsonData["bidName"] = resInfo.MarkName;
			jsonData["suppId"] = PublicVo.SupplyId;
			jsonData["suppName"] = PublicVo.SupplyName?.Replace("（", "(")?.Replace("）", ")")?.Trim();
			SqlWrapper<OsZbPurchaseProjectInfo> sqlWrapper = SqlWrapperFactory.Instance(new Expression<Func<OsZbPurchaseProjectInfo, object>>[3]
			{
				(OsZbPurchaseProjectInfo v) => v.MarkNo,
				(OsZbPurchaseProjectInfo v) => v.MarkName,
				(OsZbPurchaseProjectInfo v) => v.PackName
			}).Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo v) => v.Selected), WhereCondition.AND, (object)"1", SqlSymbol.None)
				.GroupBy(new Expression<Func<OsZbPurchaseProjectInfo, object>>[2]
				{
					(OsZbPurchaseProjectInfo v) => v.MarkNo,
					(OsZbPurchaseProjectInfo vo) => vo.PackName
				});
			IList<OsZbPurchaseProjectInfo> source = SearchCommon(sqlWrapper);
			source = source.OrderBy<OsZbPurchaseProjectInfo, int>((OsZbPurchaseProjectInfo v) => Convert.ToInt32(v.PackName?.Replace("包", "")?.Trim() ?? "0")).ToList();
			jsonData["packageName"] = string.Join(",", ((IEnumerable<OsZbPurchaseProjectInfo>)source).Select<OsZbPurchaseProjectInfo, string>((Func<OsZbPurchaseProjectInfo, string>)((OsZbPurchaseProjectInfo v) => v.PackName)));
			SqlWrapper<SysModelConfig> sqlWrapper2 = SqlWrapperFactory.Instance<SysModelConfig>().Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.In((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.Value), (IEnumerable<object>)new string[3] { "是", "Y", "YES" }, WhereCondition.AND);
			if (string.IsNullOrWhiteSpace(resInfo.PackName))
			{
				sqlWrapper2.GroupBy(new Expression<Func<SysModelConfig, object>>[1]
				{
					(SysModelConfig v) => v.MarkNo
				}).GroupBy(new Expression<Func<SysModelConfig, object>>[1]
				{
					(SysModelConfig v) => v.ModelCode
				});
			}
			Dictionary<string, SysModelConfig> configDict = SearchCommon(sqlWrapper2)?.ToDictionary<SysModelConfig, string, SysModelConfig>((SysModelConfig v) => v.ModelCode, (SysModelConfig v) => v) ?? new Dictionary<string, SysModelConfig>();
			SqlWrapper<OsZbSupplierInfo> sqlWrapper3 = SqlWrapperFactory.Instance<OsZbSupplierInfo>().Eq((Expression<Func<OsZbSupplierInfo, object>>)((OsZbSupplierInfo v) => v.Id), WhereCondition.AND, (object)PublicVo.SupplyId, SqlSymbol.None);
			OsZbSupplierInfo supplierInfo = SearchOneCommon(sqlWrapper3);
			if ("business" == resInfo.Flag)
			{
				GenBusinessResposeData(resInfo, configDict, supplierInfo, in jsonData);
				resInfo.Flag = "shangwu";
			}
			else if ("skill" == resInfo.Flag)
			{
				GenTechResposeData(resInfo, configDict, supplierInfo, jsonData);
				resInfo.Flag = "jishu";
			}
			resInfo.JsonData = JsonConvert.SerializeObject(jsonData);
			resInfo.CreateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
			SqlWrapper<OsZbSupplierCommonResponseInfo> sqlWrapper4 = SqlWrapperFactory.Instance(resInfo).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.PackName), WhereCondition.AND, (object)null, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.Flag), WhereCondition.AND, (object)null, SqlSymbol.None);
			DeleteItem(sqlWrapper4);
			AddItem(resInfo);
		}

		private void GenTechResposeData(OsZbSupplierCommonResponseInfo resInfo, Dictionary<string, SysModelConfig> configDict, OsZbSupplierInfo supplierInfo, Dictionary<string, object> jsonData)
		{
			SqlWrapper<OsZbSupplierBiaoChangeFile> sqlWrapper = SqlWrapperFactory.Instance<OsZbSupplierBiaoChangeFile>().Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.Type), WhereCondition.AND, (object)"jishu", SqlSymbol.None);
			IList<OsZbSupplierBiaoChangeFile> list = SearchCommon(sqlWrapper);
			StringBuilder stringBuilder = new StringBuilder();
			foreach (OsZbSupplierBiaoChangeFile item in list)
			{
				stringBuilder.Append(item.FormerName + "," + item.UsageStartTime + "-" + item.UsageEndTime);
			}
			jsonData["beforeName"] = StringUtil.FillWhiteSpace(stringBuilder, "/");
			SqlWrapper<OsZbBiaoTechExtraInfo> sqlWrapper2 = SqlWrapperFactory.Instance<OsZbBiaoTechExtraInfo>().Eq((Expression<Func<OsZbBiaoTechExtraInfo, object>>)((OsZbBiaoTechExtraInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbBiaoTechExtraInfo, object>>)((OsZbBiaoTechExtraInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None);
			OsZbBiaoTechExtraInfo osZbBiaoTechExtraInfo = SearchOneCommon(sqlWrapper2);
			jsonData["productType"] = StringUtil.FillWhiteSpace(osZbBiaoTechExtraInfo?.ProductBrandInfo, "/");
			jsonData["productInfo"] = StringUtil.FillWhiteSpace(osZbBiaoTechExtraInfo?.ComponentInfo, "/");
			DateTime now = DateTime.Now;
			int num = now.Year - 1;
			int num2 = now.Year - 2;
			int num3 = now.Year - 3;
			double num4 = 0.0;
			SqlWrapper<OsZbSupplierBiaoSalesInfo> sqlWrapper3 = SqlWrapperFactory.Instance(new Expression<Func<OsZbSupplierBiaoSalesInfo, object>>[4]
			{
				(OsZbSupplierBiaoSalesInfo c) => c.CalWay,
				(OsZbSupplierBiaoSalesInfo c) => c.MarkNo,
				(OsZbSupplierBiaoSalesInfo c) => c.PackName,
				(OsZbSupplierBiaoSalesInfo c) => c.Unit
			}).Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.ExtraColumn((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo e) => e.SaleCount), "SALE_COUNT", SqlFunctionEnum.Sum)
				.ExtraColumn((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo e) => e.SaleMoney), "SALE_MONEY", SqlFunctionEnum.Sum)
				.LikeRight((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.DateAssign), WhereCondition.AND, (object)num, SqlSymbol.None);
			OsZbSupplierBiaoSalesInfo osZbSupplierBiaoSalesInfo = SearchOneCommon(sqlWrapper3);
			if (osZbSupplierBiaoSalesInfo != null)
			{
				osZbSupplierBiaoSalesInfo.SaleInfo = ((osZbSupplierBiaoSalesInfo.CalWay == "销售数量") ? osZbSupplierBiaoSalesInfo.SaleCount : osZbSupplierBiaoSalesInfo.SaleMoney);
				num4 += Convert.ToDouble(osZbSupplierBiaoSalesInfo.SaleInfo);
			}
			jsonData["offsetOneYear"] = StringUtil.FillWhiteSpace(osZbSupplierBiaoSalesInfo?.SaleInfo, "0");
			sqlWrapper3.ClearCondition();
			sqlWrapper3.Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None).LikeRight((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.DateAssign), WhereCondition.AND, (object)num2, SqlSymbol.None);
			OsZbSupplierBiaoSalesInfo osZbSupplierBiaoSalesInfo2 = SearchOneCommon(sqlWrapper3);
			if (osZbSupplierBiaoSalesInfo2 != null)
			{
				osZbSupplierBiaoSalesInfo2.SaleInfo = ((osZbSupplierBiaoSalesInfo2.CalWay == "销售数量") ? osZbSupplierBiaoSalesInfo2.SaleCount : osZbSupplierBiaoSalesInfo2.SaleMoney);
				num4 += Convert.ToDouble(osZbSupplierBiaoSalesInfo2.SaleInfo);
			}
			jsonData["offsetTwoYear"] = StringUtil.FillWhiteSpace(osZbSupplierBiaoSalesInfo2?.SaleInfo, "0");
			sqlWrapper3.ClearCondition();
			sqlWrapper3.Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None).LikeRight((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo v) => v.DateAssign), WhereCondition.AND, (object)num3, SqlSymbol.None);
			OsZbSupplierBiaoSalesInfo osZbSupplierBiaoSalesInfo3 = SearchOneCommon(sqlWrapper3);
			if (osZbSupplierBiaoSalesInfo3 != null)
			{
				osZbSupplierBiaoSalesInfo3.SaleInfo = ((osZbSupplierBiaoSalesInfo3.CalWay == "销售数量") ? osZbSupplierBiaoSalesInfo3.SaleCount : osZbSupplierBiaoSalesInfo3.SaleMoney);
				num4 += Convert.ToDouble(osZbSupplierBiaoSalesInfo3.SaleInfo);
			}
			jsonData["offsetThreeYear"] = StringUtil.FillWhiteSpace(osZbSupplierBiaoSalesInfo3?.SaleInfo, "0");
			jsonData["threeYearsSale"] = num4;
			string[] values = new string[6]
			{
				num.ToString(),
				num + "年",
				num2.ToString(),
				num2 + "年",
				num3.ToString(),
				num3 + "年"
			};
			SqlWrapper<OsZbSupplierBiaoSalesInfo> sqlWrapper4 = SqlWrapperFactory.Instance<OsZbSupplierBiaoSalesInfo>().NotIn((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.DateAssign), (IEnumerable<object>)values, WhereCondition.AND).Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.ExtraColumn((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.SaleMoney), "SALE_MONEY", SqlFunctionEnum.Sum)
				.ExtraColumn((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.SaleCount), "SALE_COUNT", SqlFunctionEnum.Sum);
			sqlWrapper4.Columns = new Expression<Func<OsZbSupplierBiaoSalesInfo, object>>[1]
			{
				(OsZbSupplierBiaoSalesInfo p) => p.Unit
			};
			OsZbSupplierBiaoSalesInfo osZbSupplierBiaoSalesInfo4 = SearchOneCommon(sqlWrapper4);
			jsonData["offTimeSale"] = osZbSupplierBiaoSalesInfo4?.SaleCount ?? osZbSupplierBiaoSalesInfo4?.SaleMoney;
			jsonData["unit"] = StringUtil.FillWhiteSpace((osZbSupplierBiaoSalesInfo?.Unit ?? osZbSupplierBiaoSalesInfo2?.Unit) ?? osZbSupplierBiaoSalesInfo3?.Unit, "/");
			SqlWrapper<OsZbSupplierBiaoMeqpInfo> sqlWrapper5 = SqlWrapperFactory.Instance<OsZbSupplierBiaoMeqpInfo>().Eq((Expression<Func<OsZbSupplierBiaoMeqpInfo, object>>)((OsZbSupplierBiaoMeqpInfo p) => p.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoMeqpInfo, object>>)((OsZbSupplierBiaoMeqpInfo p) => p.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.GroupBy(new Expression<Func<OsZbSupplierBiaoMeqpInfo, object>>[2]
				{
					(OsZbSupplierBiaoMeqpInfo v1) => v1.EqpName,
					(OsZbSupplierBiaoMeqpInfo v2) => v2.Qty
				});
			IList<OsZbSupplierBiaoMeqpInfo> list2 = SearchCommon(sqlWrapper5);
			StringBuilder seed = new StringBuilder();
			seed = list2?.Distinct()?.Aggregate(seed, delegate(StringBuilder mq, OsZbSupplierBiaoMeqpInfo p)
			{
				mq.Append(p.EqpName + ":" + p.Qty + "台;");
				return mq;
			});
			jsonData["productionEquipment"] = StringUtil.FillWhiteSpace(seed, "/");
			SqlWrapper<OsZbSupplierBiaoLabeqpInfo> sqlWrapper6 = SqlWrapperFactory.Instance<OsZbSupplierBiaoLabeqpInfo>().Eq((Expression<Func<OsZbSupplierBiaoLabeqpInfo, object>>)((OsZbSupplierBiaoLabeqpInfo p) => p.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoLabeqpInfo, object>>)((OsZbSupplierBiaoLabeqpInfo p) => p.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.GroupBy(new Expression<Func<OsZbSupplierBiaoLabeqpInfo, object>>[2]
				{
					(OsZbSupplierBiaoLabeqpInfo v1) => v1.EqpName,
					(OsZbSupplierBiaoLabeqpInfo v2) => v2.Qty
				});
			IList<OsZbSupplierBiaoLabeqpInfo> list3 = SearchCommon(sqlWrapper6);
			seed.Clear();
			seed = list3?.Distinct()?.Aggregate(seed, delegate(StringBuilder mq, OsZbSupplierBiaoLabeqpInfo p)
			{
				mq.Append(p.EqpName + ":" + p.Qty + "台;");
				return mq;
			});
			jsonData["experimentEquipment"] = StringUtil.FillWhiteSpace(seed, "/");
			jsonData["coreParamInfo"] = "";
			jsonData["scopeInfo"] = "";
			jsonData["technicalBias"] = "";
			jsonData["isManufacturers"] = "是";
			jsonData["isAuthorization"] = "是";
		}

		private void GenBusinessResposeData(OsZbSupplierCommonResponseInfo resInfo, Dictionary<string, SysModelConfig> configDict, OsZbSupplierInfo supplierInfo, in Dictionary<string, object> jsonData)
		{
			jsonData["regtime"] = supplierInfo.EntRegTime;
			SqlWrapper<OsZbSupplierBiaoChangeFile> sqlWrapper = SqlWrapperFactory.Instance<OsZbSupplierBiaoChangeFile>().Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBiaoChangeFile, object>>)((OsZbSupplierBiaoChangeFile v) => v.Type), WhereCondition.AND, (object)"shangwu", SqlSymbol.None);
			IList<OsZbSupplierBiaoChangeFile> list = SearchCommon(sqlWrapper);
			StringBuilder stringBuilder = new StringBuilder();
			foreach (OsZbSupplierBiaoChangeFile item in list)
			{
				stringBuilder.Append(item.FormerName + "," + item.UsageStartTime + "-" + item.UsageEndTime);
			}
			jsonData["beforeName"] = StringUtil.FillWhiteSpace(stringBuilder, "/");
			SqlWrapper<OsSupplierModelConfig> sqlWrapper2 = SqlWrapperFactory.Instance<OsSupplierModelConfig>().Eq((Expression<Func<OsSupplierModelConfig, object>>)((OsSupplierModelConfig v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsSupplierModelConfig, object>>)((OsSupplierModelConfig v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.In((Expression<Func<OsSupplierModelConfig, object>>)((OsSupplierModelConfig v) => v.Value), (IEnumerable<object>)new string[3] { "是", "Y", "YES" }, WhereCondition.AND);
			int num = SearchCountCommon(sqlWrapper2);
			jsonData["isDescription"] = "无关系";
			if (configDict.ContainsKey("STAFF_IN_OUT_RELATION") && num > 0)
			{
				SqlWrapper<OsZbBiaoPersonRelations> sqlWrapper3 = SqlWrapperFactory.Instance<OsZbBiaoPersonRelations>().Eq((Expression<Func<OsZbBiaoPersonRelations, object>>)((OsZbBiaoPersonRelations v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbBiaoPersonRelations, object>>)((OsZbBiaoPersonRelations v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None);
				int num2 = SearchCountCommon(sqlWrapper3);
				jsonData["isDescription"] = ((num2 <= 0) ? "有关系，未提供说明" : "有关系，已提供说明");
			}
			SqlWrapper<OsZbSupplierAuthPersonInfo> sqlWrapper4 = SqlWrapperFactory.Instance<OsZbSupplierAuthPersonInfo>();
			sqlWrapper4.Join((Expression<Func<SysFileInfo, OsZbSupplierAuthPersonInfo, bool>>)((SysFileInfo file, OsZbSupplierAuthPersonInfo ap) => file.RelatedKey == "authfj" && file.RelatedId == ap.Id), JoinEnum.LEFT, new string[0]);
			sqlWrapper4.Eq((Expression<Func<OsZbSupplierAuthPersonInfo, object>>)((OsZbSupplierAuthPersonInfo p) => p.ProjectNo), WhereCondition.AND, (object)resInfo.ProjectNo, SqlSymbol.None).GroupBy(new Expression<Func<OsZbSupplierAuthPersonInfo, object>>[1]
			{
				(OsZbSupplierAuthPersonInfo p) => p.Id
			});
			sqlWrapper4.ExtraColumn((Expression<Func<SysFileInfo, object>>)((SysFileInfo file) => file.Id), "file1CountStr", SqlFunctionEnum.Count);
			OsZbSupplierAuthPersonInfo osZbSupplierAuthPersonInfo = SearchOneCommon(sqlWrapper4);
			OsZbSupplierBiaoEquityInfo query = new OsZbSupplierBiaoEquityInfo
			{
				MarkNo = resInfo.MarkNo,
				PackName = resInfo.PackName,
				PeopelType = "gudong"
			};
			SqlWrapper<OsZbSupplierBiaoEquityInfo> sqlWrapper5 = SqlWrapperFactory.Instance(query).Eq((Expression<Func<OsZbSupplierBiaoEquityInfo, object>>)((OsZbSupplierBiaoEquityInfo v) => v.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoEquityInfo, object>>)((OsZbSupplierBiaoEquityInfo v) => v.PackName), WhereCondition.AND, (object)null, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBiaoEquityInfo, object>>)((OsZbSupplierBiaoEquityInfo v) => v.PeopelType), WhereCondition.AND, (object)null, SqlSymbol.None);
			IList<OsZbSupplierBiaoEquityInfo> source = SearchCommon(sqlWrapper5);
			query = source.OrderByDescending<OsZbSupplierBiaoEquityInfo, decimal>((OsZbSupplierBiaoEquityInfo p) => Convert.ToDecimal(string.IsNullOrEmpty(p.Shares) ? "0" : p.Shares.Replace("%", ""))).FirstOrDefault();
			jsonData["authPersonName"] = StringUtil.FillWhiteSpace(osZbSupplierAuthPersonInfo?.AuthedPerson, "/");
			jsonData["authPersonIdCard"] = StringUtil.FillWhiteSpace(osZbSupplierAuthPersonInfo?.AuthedPersonIdCard, "/");
			jsonData["isPowerAttorney"] = ((!string.IsNullOrEmpty(osZbSupplierAuthPersonInfo?.Id) && !"0".Equals(osZbSupplierAuthPersonInfo.File1CountStr)) ? "是" : "否");
			jsonData["legalName"] = StringUtil.FillWhiteSpace(PublicVo.LegalRep, "/");
			jsonData["legalCardCode"] = StringUtil.FillWhiteSpace(osZbSupplierAuthPersonInfo?.LegalPersonIdCard, "/");
			jsonData["termsDeviation"] = "";
			SqlWrapper<OsZbSupplierDepositDetail> sqlWrapper6 = SqlWrapperFactory.Instance<OsZbSupplierDepositDetail>().Eq((Expression<Func<OsZbSupplierDepositDetail, object>>)((OsZbSupplierDepositDetail v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierDepositDetail, object>>)((OsZbSupplierDepositDetail v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None);
			OsZbSupplierDepositDetail osZbSupplierDepositDetail = SearchOneCommon(sqlWrapper6);
			if (!string.IsNullOrEmpty(resInfo.PackName))
			{
				sqlWrapper6 = SqlWrapperFactory.Instance<OsZbSupplierDepositDetail>().Eq((Expression<Func<OsZbSupplierDepositDetail, object>>)((OsZbSupplierDepositDetail v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None);
				osZbSupplierDepositDetail = SearchOneCommon(sqlWrapper6);
			}
			jsonData["basicAccountInfo"] = StringUtil.FillWhiteSpace(osZbSupplierDepositDetail?.AccountBaseInfo, "/");
			jsonData["purchaseAccountInfo"] = StringUtil.FillWhiteSpace(osZbSupplierDepositDetail?.PurchaseAccountInfo, "/");
			SqlWrapper<OsZbSupplierBiaoFinanceInfo> sqlWrapper7 = SqlWrapperFactory.Instance<OsZbSupplierBiaoFinanceInfo>().Eq((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.OrderBy((Expression<Func<OsZbSupplierBiaoFinanceInfo, object>>)((OsZbSupplierBiaoFinanceInfo v) => v.YearSj), OrderCondition.Asc);
			IList<OsZbSupplierBiaoFinanceInfo> list2 = SearchCommon(sqlWrapper7);
			jsonData["isFinancialSatisfy"] = "";
			if (!list2.Any())
			{
				jsonData["financialProvision"] = "未提供";
				jsonData["researchProportion"] = 0m;
			}
			else
			{
				decimal num3 = default(decimal);
				foreach (OsZbSupplierBiaoFinanceInfo item2 in list2)
				{
					decimal d = decimal.Multiply(decimal.Divide(item2.ScCost.GetValueOrDefault(), Convert.ToDecimal(item2.Zyysrje ?? "1")), 100m);
					d = decimal.Round(d, 2);
					num3 = Math.Max(d, num3);
				}
				string value = string.Join(",", ((IEnumerable<OsZbSupplierBiaoFinanceInfo>)list2).Select<OsZbSupplierBiaoFinanceInfo, string>((Func<OsZbSupplierBiaoFinanceInfo, string>)((OsZbSupplierBiaoFinanceInfo u) => u.YearSj?.Replace("年", ""))));
				jsonData["financialProvision"] = value;
				jsonData["researchProportion"] = num3;
			}
			jsonData["businessProvision"] = "";
			jsonData["afterService"] = "";
			jsonData["supplyGuarantee"] = "否";
			jsonData["greenDevelopment"] = "";
			jsonData["headcount"] = StringUtil.FillWhiteSpace(supplierInfo.HightTitleTotal, "0");
			jsonData["safeguards"] = "";
			jsonData["quotationQuality"] = "一般";
			jsonData["isBadBehavior"] = "否";
			jsonData["badBehaviorDate"] = "无不良行为";
			OsZbSupplierBiaoCreditInfo query2 = new OsZbSupplierBiaoCreditInfo
			{
				MarkNo = resInfo.MarkNo,
				PackName = resInfo.PackName
			};
			SqlWrapper<OsZbSupplierBiaoCreditInfo> sqlWrapper8 = SqlWrapperFactory.Instance(query2).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo v) => v.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo v) => v.PackName), WhereCondition.AND, (object)null, SqlSymbol.None);
			int num4 = SearchCountCommon(sqlWrapper8);
			jsonData["isEnteCredit"] = ((num4 > 0) ? "是" : "否");
			jsonData["isDiscredit"] = "";
			jsonData["qualityPenalty"] = "";
			jsonData["administrativePenalty"] = "";
			jsonData["environmentalPenalty"] = "";
			jsonData["isPromise"] = "否";
			jsonData["isImpartial"] = "否";
			SqlWrapper<OsZbBiaoModelFileInfo> sqlWrapper9 = SqlWrapperFactory.Instance<OsZbBiaoModelFileInfo>().Join((Expression<Func<OsZbModelFileInfo, OsZbBiaoModelFileInfo, bool>>)((OsZbModelFileInfo t1, OsZbBiaoModelFileInfo t0) => t0.RelateId == t1.Id), JoinEnum.INNER, new string[0]).Join((Expression<Func<SysFileInfo, OsZbBiaoModelFileInfo, bool>>)((SysFileInfo t1, OsZbBiaoModelFileInfo t0) => t0.RelateId == t1.RelatedId && t1.RelatedPage == "OS_ZB_MODEL_FILE_INFO"), JoinEnum.INNER, new string[0])
				.ExtraColumn((Expression<Func<OsZbModelFileInfo, object>>)((OsZbModelFileInfo t1) => t1.ModelText), (string)null, SqlFunctionEnum.None)
				.ExtraColumn((Expression<Func<OsZbModelFileInfo, object>>)((OsZbModelFileInfo t1) => t1.FileCode), (string)null, SqlFunctionEnum.None)
				.ExtraColumn((Expression<Func<OsZbModelFileInfo, object>>)((OsZbModelFileInfo t1) => t1.FileType), (string)null, SqlFunctionEnum.None)
				.Eq((Expression<Func<OsZbBiaoModelFileInfo, object>>)((OsZbBiaoModelFileInfo v) => v.MarkNo), WhereCondition.AND, (object)resInfo.MarkNo, SqlSymbol.None)
				.Eq((Expression<Func<OsZbBiaoModelFileInfo, object>>)((OsZbBiaoModelFileInfo v) => v.PackName), WhereCondition.AND, (object)resInfo.PackName, SqlSymbol.None)
				.Eq((Expression<Func<OsZbBiaoModelFileInfo, object>>)((OsZbBiaoModelFileInfo v) => v.ModelCode), WhereCondition.AND, (object)"FAST_GROW_REVIEW", SqlSymbol.None);
			IList<OsZbBiaoModelFileInfoVo> list3 = SearchOther<OsZbBiaoModelFileInfo, OsZbBiaoModelFileInfoVo>(sqlWrapper9);
			string value2 = File.ReadAllText(ConstantVo.Model_File_JSON_PATH);
			JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings();
			jsonSerializerSettings.ContractResolver = new IgnorePropertiesResolver(new string[1] { "OwnerInfo" });
			IList<Dictionary<string, object>> list4 = JsonConvert.DeserializeObject<IList<Dictionary<string, object>>>(value2, jsonSerializerSettings);
			IList<Dictionary<string, string>> list5 = new List<Dictionary<string, string>>();
			foreach (Dictionary<string, object> item3 in list4)
			{
				Dictionary<string, string> dictionary = new Dictionary<string, string>();
				foreach (string key in item3.Keys)
				{
					try
					{
						if (item3[key] == null)
						{
							dictionary[key] = item3[key]?.ToString();
						}
						else if (!item3[key].Equals(typeof(string)) && !item3[key].GetType().Equals(typeof(int?)) && !item3[key].GetType().Equals(typeof(int)))
						{
							dictionary[key] = item3[key]?.ToString();
						}
					}
					catch (Exception)
					{
						throw;
					}
				}
				list5.Add(dictionary);
			}
			Dictionary<string, ISet<string>> dictionary2 = new Dictionary<string, ISet<string>>();
			foreach (Dictionary<string, string> item4 in list5)
			{
				string text = item4["ResponseType"];
				if (!string.IsNullOrWhiteSpace(text))
				{
					if (!dictionary2.ContainsKey(text))
					{
						dictionary2[text] = new HashSet<string>();
					}
					dictionary2[text].Add(item4["FileCode"]);
				}
			}
			HashSet<string> value3 = new HashSet<string> { "是", "否" };
			HashSet<string> value4 = new HashSet<string> { "有", "无" };
			Dictionary<string, ISet<string>> dictionary3 = new Dictionary<string, ISet<string>>
			{
				{ "greenElectricity", value3 },
				{ "chinaQualityAward", value4 },
				{ "specialRecognition", value3 },
				{ "technologyCompanies", value3 }
			};
			foreach (KeyValuePair<string, ISet<string>> item5 in dictionary2)
			{
				string defaultValue = "/";
				StringBuilder stringBuilder2 = new StringBuilder();
				foreach (OsZbBiaoModelFileInfoVo item6 in list3)
				{
					if (item5.Value.Contains(item6.FileCode))
					{
						stringBuilder2.Append(item6.FileType + ",");
					}
				}
				if (dictionary3.ContainsKey(item5.Key))
				{
					jsonData[item5.Key] = ((stringBuilder2.Length > 0) ? dictionary3[item5.Key].First() : dictionary3[item5.Key].Last());
					continue;
				}
				jsonData[item5.Key] = StringUtil.FillWhiteSpace(stringBuilder2?.ToString()?.TrimEnd(','), defaultValue);
			}
			string value5 = ((IEnumerable<Dictionary<string, string>>)list5).Where((Func<Dictionary<string, string>, bool>)((Dictionary<string, string> v) => v.ContainsValue("recent_country_result"))).FirstOrDefault()?["FileType"];
			string value6 = ((IEnumerable<Dictionary<string, string>>)list5).Where((Func<Dictionary<string, string>, bool>)((Dictionary<string, string> v) => v.ContainsValue("recent_province_result"))).FirstOrDefault()?["FileType"];
			string value7 = ((IEnumerable<Dictionary<string, string>>)list5).Where((Func<Dictionary<string, string>, bool>)((Dictionary<string, string> v) => v.ContainsValue("recent_other_result"))).FirstOrDefault()?["FileType"];
			object obj = jsonData["innovationAchievements"];
			if (obj != null && obj.ToString()?.Contains(value5) == true)
			{
				jsonData["innovationAchievements"] = value5;
			}
			else
			{
				object obj2 = jsonData["innovationAchievements"];
				if (obj2 != null && obj2.ToString()?.Contains(value6) == true)
				{
					jsonData["innovationAchievements"] = value6;
				}
				else
				{
					jsonData["innovationAchievements"] = value7;
				}
			}
			string value8 = ((IEnumerable<Dictionary<string, string>>)list5).Where((Func<Dictionary<string, string>, bool>)((Dictionary<string, string> v) => v.ContainsValue("figure_factory_cert"))).FirstOrDefault()?["FileType"];
			string value9 = ((IEnumerable<Dictionary<string, string>>)list5).Where((Func<Dictionary<string, string>, bool>)((Dictionary<string, string> v) => v.ContainsValue("manufacture_cert"))).FirstOrDefault()?["FileType"];
			object obj3 = jsonData["enterpriseEvaluation"];
			if (obj3 != null && obj3.ToString()?.Contains(value8) == true)
			{
				jsonData["enterpriseEvaluation"] = value8;
				return;
			}
			object obj4 = jsonData["enterpriseEvaluation"];
			if (obj4 != null && obj4.ToString()?.Contains(value9) == true)
			{
				jsonData["enterpriseEvaluation"] = value9;
			}
			else
			{
				jsonData["enterpriseEvaluation"] = "其他情况";
			}
		}

		public void ExportToJsonFile(OsZbSupplierCommonResponseInfo resInfo, string saveRootPath)
		{
			SqlWrapper<OsZbSupplierCommonResponseInfo> wrapperThis = SqlWrapperFactory.Instance(resInfo).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.ProjectNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.Flag), WhereCondition.AND, (object)"shangwu", SqlSymbol.None);
			wrapperThis = wrapperThis.GroupBy(new Expression<Func<OsZbSupplierCommonResponseInfo, object>>[4]
			{
				(OsZbSupplierCommonResponseInfo v) => v.MarkNo,
				(OsZbSupplierCommonResponseInfo v) => v.PackName,
				(OsZbSupplierCommonResponseInfo v) => v.SupplierId,
				(OsZbSupplierCommonResponseInfo v) => v.SubPack
			});
			SqlWrapper<OsZbSupplierCommonResponseInfo> wrapperThis2 = SqlWrapperFactory.Instance(resInfo).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.ProjectNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierCommonResponseInfo, object>>)((OsZbSupplierCommonResponseInfo v) => v.Flag), WhereCondition.AND, (object)"jishu", SqlSymbol.None);
			wrapperThis2 = wrapperThis2.GroupBy(new Expression<Func<OsZbSupplierCommonResponseInfo, object>>[4]
			{
				(OsZbSupplierCommonResponseInfo v) => v.MarkNo,
				(OsZbSupplierCommonResponseInfo v) => v.PackName,
				(OsZbSupplierCommonResponseInfo v) => v.SupplierId,
				(OsZbSupplierCommonResponseInfo v) => v.SubPack
			});
			IList<OsZbSupplierCommonResponseInfo> list = Search(wrapperThis);
			if (list.Any())
			{
				IList<Dictionary<string, object>> list2 = new List<Dictionary<string, object>>(list.Count);
				foreach (OsZbSupplierCommonResponseInfo item2 in list)
				{
					Dictionary<string, object> item = JsonConvert.DeserializeObject<Dictionary<string, object>>(item2.JsonData);
					list2.Add(item);
				}
				string path = DateTime.Now.ToString("MMddHHmmss") + "_商务响应数据.json";
				File.WriteAllText(Path.Combine(saveRootPath, path), JsonConvert.SerializeObject(list2));
			}
			IList<OsZbSupplierCommonResponseInfo> list3 = Search(wrapperThis2);
			if (list3.Any())
			{
				IList<Dictionary<string, object>> list4 = new List<Dictionary<string, object>>(list.Count);
				foreach (OsZbSupplierCommonResponseInfo item3 in list3)
				{
					Dictionary<string, object> dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(item3.JsonData);
					dictionary["suppId"] = Md5Util.Md5(dictionary["suppName"].ToString());
					list4.Add(dictionary);
				}
				string path2 = DateTime.Now.ToString("MMddHHmmss") + "_技术响应数据.json";
				File.WriteAllText(Path.Combine(saveRootPath, path2), JsonConvert.SerializeObject(list4));
				string templateFilePath = LocalFileUtil.GetTemplateFilePath("summary\\技术响应表（物资）.xlsx");
				string path3 = DateTime.Now.ToString("MMddHHmmss") + "_技术响应数据.xlsx";
				Dictionary<string, object> dataSources = new Dictionary<string, object> { { "table", list4 } };
				AsposeExcel.TemplateToExcel(templateFilePath, Path.Combine(saveRootPath, path3), dataSources);
			}
			if (!list3.Any() && !list.Any())
			{
				throw new Exception("暂无数据");
			}
		}
	}
	public class OsZbBiaoTechExtraInfoRepository : BaseAbstractRepository<OsZbBiaoTechExtraInfo>, IOsZbBiaoTechExtraInfoRepository, IBaseRepository<OsZbBiaoTechExtraInfo>
	{
		public OsZbBiaoTechExtraInfoRepository()
		{
		}

		public OsZbBiaoTechExtraInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbBiaoTechParamGoodSettingRepository : BaseAbstractRepository<OsZbBiaoTechParamGoodSetting>, IOsZbBiaoTechParamGoodSettingRepository, IBaseRepository<OsZbBiaoTechParamGoodSetting>
	{
		public OsZbBiaoTechParamGoodSettingRepository()
		{
		}

		public OsZbBiaoTechParamGoodSettingRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbBiaoModelFileInfoRepository : BaseAbstractRepository<OsZbBiaoModelFileInfo>, IOsZbBiaoModelFileInfoRepository, IBaseRepository<OsZbBiaoModelFileInfo>
	{
		public OsZbBiaoModelFileInfoRepository()
		{
		}

		public OsZbBiaoModelFileInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbModelFileInfoRepository : BaseAbstractRepository<OsZbModelFileInfo>, IOsZbModelFileInfoRepository, IBaseRepository<OsZbModelFileInfo>
	{
		public OsZbModelFileInfoRepository()
		{
		}

		public OsZbModelFileInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsSupplierModelConfigRepository : BaseAbstractRepository<OsSupplierModelConfig>, IOsSupplierModelConfigRepository, IBaseRepository<OsSupplierModelConfig>
	{
		public OsSupplierModelConfigRepository()
		{
		}

		public OsSupplierModelConfigRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbBiaoPersonRelationsRepository : BaseAbstractRepository<OsZbBiaoPersonRelations>, IOsZbBiaoPersonRelationsRepository, IBaseRepository<OsZbBiaoPersonRelations>
	{
		public OsZbBiaoPersonRelationsRepository()
		{
		}

		public OsZbBiaoPersonRelationsRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbPersonRelationsRepository : BaseAbstractRepository<OsZbPersonRelations>, IOsZbPersonRelationsRepository, IBaseRepository<OsZbPersonRelations>
	{
		public OsZbPersonRelationsRepository()
		{
		}

		public OsZbPersonRelationsRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbBadBehaviorInfoRepository : BaseAbstractRepository<OsZbBadBehaviorInfo>, IOsZbBadBehaviorInfoRepository, IBaseRepository<OsZbBadBehaviorInfo>
	{
		public OsZbBadBehaviorInfoRepository()
		{
		}

		public OsZbBadBehaviorInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class BaseBillRepository : BaseAbstractRepository<OsZbBaseBillInfo>, IBaseBillRepository, IBaseRepository<OsZbBaseBillInfo>
	{
		public override BendPageInfo<OsZbBaseBillInfo> Search(BendPageInfo<OsZbBaseBillInfo> pageInfo, string selectColumnSql = null, string joinTableSql = null, Expression<Func<OsZbBaseBillInfo, object>>[] whereColums = null, string groupby = null, string whereSqlFragment = null)
		{
			whereColums = whereColums ?? new Expression<Func<OsZbBaseBillInfo, object>>[2]
			{
				(OsZbBaseBillInfo p) => p.SupplierId,
				(OsZbBaseBillInfo p) => p.Status
			};
			OsZbBaseBillInfo searchEntity = pageInfo.SearchEntity;
			string text = "'已上传数(' || (SELECT count(ID) from SYS_FILE_INFO where RELATED_ID = iif(BILL_CODE  is not null,BILL_CODE,'') || '_' || BILL_NUMBER AND RELATED_KEY = 'bill') || ')' BILL_ATTACH,";
			text += "'已上传数(' || (SELECT count(ID) from SYS_FILE_INFO where RELATED_ID = iif(BILL_CODE  is not null,BILL_CODE,'') || '_' || BILL_NUMBER AND RELATED_KEY = 'result') || ')' RESULT_ATTACH";
			text += ",(select iif(ID is null,'N','Y') from OS_ZB_BILL_INFO where OS_ZB_BILL_INFO.BILL_CODE =OS_ZB_BASE_BILL_INFO.BILL_CODE and OS_ZB_BILL_INFO.BILL_NUMBER=OS_ZB_BASE_BILL_INFO.BILL_NUMBER) IS_RELATED";
			bool groupById = string.IsNullOrEmpty(groupby);
			string text2 = SQLiteSqlUtils.CreateSelectSql2(searchEntity, text, null, groupById, null, whereColums);
			string text3 = SQLiteSqlUtils.CreateCountSql(searchEntity, new Expression<Func<OsZbBaseBillInfo, object>>[2]
			{
				(OsZbBaseBillInfo p) => p.SupplierId,
				(OsZbBaseBillInfo p) => p.Status
			});
			if (!string.IsNullOrWhiteSpace(searchEntity.BillCode))
			{
				text2 = text2 + " AND BILL_CODE like '%" + searchEntity.BillCode + "%' ";
				text3 = text3 + " AND BILL_CODE like '%" + searchEntity.BillCode + "%' ";
			}
			if (!string.IsNullOrWhiteSpace(searchEntity.BillNumber))
			{
				text2 = text2 + " AND BILL_NUMBER like '%" + searchEntity.BillNumber + "%' ";
				text3 = text3 + " AND BILL_NUMBER like '%" + searchEntity.BillNumber + "%' ";
			}
			if (!string.IsNullOrWhiteSpace(groupby))
			{
				text2 = text2 + " " + whereSqlFragment + " " + groupby + " ";
				text3 = text3 + " " + whereSqlFragment + " " + groupby + " ";
				text3 = "select count(0)  from ( " + text3 + " ) ";
			}
			text2 += " ORDER BY BILL_CODE,BILL_NUMBER";
			int num = ((pageInfo.CurPage - 1 > 0) ? (pageInfo.CurPage - 1) : 0);
			text2 += $" limit {num * pageInfo.PageSize},{pageInfo.PageSize} ";
			IList<OsZbBaseBillInfo> recordList = SQLiteLibrary.SelectBySql<BindingList<OsZbBaseBillInfo>, OsZbBaseBillInfo>(sqliteDbLocation, sqliteDbName, text2);
			int num2 = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, text3);
			int totalPage = num2 / pageInfo.PageSize + ((num2 % pageInfo.PageSize != 0) ? 1 : 0);
			return new BendPageInfo<OsZbBaseBillInfo>
			{
				CurPage = pageInfo.CurPage,
				PageSize = pageInfo.PageSize,
				TotalPage = totalPage,
				RecordList = recordList,
				TotalRecord = num2
			};
		}

		public BendPageInfo<OsZbBillInfo> SearchWithBill(BendPageInfo<OsZbBillInfo> pageInfo, string selectColumnSql = null, string joinTableSql = null, Expression<Func<OsZbBillInfo, object>>[] whereColums = null, string groupby = null)
		{
			whereColums = whereColums ?? new Expression<Func<OsZbBillInfo, object>>[0];
			OsZbBillInfo searchEntity = pageInfo.SearchEntity;
			string text = "'已上传数(' || (SELECT count(ID) from SYS_FILE_INFO where RELATED_ID = iif(BILL_CODE  is not null,BILL_CODE,'') || '_' || BILL_NUMBER AND RELATED_KEY = 'bill') || ')' BILL_ATTACH,";
			text += "'已上传数(' || (SELECT count(ID) from SYS_FILE_INFO where RELATED_ID = iif(BILL_CODE  is not null,BILL_CODE,'') || '_' || BILL_NUMBER AND RELATED_KEY = 'result') || ')' RESULT_ATTACH";
			text += ",(select iif(ID is null,'N','Y') from OS_ZB_BILL_INFO where OS_ZB_BILL_INFO.BILL_CODE =OS_ZB_BASE_BILL_INFO.BILL_CODE and OS_ZB_BILL_INFO.BILL_NUMBER=OS_ZB_BASE_BILL_INFO.BILL_NUMBER) IS_RELATED";
			bool groupById = string.IsNullOrEmpty(groupby);
			string text2 = SQLiteSqlUtils.CreateSelectSql2(searchEntity, text, null, groupById, null, whereColums);
			string text3 = SQLiteSqlUtils.CreateCountSql(searchEntity);
			if (!string.IsNullOrWhiteSpace(searchEntity.BillCode))
			{
				text2 = text2 + " AND BILL_CODE like '%" + searchEntity.BillCode + "%' ";
				text3 = text3 + " AND BILL_CODE like '%" + searchEntity.BillCode + "%' ";
			}
			if (!string.IsNullOrWhiteSpace(searchEntity.BillNumber))
			{
				text2 = text2 + " AND BILL_NUMBER like '%" + searchEntity.BillNumber + "%' ";
				text3 = text3 + " AND BILL_NUMBER like '%" + searchEntity.BillNumber + "%' ";
			}
			if (!string.IsNullOrWhiteSpace(groupby))
			{
				text2 = text2 + " " + groupby + " ";
				text3 = text3 + " " + groupby + " ";
				text3 = "select count(0)  from ( " + text3 + " ) ";
			}
			text2 += " ORDER BY BILL_CODE,BILL_NUMBER";
			int num = ((pageInfo.CurPage - 1 > 0) ? (pageInfo.CurPage - 1) : 0);
			text2 += $" limit {num * pageInfo.PageSize},{pageInfo.PageSize} ";
			IList<OsZbBillInfo> recordList = SQLiteLibrary.SelectBySql<BindingList<OsZbBillInfo>, OsZbBillInfo>(sqliteDbLocation, sqliteDbName, text2);
			int num2 = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, text3);
			int totalPage = num2 / pageInfo.PageSize + ((num2 % pageInfo.PageSize != 0) ? 1 : 0);
			return new BendPageInfo<OsZbBillInfo>
			{
				CurPage = pageInfo.CurPage,
				PageSize = pageInfo.PageSize,
				TotalPage = totalPage,
				RecordList = recordList,
				TotalRecord = num2
			};
		}
	}
	public class OsTechSpecifyBookRepository : BaseAbstractRepository<OsTechSpecifyBook>, IOsTechSpecifyBookRepository, IBaseRepository<OsTechSpecifyBook>
	{
		public OsTechSpecifyBookRepository()
		{
		}

		public OsTechSpecifyBookRepository(DbDataSource ds)
			: base(ds)
		{
		}

		public bool CreatePdfs(ref string selectedPath, IEnumerable<OsTechSpecifyBook> books, int printZoom, PageOrientationType printOrientation, out List<string> outFiles)
		{
			selectedPath = Path.Combine(selectedPath, "工程量清单pdf");
			Directory.CreateDirectory(selectedPath);
			outFiles = new List<string>();
			foreach (OsTechSpecifyBook book in books)
			{
				string fuJianPath = LocalFileUtil.GetFuJianPath(book.FilePath);
				string s = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
				using (MemoryStream memoryStream = new MemoryStream(Convert.FromBase64String(s)))
				{
					Aspose.Cells.License license = new Aspose.Cells.License();
					memoryStream.Seek(0L, SeekOrigin.Begin);
					license.SetLicense(memoryStream);
				}
				Workbook workbook = new Workbook(fuJianPath);
				int num = 0;
				foreach (Worksheet worksheet in workbook.Worksheets)
				{
					worksheet.PageSetup.PrintQuality = 360;
					worksheet.PageSetup.Zoom = printZoom;
					worksheet.PageSetup.Orientation = printOrientation;
				}
				string text = book.MarkName + "_" + book.PackName + "_" + PublicVo.EntName + "_" + book.TechSpecifyId;
				string text2 = Path.Combine(selectedPath, text + ".opdf");
				workbook.Save(text2, Aspose.Cells.SaveFormat.Pdf);
				using Document document = new Document(text2);
				OutlineItemCollection outlineItemCollection = new OutlineItemCollection(document.Outlines);
				outlineItemCollection.Title = book.MarkNo + "_" + book.PackName + "_" + book.TechSpecifyId;
				outlineItemCollection.Italic = false;
				outlineItemCollection.Bold = true;
				PageCollection pages = document.Pages;
				for (int num2 = pages.Count; num2 >= 1; num2--)
				{
					if (pages[num2].IsBlank(0.01))
					{
						pages.Remove(pages[num2]);
					}
				}
				document.Outlines.Add(outlineItemCollection);
				document.Save(Path.Combine(selectedPath, text + ".pdf"));
				File.Delete(text2);
				outFiles.Add(Path.Combine(selectedPath, text + ".pdf"));
			}
			return true;
		}

		public string ExportExcelFiles(OsTechSpecifyBook book, string selectedPath)
		{
			SqlWrapper<OsTechSpecifyBook> sqlWrapper = SqlWrapperFactory.Instance(book).Join((Expression<Func<SysFileInfo, OsTechSpecifyBook, bool>>)((SysFileInfo t1, OsTechSpecifyBook t2) => t1.RelatedPage == "@const0" && t1.RelatedId.Contains("@const1")), JoinEnum.LEFT, new string[2]
			{
				"OS_TECH_SPECIFY_BOOK",
				book.MarkName + "&&" + book.PackName + "&&" + book.TechSpecifyId
			}).ExtraColumn((Expression<Func<SysFileInfo, object>>)((SysFileInfo t) => t.BinaryFile), "BINARY_DATA", SqlFunctionEnum.None)
				.Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook b) => b.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None)
				.Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook b) => b.PackName), WhereCondition.AND, (object)null, SqlSymbol.None)
				.Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook b) => b.TechSpecifyId), WhereCondition.AND, (object)null, SqlSymbol.None);
			IList<OsTechSpecifyBook> list = Search(sqlWrapper);
			if (list.Count <= 0)
			{
				return "暂无数据";
			}
			string text = null;
			foreach (OsTechSpecifyBook item in list)
			{
				string realPath = item.MarkNo + item.PackName + "\\" + item.TechSpecifyId + "\\" + item.FileDisplayName;
				string path = item.MarkName + "_" + item.PackName + "_" + PublicVo.EntName + "_" + item.TechSpecifyId + ".xlsx";
				string fuJianPath = LocalFileUtil.GetFuJianPath(realPath);
				text = Path.Combine(selectedPath, path);
				if (File.Exists(fuJianPath))
				{
					File.Copy(fuJianPath, text, overwrite: true);
				}
				else if (item.BinaryData != null)
				{
					File.WriteAllBytes(text, item.BinaryData);
				}
			}
			return "导出成功,文件地址:" + ((list.Count == 1) ? text : selectedPath);
		}

		public bool ImportStandardPdfFiles(SqlWrapper<OsTechSpecifyBook> sqlWrapper, IEnumerable<string> fileNames, OsTechSpecifyBook curBook = null)
		{
			Expression<Func<OsTechSpecifyBook, object>>[] second = new Expression<Func<OsTechSpecifyBook, object>>[4]
			{
				(OsTechSpecifyBook p) => (object)p.EditAbleRowStart,
				(OsTechSpecifyBook p) => (object)p.EditAbleColumnStart,
				(OsTechSpecifyBook p) => (object)p.EditAbleRowEnd,
				(OsTechSpecifyBook p) => (object)p.EditAbleColumnEnd
			};
			if (sqlWrapper.Columns != null)
			{
				sqlWrapper.Columns = sqlWrapper.Columns.Concat(second).ToArray();
			}
			IList<OsTechSpecifyBook> source = Search(sqlWrapper);
			Dictionary<string, OsTechSpecifyBook> dictionary = source.ToDictionaryByLocal<OsTechSpecifyBook, string, OsTechSpecifyBook>((OsTechSpecifyBook p) => p.MarkNo + "_" + p.PackName + "_" + p.TechSpecifyId, (OsTechSpecifyBook p) => p);
			ISet<string> set = new HashSet<string>();
			ISet<string> set2 = new HashSet<string>();
			string valueByKey = ConfigHelper.GetValueByKey("verifySpecifyBookPdf");
			string valueByKey2 = ConfigHelper.GetValueByKey("verifySpecifyBookSysCreate");
			bool flag = !string.IsNullOrWhiteSpace(valueByKey) && Convert.ToBoolean(valueByKey);
			bool flag2 = !string.IsNullOrWhiteSpace(valueByKey2) && Convert.ToBoolean(valueByKey2);
			StringBuilder stringBuilder = new StringBuilder();
			foreach (string fileName in fileNames)
			{
				if (flag)
				{
					R r = UTCReadUtil.VerifyAdobeSignature(fileName);
					if (!r.Successful)
					{
						throw new Exception(r.ResultHint);
					}
				}
				if (curBook != null && !flag2)
				{
					dictionary[curBook.MarkNo + "_" + curBook.PackName + "_" + curBook.TechSpecifyId].PdfFilePath = fileName;
					set.Add(curBook.MarkNo);
					set2.Add(curBook.MarkNo + curBook.PackName);
					R r2 = VerifyExcel(curBook);
					if (!r2.Successful)
					{
						throw new Exception(r2.ResultHint);
					}
					continue;
				}
				using Document document = new Document(fileName);
				OutlineCollection outlines = document.Outlines;
				if (outlines.Count <= 0 || !dictionary.ContainsKey(outlines[1].Title))
				{
					throw new Exception("文件:" + Path.GetFileName(fileName) + ",非本工具生成");
				}
				dictionary[outlines[1].Title].PdfFilePath = fileName;
				set.Add(dictionary[outlines[1].Title].MarkNo);
				set2.Add(dictionary[outlines[1].Title].MarkNo + dictionary[outlines[1].Title].PackName);
				R r3 = VerifyExcel(dictionary[outlines[1].Title]);
				if (!r3.Successful)
				{
					throw new Exception(r3.ResultHint);
				}
			}
			var entity = new
			{
				curPage = 1,
				pageSize = 100,
				data = new
				{
					supplierName = PublicVo.EntName,
					flag = "uploadList",
					markNo = set.First(),
					fileType = "js"
				}
			};
			R r4 = WebRequestUtil.PostBasicApi("queryPageRecordList", entity, 12000, needDecrypt: false);
			if (!r4.Successful)
			{
				throw new Exception(r4.ResultHint);
			}
			BendPageInfo<ZbBidAttachRecord> bendPageInfo = JsonConvert.DeserializeObject<BendPageInfo<ZbBidAttachRecord>>(r4.ResultValue.ToString());
			if (bendPageInfo?.RecordList != null)
			{
				foreach (ZbBidAttachRecord record in bendPageInfo.RecordList)
				{
					if (set2.Contains(record.MarkNo + record.PackName))
					{
						throw new Exception("当前的投标文件已上传，不允许重新导入");
					}
				}
			}
			List<OsTechSpecifyBook> list = ((IEnumerable<KeyValuePair<string, OsTechSpecifyBook>>)dictionary).Select<KeyValuePair<string, OsTechSpecifyBook>, OsTechSpecifyBook>((Func<KeyValuePair<string, OsTechSpecifyBook>, OsTechSpecifyBook>)((KeyValuePair<string, OsTechSpecifyBook> p) => p.Value)).ToList();
			string relatedPage = "OS_TECH_SPECIFY_BOOK";
			List<SysFileInfo> list2 = new List<SysFileInfo>();
			string uploadTime = default(DateTime).ToString("yyyy-MM-dd HH:mm:ss");
			foreach (OsTechSpecifyBook item3 in list)
			{
				if (!string.IsNullOrEmpty(item3.PdfFilePath))
				{
					SysFileInfo sysFileInfo = new SysFileInfo();
					sysFileInfo.Id = Guid.NewGuid().ToString("N");
					sysFileInfo.RelatedPage = relatedPage;
					sysFileInfo.RelatedKey = "gclqc-excel";
					sysFileInfo.RelatedId = item3.MarkNo + "&&" + item3.PackName + "&&" + item3.TechSpecifyId;
					sysFileInfo.FileDispName = item3.MarkName + "_" + item3.PackName + "_" + PublicVo.SupplyName + "_" + item3.TechSpecifyId + ".xlsx";
					sysFileInfo.FileFormat = ".xlsx";
					sysFileInfo.BinaryFile = File.ReadAllBytes(LocalFileUtil.GetFuJianPath(item3.FilePath));
					sysFileInfo.UploadTime = uploadTime;
					SysFileInfo item = sysFileInfo;
					sysFileInfo = new SysFileInfo();
					sysFileInfo.Id = Guid.NewGuid().ToString("N");
					sysFileInfo.RelatedPage = relatedPage;
					sysFileInfo.RelatedKey = "gclqc-pdf";
					sysFileInfo.RelatedId = item3.MarkNo + "&&" + item3.PackName + "&&" + item3.TechSpecifyId;
					sysFileInfo.FileDispName = item3.MarkName + "_" + item3.PackName + "_" + PublicVo.SupplyName + "_" + item3.TechSpecifyId + ".pdf";
					sysFileInfo.FileFormat = ".pdf";
					sysFileInfo.BinaryFile = File.ReadAllBytes(item3.PdfFilePath);
					sysFileInfo.UploadTime = uploadTime;
					SysFileInfo item2 = sysFileInfo;
					list2.Add(item);
					list2.Add(item2);
					item3.PdfImportStatus = "是";
				}
			}
			ISysFileInfoRepository sysFileInfoRepository = new SysFileInfoRepository();
			sysFileInfoRepository.InsertAndStoreFile(list2, FileDelStrategy.RelatedKeyAndRelatedId);
			Expression<Func<OsTechSpecifyBook, object>>[] columns = new Expression<Func<OsTechSpecifyBook, object>>[1]
			{
				(OsTechSpecifyBook p) => p.PdfImportStatus
			};
			Expression<Func<OsTechSpecifyBook, object>>[] whereColums = new Expression<Func<OsTechSpecifyBook, object>>[1]
			{
				(OsTechSpecifyBook p) => p.Id
			};
			UpdateBatch(list, columns, whereColums);
			return true;
		}

		private static R VerifyExcel(OsTechSpecifyBook curBook)
		{
			R r = new R
			{
				Successful = true
			};
			string fuJianPath = LocalFileUtil.GetFuJianPath(curBook.FilePath);
			int? editAbleRowStart = curBook.EditAbleRowStart;
			int? editAbleColumnStart = curBook.EditAbleColumnStart;
			int? num = curBook.EditAbleRowEnd - 1;
			int? editAbleColumnEnd = curBook.EditAbleColumnEnd;
			using (Workbook workbook = new Workbook(fuJianPath))
			{
				WorksheetCollection worksheets = workbook.Worksheets;
				Worksheet worksheet = worksheets[0];
				Aspose.Cells.Cells cells = worksheet.Cells;
				RowCollection rows = cells.Rows;
				foreach (Aspose.Cells.Row item in rows)
				{
					if (item.Index >= editAbleRowStart && item.Index <= num)
					{
						Aspose.Cells.Cell firstCell = item.FirstCell;
						for (int i = editAbleColumnStart.Value; i <= editAbleColumnEnd.Value; i++)
						{
							Aspose.Cells.Cell cell = cells[item.Index, i];
							bool flag = false;
							if (string.IsNullOrWhiteSpace(cell?.DisplayStringValue) && cell.IsMerged)
							{
								object[,] array = (object[,])cell.GetMergedRange().Value;
								object[,] array2 = array;
								int upperBound = array2.GetUpperBound(0);
								int upperBound2 = array2.GetUpperBound(1);
								for (int j = array2.GetLowerBound(0); j <= upperBound; j++)
								{
									for (int k = array2.GetLowerBound(1); k <= upperBound2; k++)
									{
										if (!string.IsNullOrWhiteSpace(array2[j, k]?.ToString()))
										{
											flag = true;
										}
									}
								}
							}
							if (!flag && string.IsNullOrWhiteSpace(cell?.DisplayStringValue))
							{
								r.Successful = false;
								r.ResultHint = "文件:" + Path.GetFileName(fuJianPath) + ",未完整填写";
								break;
							}
						}
					}
					if (!r.Successful)
					{
						break;
					}
					if (r.Successful && item.Index == num + 1 && string.IsNullOrWhiteSpace(cells[item.Index, editAbleColumnEnd.Value]?.DisplayStringValue))
					{
						r.Successful = false;
						r.ResultHint = "文件:" + Path.GetFileName(fuJianPath) + ",未完整填写";
						break;
					}
				}
			}
			return r;
		}

		public void InsertAndStoreFile(List<OsTechSpecifyBook> fileList, bool deleteByRelatedId = true)
		{
			if (deleteByRelatedId)
			{
				Expression<Func<OsTechSpecifyBook, object>>[] whereColums = new Expression<Func<OsTechSpecifyBook, object>>[3]
				{
					(OsTechSpecifyBook p) => p.MarkNo,
					(OsTechSpecifyBook p) => p.PackName,
					(OsTechSpecifyBook p) => p.TechSpecifyId
				};
				string deleteSql = SQLiteSqlUtils.CreateDeleteSql(fileList, whereColums);
				SQLiteLibrary.DeleteBySql(sqliteDbLocation, sqliteDbName, deleteSql);
			}
			Dictionary<string, string> extraParams = new Dictionary<string, string> { { "binaryData", "BINARY_DATA" } };
			SQLiteLibrary.InsertByParams(fileList, extraParams, sqliteDbLocation, sqliteDbName);
		}

		public bool UploadFile(SysFileInfo fileInfoVo, OsTechSpecifyBook book, string fileNameTxtFlag = null, int fileMaxSize = 2046)
		{
			FileInfo fileInfo = new FileInfo(fileInfoVo.LocationPath);
			long length = fileInfo.Length;
			if (length < 500)
			{
				throw new Exception("文件过小，请先检查原文件");
			}
			if (fileMaxSize * 1024 * 1024 < length)
			{
				throw new Exception("超出导入大小限制，当前最大支持" + fileMaxSize + "M");
			}
			SysFileInfo sysFileInfo = new SysFileInfo
			{
				Id = Guid.NewGuid().ToString("N"),
				RelatedKey = fileInfoVo.RelatedKey,
				RelatedId = fileInfoVo.RelatedId
			};
			sysFileInfo.RelatedPage = fileInfoVo.RelatedPage;
			sysFileInfo.BinaryFile = File.ReadAllBytes(fileInfoVo.LocationPath);
			sysFileInfo.Size = length.ToString();
			sysFileInfo.FileFormat = fileInfo.Extension;
			if (sysFileInfo.FileDispName == null)
			{
				sysFileInfo.FileDispName = book.MarkName + "_" + book.PackName + "_" + PublicVo.EntName + (string.IsNullOrEmpty(fileNameTxtFlag) ? "" : ("_" + fileNameTxtFlag)) + sysFileInfo.FileFormat;
			}
			sysFileInfo.RealFileName = Path.GetFileName(fileInfoVo.LocationPath);
			sysFileInfo.UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
			SysFileInfoRepository sysFileInfoRepository = new SysFileInfoRepository();
			sysFileInfoRepository.InsertAndStoreFile(new List<SysFileInfo> { sysFileInfo }, FileDelStrategy.RelatedPageAndRelatedKey);
			return true;
		}

		public R MigrateExportData(OsTechSpecifyBook techSpecifyBook, string saveFile)
		{
			BidDataContext dbDataContext = DbDataContextFactory.GetDbDataContext<BidDataContext>();
			SqlWrapper<SysFileInfo> sqlWrapper = SqlWrapperFactory.Instance<SysFileInfo>().LikeRight((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedId), WhereCondition.AND, (object)(techSpecifyBook.MarkNo + "&&" + techSpecifyBook.PackName + "&&"), SqlSymbol.BracketLeft).LikeRight((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedKey), WhereCondition.OR, (object)(techSpecifyBook.MarkNo + "_" + techSpecifyBook.PackName + "_"), SqlSymbol.BracketRight)
				.Eq((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedPage), WhereCondition.AND, (object)"OS_TECH_SPECIFY_BOOK", SqlSymbol.None);
			int num = dbDataContext.SysFileRepository.SearchCount(sqlWrapper);
			if (num <= 0)
			{
				return new R
				{
					Successful = true,
					ResultHint = "迁出包数据失败，暂无数据"
				};
			}
			string text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, sqliteDbLocation, sqliteDbName);
			StringBuilder stringBuilder = new StringBuilder("ATTACH DATABASE '" + text + "' AS db1;\r\n");
			string selectSql = "SELECT sql FROM sqlite_master WHERE type='table' and name in('SYS_FILE_INFO') ORDER BY name;";
			DataRowCollection dataRowCollection = SQLiteLibrary.SelectDataBySql(sqliteDbLocation, sqliteDbName, selectSql)?.Rows;
			foreach (DataRow item in dataRowCollection)
			{
				stringBuilder.Append(item?.Field<string>("sql") + ";\r\n");
			}
			sqlWrapper.DbScheme = "db1";
			string value = sqlWrapper.BuildWithDataMigration(null);
			stringBuilder.AppendLine(value);
			stringBuilder.Append("DETACH DATABASE db1;");
			SQLiteLibrary.ExcuteSql(Path.GetDirectoryName(saveFile), Path.GetFileName(saveFile), stringBuilder.ToString());
			return new R
			{
				Successful = true,
				ResultHint = "已成功迁出包数据"
			};
		}

		public R MigrateImportData(OsTechSpecifyBook techSpecifyBook, string saveFile, Func<string, OsTechSpecifyBook, bool> checkExcelEdit = null)
		{
			BidDataContext dbDataContext = DbDataContextFactory.GetDbDataContext<BidDataContext>();
			MigrateDataContext migrateDataContext = new MigrateDataContext(Path.GetDirectoryName(saveFile), Path.GetFileName(saveFile));
			SqlWrapper<SysFileInfo> sqlWrapper = SqlWrapperFactory.Instance<SysFileInfo>().LikeRight((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedId), WhereCondition.AND, (object)(techSpecifyBook.MarkNo + "&&" + techSpecifyBook.PackName), SqlSymbol.BracketLeft).LikeRight((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedKey), WhereCondition.OR, (object)(techSpecifyBook.MarkNo + "_" + techSpecifyBook.PackName), SqlSymbol.BracketRight)
				.Eq((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedPage), WhereCondition.AND, (object)"OS_TECH_SPECIFY_BOOK", SqlSymbol.None);
			IList<SysFileInfo> source = migrateDataContext.SysFileRepository.Search(sqlWrapper);
			if (!source.Any())
			{
				return new R
				{
					Successful = true,
					ResultHint = "迁入包数据失败，暂无数据"
				};
			}
			IList<SysFileInfo> source2 = SearchOther<SysFileInfo, SysFileInfo>(sqlWrapper);
			if (source2.Any())
			{
				sqlWrapper.ExtraNotIn((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedId), (IEnumerable<object>)((IEnumerable<SysFileInfo>)source2).Select<SysFileInfo, string>((Func<SysFileInfo, string>)((SysFileInfo v) => v.RelatedId ?? "")), WhereCondition.AND, SqlSymbol.BracketLeft).ExtraNotIn((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedKey), (IEnumerable<object>)((IEnumerable<SysFileInfo>)source2).Select<SysFileInfo, string>((Func<SysFileInfo, string>)((SysFileInfo v) => v.RelatedKey ?? "")), WhereCondition.OR, SqlSymbol.BracketRight);
			}
			sqlWrapper.DbScheme = "db1";
			StringBuilder stringBuilder = new StringBuilder("ATTACH DATABASE '" + saveFile + "' AS db1;\r\n");
			string value = sqlWrapper.BuildWithDataMigration(null);
			stringBuilder.AppendLine(value);
			stringBuilder.Append("DETACH DATABASE db1;");
			SQLiteLibrary.ExcuteSql(sqliteDbLocation, sqliteDbName, stringBuilder.ToString());
			SqlWrapper<SysFileInfo> sqlWrapper2 = SqlWrapperFactory.Instance<SysFileInfo>().LikeRight((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedId), WhereCondition.AND, (object)(techSpecifyBook.MarkNo + "&&" + techSpecifyBook.PackName), SqlSymbol.None).Eq((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedKey), WhereCondition.AND, (object)"gclqc-excel", SqlSymbol.None)
				.Eq((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedPage), WhereCondition.AND, (object)"OS_TECH_SPECIFY_BOOK", SqlSymbol.None);
			IList<SysFileInfo> list = migrateDataContext.SysFileRepository.Search(sqlWrapper2);
			List<OsTechSpecifyBook> list2 = new List<OsTechSpecifyBook>();
			SqlWrapper<OsTechSpecifyBook> sqlWrapper3 = SqlWrapperFactory.Instance(new Expression<Func<OsTechSpecifyBook, object>>[11]
			{
				(OsTechSpecifyBook c) => c.ProjectNo,
				(OsTechSpecifyBook c) => c.MarkNo,
				(OsTechSpecifyBook c) => c.PackName,
				(OsTechSpecifyBook c) => c.SubPack,
				(OsTechSpecifyBook c) => c.ProjectTitle,
				(OsTechSpecifyBook c) => c.TechSpecifyId,
				(OsTechSpecifyBook c) => c.FileDisplayName,
				(OsTechSpecifyBook c) => (object)c.EditAbleRowStart,
				(OsTechSpecifyBook c) => (object)c.EditAbleColumnStart,
				(OsTechSpecifyBook c) => (object)c.EditAbleRowEnd,
				(OsTechSpecifyBook c) => (object)c.EditAbleColumnEnd
			}).Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook p) => p.MarkNo), WhereCondition.AND, (object)techSpecifyBook.MarkNo, SqlSymbol.None).Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook p) => p.PackName), WhereCondition.AND, (object)techSpecifyBook.PackName, SqlSymbol.None);
			Dictionary<string, OsTechSpecifyBook> dictionary = dbDataContext.TechSpecifyBookRepository.Search(sqlWrapper3).ToDictionaryByLocal<OsTechSpecifyBook, string, OsTechSpecifyBook>((OsTechSpecifyBook p) => p.MarkNo + p.PackName + p.TechSpecifyId, (OsTechSpecifyBook p) => p);
			foreach (SysFileInfo item in list)
			{
				string relatedId = item.RelatedId;
				string[] array = Regex.Split(relatedId, "&&");
				string text = array[0];
				string text2 = ((array.Length > 1) ? array[1] : "");
				string text3 = ((array.Length > 2) ? array[2] : "");
				string key = text + text2 + text3;
				string text4 = text + text2 + "\\" + text3 + "\\" + (dictionary.ContainsKey(key) ? dictionary[key].FileDisplayName : item.FileDispName);
				string fuJianPath = LocalFileUtil.GetFuJianPath(text4);
				byte[] binaryFile = item.BinaryFile;
				item.FilePath = text4;
				Directory.CreateDirectory(Path.GetDirectoryName(fuJianPath));
				File.WriteAllBytes(fuJianPath, binaryFile);
				OsTechSpecifyBook osTechSpecifyBook = new OsTechSpecifyBook();
				osTechSpecifyBook.MarkNo = text;
				osTechSpecifyBook.PackName = text2;
				osTechSpecifyBook.TechSpecifyId = text3;
				osTechSpecifyBook.FileDisplayName = item.FileDispName;
				osTechSpecifyBook.IsEdited = "是";
				if (dictionary.ContainsKey(key))
				{
					osTechSpecifyBook.FileDisplayName = dictionary[key].FileDisplayName;
					osTechSpecifyBook.EditAbleRowStart = dictionary[key].EditAbleRowStart;
					osTechSpecifyBook.EditAbleColumnStart = dictionary[key].EditAbleColumnStart;
					osTechSpecifyBook.EditAbleRowEnd = dictionary[key].EditAbleRowEnd;
					osTechSpecifyBook.EditAbleColumnEnd = dictionary[key].EditAbleColumnEnd;
					osTechSpecifyBook.IsEdited = ((checkExcelEdit == null) ? "是" : (checkExcelEdit.Invoke(fuJianPath, osTechSpecifyBook) ? "是" : "否"));
				}
				osTechSpecifyBook.ImportStatus = "是";
				osTechSpecifyBook.PdfImportStatus = "是";
				osTechSpecifyBook.IsStoreLocal = "是";
				list2.Add(osTechSpecifyBook);
			}
			Expression<Func<OsTechSpecifyBook, object>>[] columns = new Expression<Func<OsTechSpecifyBook, object>>[4]
			{
				(OsTechSpecifyBook p) => p.ImportStatus,
				(OsTechSpecifyBook p) => p.PdfImportStatus,
				(OsTechSpecifyBook p) => p.IsStoreLocal,
				(OsTechSpecifyBook p) => p.IsEdited
			};
			Expression<Func<OsTechSpecifyBook, object>>[] whereColums = new Expression<Func<OsTechSpecifyBook, object>>[3]
			{
				(OsTechSpecifyBook p) => p.MarkNo,
				(OsTechSpecifyBook p) => p.PackName,
				(OsTechSpecifyBook p) => p.TechSpecifyId
			};
			dbDataContext.TechSpecifyBookRepository.UpdateBatch(list2, columns, whereColums);
			return new R
			{
				Successful = true,
				ResultHint = "已成功迁入包数据"
			};
		}
	}
	public class OsZbBiaoCoreStaffRepository : BaseAbstractRepository<OsZbBiaoCoreStaff>, IOsZbBiaoCoreStaffRepository, IBaseRepository<OsZbBiaoCoreStaff>
	{
	}
	public class OsZbBillInfoRepository : BaseAbstractRepository<OsZbBillInfo>, IOsZbBillInfoRepository, IBaseRepository<OsZbBillInfo>
	{
		public OsZbBillInfoRepository()
		{
		}

		public OsZbBillInfoRepository(DbDataSource ds)
			: base(ds?.SqliteDbLocation, ds?.SqliteDbName)
		{
		}

		public bool ExportToExcelUsePage(OsZbBillInfo osZbBillInfo, string projectNo, string exportFolder)
		{
			string text = XmlHelper.GetXMlContentById("ViewBidMapper", "selectBills", (string str) => true).Replace("{projectNo}", projectNo);
			string path = "templates\\summary\\发票汇总表.xlsx";
			string text2 = Path.Combine(Environment.CurrentDirectory, path);
			int num = SQLiteLibrary.SelectCount("select COUNT(0)  FROM OS_ZB_BILL_INFO t0", sqliteDbLocation, sqliteDbName);
			int num2 = 60000;
			int num3 = num / num2 + ((num % num2 != 0) ? 1 : 0);
			Dictionary<string, object> dataSourceKV = new Dictionary<string, object>();
			for (int i = 0; i < num3; i++)
			{
				string savePath = Path.Combine(exportFolder, DateTime.Now.ToString("yyyyMMdd_") + $"发票汇总表_{i + 1}.xlsx");
				DataTable dataTable = SQLiteLibrary.SelectDataBySql(sqliteDbLocation, sqliteDbName, text.Replace("{offset}", (i * num2).ToString()).Replace("{pageSize}", num2.ToString()));
				dataTable.TableName = "billTb";
				LogsHelper.Debug("发票条目数:" + dataTable?.Rows.Count);
				if (dataTable != null && dataTable.Rows.Count > 0)
				{
					AsposeExcel.TemplateToExcel(text2, dataTable, dataSourceKV, savePath);
					dataTable.Clear();
				}
			}
			return true;
		}
	}
	public class OsZbCoreStaffRepository : BaseAbstractRepository<OsZbCoreStaff>, IOsZbCoreStaffRepository, IBaseRepository<OsZbCoreStaff>
	{
	}
	public class OsZbMaterialListRepository : BaseAbstractRepository<OsZbMaterialList>, IOsZbMaterialListRepository, IBaseRepository<OsZbMaterialList>
	{
		public OsZbMaterialListRepository()
		{
		}

		public OsZbMaterialListRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbPurchaseProjectInfoRepository : BaseAbstractRepository<OsZbPurchaseProjectInfo>, IOsZbPurchaseProjectInfoRepository, IBaseRepository<OsZbPurchaseProjectInfo>
	{
		public OsZbPurchaseProjectInfoRepository()
		{
		}

		public OsZbPurchaseProjectInfoRepository(DbDataSource ds)
			: base(ds?.SqliteDbLocation, ds?.SqliteDbName)
		{
		}

		public OsZbPurchaseProjectInfoRepository(string sqliteDbLocation, string sqliteDbName)
			: base(sqliteDbLocation, sqliteDbName)
		{
		}

		public bool ExportToExcelWithProjectList(string businessId)
		{
			string fileName = DateTime.Now.ToString("yyyyMMddhhmmss") + businessId + "包信息导入.xlsx";
			IList<OsZbPurchaseProjectInfo> dataSources = new List<OsZbPurchaseProjectInfo>
			{
				new OsZbPurchaseProjectInfo
				{
					ProjectNo = "122XXX",
					ProjectName = "国网安徽省XXXX2021年第一次XX采购",
					MarkNo = "122XXX-1502XX-00XX",
					MarkName = "变压XX装置",
					PackName = "包1",
					SubPack = "包1-1",
					ProjectTitle = "xx州xxxx工程",
					PurchaseType = (businessId.StartsWith("物资") ? "物资招标" : (businessId.StartsWith("服务") ? "服务招标" : null))
				}
			};
			IList<OsGoodsModulesList> dataSources2 = new List<OsGoodsModulesList>
			{
				new OsGoodsModulesList
				{
					ProjectNo = "122XXX",
					MarkNo = "122XXX-1502XX-00XX",
					MarkName = "变压XX装置",
					PackName = "包1",
					ComponentName = "组件xx"
				}
			};
			IList<ModelTagAttribute> modelTagAttributes = ReflectUtil.GetModelTagAttributes(businessId.StartsWith("物资") ? "物资" : "服务");
			Dictionary<string, ExportColumnInfo<OsZbPurchaseProjectInfo>> exportPropColumns = ExportColumnHelper.GetExportPropColumns<OsZbPurchaseProjectInfo>(businessId);
			int num = 1;
			foreach (ModelTagAttribute p2 in modelTagAttributes)
			{
				ExportColumnInfo<OsZbPurchaseProjectInfo> exportColumnInfo = new ExportColumnInfo<OsZbPurchaseProjectInfo>
				{
					OrderValue = ((p2.OrderValue > 0f) ? p2.OrderValue : ((float)(exportPropColumns.Count + num++))),
					FuncObj = (OsZbPurchaseProjectInfo t) => p2.DefaultValue
				};
				Dictionary<string, string> dictionary = new Dictionary<string, string>();
				if (p2.OPTION1 != null)
				{
					dictionary[p2.OPTION1] = p2.OPTION1;
				}
				if (p2.OPTION2 != null)
				{
					dictionary[p2.OPTION2] = p2.OPTION2;
				}
				if (p2.OPTION3 != null)
				{
					dictionary[p2.OPTION3] = p2.OPTION3;
				}
				if (p2.OPTION4 != null)
				{
					dictionary[p2.OPTION4] = p2.OPTION4;
				}
				if (p2.OPTION5 != null)
				{
					dictionary[p2.OPTION5] = p2.OPTION5;
				}
				exportColumnInfo.ComboBoxItemDict = dictionary;
				exportPropColumns[p2.ModelText] = exportColumnInfo;
			}
			exportPropColumns = exportPropColumns.OrderBy<KeyValuePair<string, ExportColumnInfo<OsZbPurchaseProjectInfo>>, float>((KeyValuePair<string, ExportColumnInfo<OsZbPurchaseProjectInfo>> p) => p.Value.OrderValue).ToDictionary<KeyValuePair<string, ExportColumnInfo<OsZbPurchaseProjectInfo>>, string, ExportColumnInfo<OsZbPurchaseProjectInfo>>((KeyValuePair<string, ExportColumnInfo<OsZbPurchaseProjectInfo>> p) => p.Key, (KeyValuePair<string, ExportColumnInfo<OsZbPurchaseProjectInfo>> p) => p.Value);
			Dictionary<string, ExportColumnInfo<OsGoodsModulesList>> exportPropColumns2 = ExportColumnHelper.GetExportPropColumns<OsGoodsModulesList>(null);
			AsposeExcelOption<OsZbPurchaseProjectInfo> excelOption = new AsposeExcelOption<OsZbPurchaseProjectInfo>(dataSources, null, exportPropColumns, null)
			{
				SheetIndex = 0
			};
			AsposeExcelOption<OsGoodsModulesList> excelOption2 = ((!businessId.StartsWith("物资")) ? null : new AsposeExcelOption<OsGoodsModulesList>(dataSources2, null, exportPropColumns2, null)
			{
				SheetIndex = 1
			});
			AsposeExcel.ExportTemplateToExcel(fileName, excelOption, excelOption2, delegate(Worksheet wk)
			{
				if (wk != null && wk.Index == 0)
				{
					int num2 = (businessId.StartsWith("物资") ? 6 : 7);
					wk.FreezePanes(1, num2, 1, num2);
					Range range = wk.Cells.CreateRange("A1:AE5");
					Style style = wk.Workbook.CreateStyle();
					style.Number = 49;
					range.ApplyStyle(style, new StyleFlag
					{
						NumberFormat = true
					});
					wk.Name = "包信息";
				}
				else if (wk != null && wk.Index == 1)
				{
					wk.Name = "货物组件";
				}
			});
			return true;
		}

		public bool ImportExcelWithProjectAndGoods()
		{
			Dictionary<string, string> map = new Dictionary<string, string>
			{
				{ "标段", "MARK" },
				{ "分包", "PACK" },
				{ "子分包", "SUB_PACK" },
				{ "是", "YES" },
				{ "否", "NO" }
			};
			Dictionary<string, string> excelDic = new Dictionary<string, string>();
			string insertSql = SQLiteSqlUtils.CreateAndDropTable<OsZbPurchaseProjectInfo>();
			SQLiteLibrary.InsertData(sqliteDbLocation, sqliteDbName, insertSql);
			OpenWorkBook openWorkBook = new OpenWorkBook();
			Workbook workbook = openWorkBook.OpenByDialog();
			if (workbook == null)
			{
				return false;
			}
			IList<ModelTagAttribute> modelTagAttributes = ReflectUtil.GetModelTagAttributes(null);
			Dictionary<string, ModelTagAttribute> subDict = modelTagAttributes.ToDictionaryByLocal<ModelTagAttribute, string, ModelTagAttribute>((ModelTagAttribute p) => p.ModelText, (ModelTagAttribute p) => p);
			IList<SysModelConfig> modelConfigList = new List<SysModelConfig>();
			WorksheetCollection worksheets = workbook.Worksheets;
			MessageInfo<OsZbPurchaseProjectInfo> messageInfo = ExcelReadUtil.GetmessageInfoV2<OsZbPurchaseProjectInfo>(worksheets, 0, 0, null, readAll: false, delegate(OsZbPurchaseProjectInfo t, int row)
			{
				t.BusinessFileWay = (map.ContainsKey(t.BusinessFileWay ?? "") ? map[t.BusinessFileWay] : t.BusinessFileWay);
				t.SkillFileWay = (map.ContainsKey(t.SkillFileWay ?? "") ? map[t.SkillFileWay] : t.SkillFileWay);
				t.BzjWd = (map.ContainsKey(t.BzjWd ?? "") ? map[t.BzjWd] : t.BzjWd);
				t.ProjectStatus = "01";
				t.SignUp = "YES";
				t.Selected = "0";
				t.NeedProduct = (map.ContainsKey(t.NeedProduct ?? "") ? map[t.NeedProduct] : "NO");
				t.NeedParts = (map.ContainsKey(t.NeedParts ?? "") ? map[t.NeedParts] : "NO");
				t.MarkValue = row.ToString();
				t.IsServiceFrame = (("服务框架协议" == t.PurchaseWay) ? "是" : "否");
				string key2 = t.MarkNo + t.PackName + t.SubPack;
				if (t.ModelConfigList != null)
				{
					Dictionary<string, ModelTagAttribute> dictionary3 = subDict;
					if (dictionary3 != null && dictionary3.Any())
					{
						foreach (SysModelConfig modelConfig in t.ModelConfigList)
						{
							if (!string.IsNullOrWhiteSpace(modelConfig.Value))
							{
								try
								{
									modelConfig.ModelCode = subDict[modelConfig.ModelText].ModelCode;
									modelConfig.ModelType = subDict[modelConfig.ModelText].ModelType;
								}
								catch (Exception)
								{
									throw;
								}
								modelConfigList.Add(modelConfig);
							}
						}
					}
				}
				if (excelDic.ContainsKey(key2))
				{
					return $"第{row}行，该项目信息在excel中重复";
				}
				excelDic.Add(key2, "");
				return (string)null;
			});
			MessageInfo<OsGoodsModulesList> messageInfo2 = ((worksheets.Count < 1) ? new MessageInfo<OsGoodsModulesList>() : ExcelReadUtil.GetmessageInfoV2<OsGoodsModulesList>(worksheets, 1, 0, null, readAll: false, delegate(OsGoodsModulesList t, int row)
			{
				string key = t.MarkNo + t.ComponentName;
				if (excelDic.ContainsKey(key))
				{
					return $"第{row}行,该货物组件清单在excel中重复";
				}
				excelDic.Add(key, "");
				return (string)null;
			}));
			if (messageInfo.ExistError)
			{
				string message = ((messageInfo.ErrorInfo.Length > 1024) ? (messageInfo.ErrorInfo.Substring(0, 1024) + "\r\n.......") : messageInfo.ErrorInfo);
				throw new Exception(message);
			}
			if (!messageInfo2.NoData && messageInfo2.ExistError)
			{
				string message2 = ((messageInfo2.ErrorInfo.Length > 1024) ? (messageInfo2.ErrorInfo.Substring(0, 1024) + "\r\n.......") : messageInfo2.ErrorInfo);
				throw new Exception(message2);
			}
			IList<OsZbPurchaseProjectInfo> record = messageInfo.Record;
			if (record != null && record.Any())
			{
				Regex regex = new Regex("[\\s]");
				Dictionary<string, string> dictionary = new Dictionary<string, string>();
				Dictionary<string, ISet<string>> dictionary2 = new Dictionary<string, ISet<string>>();
				foreach (OsZbPurchaseProjectInfo item5 in record)
				{
					item5.ProjectName = regex.Replace(item5.ProjectName, "");
					item5.MarkName = regex.Replace(item5.MarkName, "");
					item5.PackName = regex.Replace(item5.PackName, "");
					if (!string.IsNullOrWhiteSpace(item5.ProjectTitle))
					{
						item5.ProjectTitle = regex.Replace(item5.ProjectTitle, "");
					}
					if (RegexHelper.IsMath(item5.ProjectName, "[\\\\/:\\*\\?\"<>\\|]"))
					{
						throw new Exception("项目名称不能包含下列任何字符:/:*?\"<>|");
					}
					if (RegexHelper.IsMath(item5.ProjectNo, "[\\\\/:\\*\\?\"<>\\|]"))
					{
						throw new Exception("项目编号不能包含下列任何字符:/:*?\"<>|");
					}
					if (RegexHelper.IsMath(item5.MarkName, "[\\\\/:\\*\\?\"<>\\|]"))
					{
						throw new Exception("分标名称不能包含下列任何字符:/:*?\"<>|");
					}
					if (RegexHelper.IsMath(item5.MarkNo, "[\\\\/:\\*\\?\"<>\\|]"))
					{
						throw new Exception("分标编号不能包含下列任何字符:/:*?\"<>|");
					}
					if (RegexHelper.IsMath(item5.PackName, "[\\\\/:\\*\\?\"<>\\|]"))
					{
						throw new Exception("包号不能包含下列任何字符:/:*?\"<>|");
					}
					dictionary[item5.ProjectNo] = null;
					dictionary[item5.ProjectName] = null;
					if (!dictionary2.ContainsKey(item5.MarkNo))
					{
						dictionary2[item5.MarkNo] = new HashSet<string>();
					}
					dictionary2[item5.MarkNo].Add(item5.MarkName);
				}
				if (dictionary.Count > 2)
				{
					throw new Exception("存在多个不同的项目编号或多个不同的项目名称");
				}
				IEnumerable<string> enumerable = ((IEnumerable<KeyValuePair<string, ISet<string>>>)dictionary2).Where((Func<KeyValuePair<string, ISet<string>>, bool>)((KeyValuePair<string, ISet<string>> p) => p.Value.Count > 1)).Select<KeyValuePair<string, ISet<string>>, string>((Func<KeyValuePair<string, ISet<string>>, string>)((KeyValuePair<string, ISet<string>> p) => p.Key));
				if (enumerable != null && enumerable.Any())
				{
					throw new Exception("以下标段编号下对应多个不同的标段名称：" + string.Join(",", enumerable));
				}
				List<string> list = new List<string>();
				string item = "DELETE FROM OS_ZB_PURCHASE_PROJECT_INFO";
				string item2 = SQLiteSqlUtils.CreateInsertSql((List<OsZbPurchaseProjectInfo>)record);
				list.Add(item);
				list.Add(item2);
				if (modelConfigList.Count > 0)
				{
					Dictionary<string, string> markNameDict = record.ToDictionaryByLocal<OsZbPurchaseProjectInfo, string, string>((OsZbPurchaseProjectInfo p) => p.MarkNo, (OsZbPurchaseProjectInfo p) => p.MarkName);
					list.Add("DELETE FROM SYS_MODEL_CONFIG WHERE OWNER_MODEL IS NULL");
					list.Add(SQLiteSqlUtils.CreateInsertSql((List<SysModelConfig>)modelConfigList));
					Dictionary<string, string> modelConfigDict = ((IEnumerable<SysModelConfig>)modelConfigList).Where((Func<SysModelConfig, bool>)((SysModelConfig p) => p.ModelCode == "NEED_PRICE_FILE" && p.Value == "是")).ToDictionaryByLocal<SysModelConfig, string, string>((SysModelConfig p) => p.MarkNo + p.PackName + p.ProjectTitle, (SysModelConfig p) => (string)null);
					List<OsTechSpecifyBook> list2 = ((IEnumerable<SysModelConfig>)modelConfigList).Where((Func<SysModelConfig, bool>)((SysModelConfig p) => p.ModelCode == "TECH_SPECIFY_ID" && modelConfigDict.ContainsKey(p.MarkNo + p.PackName + p.ProjectTitle))).Select<SysModelConfig, OsTechSpecifyBook>((Func<SysModelConfig, OsTechSpecifyBook>)((SysModelConfig p) => new OsTechSpecifyBook
					{
						ProjectNo = p.ProjectNo,
						MarkNo = p.MarkNo,
						MarkName = markNameDict[p.MarkNo],
						PackName = p.PackName,
						ProjectTitle = p.ProjectTitle,
						SubPack = p.SubPack,
						TechSpecifyId = p.Value,
						ImportStatus = "否"
					})).ToList();
					list.Add("DELETE FROM OS_TECH_SPECIFY_BOOK");
					if (list2 != null && list2.Any())
					{
						SqlWrapper<OsTechSpecifyBook> sqlWrapper = SqlWrapperFactory.Instance<OsTechSpecifyBook>().Eq((Expression<Func<OsTechSpecifyBook, object>>)((OsTechSpecifyBook p) => p.ProjectNo), WhereCondition.AND, (object)record.FirstOrDefault().ProjectNo, SqlSymbol.None);
						list.Add(SQLiteSqlUtils.CreateInsertSql(list2));
					}
				}
				List<OsGoodsModulesList> record2 = messageInfo2.Record;
				if (record2 != null && record2.Any())
				{
					string item3 = "DELETE FROM OS_GOODS_MODULES_LIST";
					string item4 = SQLiteSqlUtils.CreateInsertSql(messageInfo2.Record);
					list.Add(item3);
					list.Add(item4);
				}
				SQLiteLibrary.ExecuteSqlByTransaction(sqliteDbLocation, sqliteDbName, list.ToArray());
			}
			return true;
		}

		public IList<OsZbPurchaseProjectInfo> SelectMarkAllPack(OsZbPurchaseProjectInfo query)
		{
			Expression<Func<OsZbPurchaseProjectInfo, object>>[] array = new Expression<Func<OsZbPurchaseProjectInfo, object>>[2]
			{
				(OsZbPurchaseProjectInfo p) => p.MarkNo,
				(OsZbPurchaseProjectInfo p) => p.PackName
			};
			SqlWrapper<OsZbPurchaseProjectInfo> sqlWrapper = SqlWrapperFactory.Instance(query).Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo p) => p.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo p) => p.PackName), WhereCondition.AND, (object)null, SqlSymbol.None);
			sqlWrapper.Eq((Expression<Func<OsZbPurchaseProjectInfo, object>>)((OsZbPurchaseProjectInfo p) => p.Selected), WhereCondition.AND, (object)"1", SqlSymbol.None);
			sqlWrapper.ExtraGroupBy(new Expression<Func<OsZbPurchaseProjectInfo, object>>[1]
			{
				(OsZbPurchaseProjectInfo p) => p.MarkNo
			}).ExtraGroupBy(new Expression<Func<OsZbPurchaseProjectInfo, object>>[1]
			{
				(OsZbPurchaseProjectInfo p) => p.PackName
			});
			return Search(sqlWrapper);
		}
	}
	public class OsZbSTechResponseInfoRepository : BaseAbstractRepository<OsZbSTechResponseInfo>, IOsZbSTechResponseInfoRepository, IBaseRepository<OsZbSTechResponseInfo>
	{
	}
	public class OsZbSupplierBiaoDurationResponseRepository : BaseAbstractRepository<OsZbSupplierBiaoDurationResponse>, IOsZbSupplierBiaoDurationResponseRepository, IBaseRepository<OsZbSupplierBiaoDurationResponse>
	{
	}
	public class OsZbSupplierCreditInfoRepository : BaseAbstractRepository<OsZbSupplierCreditInfo>, IOsZbSupplierCreditInfoRepository, IBaseRepository<OsZbSupplierCreditInfo>
	{
	}
	public class OsZbSupplierBiaoCreditInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoCreditInfo>, IOsZbSupplierBiaoCreditInfoRepository, IBaseRepository<OsZbSupplierBiaoCreditInfo>
	{
		public List<OsZbSupplierCreditInfo> SearchCompanyAwardsForBus(OsZbPurchaseProjectInfo currentProjectInfo, BidBookType bidBookType)
		{
			string attachType = ((bidBookType == BidBookType.BusinessBidBook) ? "project_awards_files_bus" : "project_awards_files_tech");
			SqlWrapper<OsZbSupplierBiaoCreditInfo> sqlWrapper = SqlWrapperFactory.Instance(new OsZbSupplierBiaoCreditInfo
			{
				MarkNo = currentProjectInfo.MarkNo,
				PackName = currentProjectInfo.PackName,
				SupplierId = PublicVo.SupplyId,
				AttachType = attachType
			});
			sqlWrapper.Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.PackName), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.AttachType), WhereCondition.AND, (object)null, SqlSymbol.None);
			IList<OsZbSupplierBiaoCreditInfo> source = Search(sqlWrapper);
			return ((IEnumerable<OsZbSupplierBiaoCreditInfo>)source).Select<OsZbSupplierBiaoCreditInfo, OsZbSupplierCreditInfo>((Func<OsZbSupplierBiaoCreditInfo, OsZbSupplierCreditInfo>)((OsZbSupplierBiaoCreditInfo p) => new OsZbSupplierCreditInfo
			{
				Id = p.Id,
				SupplierId = p.SupplierId,
				AttachType = p.AttachType,
				AttachId = p.AttachId,
				FileDispName = p.FileDispName,
				LocationPath = p.LocationPath,
				FileFormat = p.FileFormat,
				FilePath = p.FilePath,
				Remark = p.Remark
			})).ToList();
		}

		public List<OsZbSupplierBiaoCreditInfo> SearchCompanyAwardsForTech(OsZbPurchaseProjectInfo currentProjectInfo, BidBookType bidBookType)
		{
			string attachType = ((bidBookType == BidBookType.BusinessBidBook) ? "project_awards_files_bus" : "project_awards_files_tech");
			SqlWrapper<OsZbSupplierBiaoCreditInfo> sqlWrapper = SqlWrapperFactory.Instance(new OsZbSupplierBiaoCreditInfo
			{
				MarkNo = currentProjectInfo.MarkNo,
				PackName = currentProjectInfo.PackName,
				SupplierId = PublicVo.SupplyId,
				AttachType = attachType
			});
			sqlWrapper.Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.PackName), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBiaoCreditInfo, object>>)((OsZbSupplierBiaoCreditInfo p) => p.AttachType), WhereCondition.AND, (object)null, SqlSymbol.None);
			IList<OsZbSupplierBiaoCreditInfo> source = Search(sqlWrapper);
			return source.ToList();
		}
	}
	public class OsZbSupplierBiaoLabeqpInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoLabeqpInfo>, IOsZbSupplierBiaoLabeqpInfoRepository, IBaseRepository<OsZbSupplierBiaoLabeqpInfo>
	{
	}
	public class OsZbSupplierBiaoMeqpInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoMeqpInfo>, IOsZbSupplierBiaoMeqpInfoRepository, IBaseRepository<OsZbSupplierBiaoMeqpInfo>
	{
	}
	public class OsZbSupplierBiaoReportInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoReportInfo>, IOsZbSupplierBiaoReportInfoRepository, IBaseRepository<OsZbSupplierBiaoReportInfo>
	{
	}
	public class OsZbSupplierBiaoSalesInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoSalesInfo>, IOsZbSupplierBiaoSalesInfoRepository, IBaseRepository<OsZbSupplierBiaoSalesInfo>
	{
		public OsZbSupplierBiaoSalesInfoRepository()
		{
		}

		public OsZbSupplierBiaoSalesInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}

		public bool ExportToExcelUsePage(OsZbSupplierBiaoSalesInfo sale, string selectedPath)
		{
			string text = XmlHelper.GetXMlContentById("ViewBidMapper", "selectSales", (string str) => true).Replace("{projectNo}", sale.ProjectNo);
			string path = "templates\\summary\\业绩汇总表.xlsx";
			string text2 = Path.Combine(Environment.CurrentDirectory, path);
			SqlWrapper<OsZbSupplierBiaoSalesInfo> sqlWrapper = SqlWrapperFactory.Instance<OsZbSupplierBiaoSalesInfo>().Eq((Expression<Func<OsZbSupplierBiaoSalesInfo, object>>)((OsZbSupplierBiaoSalesInfo p) => p.ProjectNo), WhereCondition.AND, (object)sale.ProjectNo, SqlSymbol.None);
			int num = SearchCount(sqlWrapper);
			int num2 = 70000;
			int num3 = num / num2 + ((num % num2 != 0) ? 1 : 0);
			Dictionary<string, object> dataSourceKV = new Dictionary<string, object>();
			for (int i = 0; i < num3; i++)
			{
				string savePath = Path.Combine(selectedPath, DateTime.Now.ToString("yyyyMMdd_") + $"业绩信息{i + 1}.xlsx");
				DataTable dataTable = SQLiteLibrary.SelectDataBySql(sqliteDbLocation, sqliteDbName, text.Replace("{offset}", (i * num2).ToString()).Replace("{pageSize}", num2.ToString()));
				dataTable.TableName = "saleTb";
				LogsHelper.Debug("【业绩信息】条目数:" + dataTable?.Rows.Count);
				if (dataTable != null && dataTable.Rows.Count > 0)
				{
					AsposeExcel.TemplateToExcel(text2, dataTable, dataSourceKV, savePath);
				}
			}
			return true;
		}
	}
	public class OsZbSupplierBidAttachmentRepository : BaseAbstractRepository<OsZbSupplierBidAttachment>, IOsZbSupplierBidAttachmentRepository, IBaseRepository<OsZbSupplierBidAttachment>
	{
		public void CheckAttachIsUpload(string markNo, string packName, string supplierId)
		{
			OsZbSupplierBidAttachment query = new OsZbSupplierBidAttachment
			{
				IsDelete = "1",
				MarkNo = markNo,
				PackName = packName,
				SupplierId = supplierId
			};
			SqlWrapper<OsZbSupplierBidAttachment> sqlWrapper = SqlWrapperFactory.Instance(query);
			sqlWrapper.Eq((Expression<Func<OsZbSupplierBidAttachment, object>>)((OsZbSupplierBidAttachment p) => p.IsDelete), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBidAttachment, object>>)((OsZbSupplierBidAttachment p) => p.MarkNo), WhereCondition.AND, (object)null, SqlSymbol.None).Eq((Expression<Func<OsZbSupplierBidAttachment, object>>)((OsZbSupplierBidAttachment p) => p.PackName), WhereCondition.AND, (object)null, SqlSymbol.None)
				.Eq((Expression<Func<OsZbSupplierBidAttachment, object>>)((OsZbSupplierBidAttachment p) => p.SupplierId), WhereCondition.AND, (object)null, SqlSymbol.None);
			int num = SearchCount(sqlWrapper);
			if (num > 0)
			{
				throw new Exception("标书文件已上传数据不得编辑相关数据");
			}
		}
	}
	public class OsZbSupplierBiaoEquityInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoEquityInfo>, IOsZbSupplierBiaoEquityInfoRepository, IBaseRepository<OsZbSupplierBiaoEquityInfo>
	{
		public OsZbSupplierBiaoEquityInfoRepository()
		{
		}

		public OsZbSupplierBiaoEquityInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbSupplierDepositDetailRepository : BaseRepository<OsZbSupplierDepositDetail>, IOsZbSupplierDepositDetailRepository, IBaseRepository<OsZbSupplierDepositDetail>
	{
	}
	public class OsZbSupplierDepositRepository : BaseRepository<OsZbSupplierDeposit>, IOsZbSupplierDepositRepository, IBaseRepository<OsZbSupplierDeposit>
	{
	}
	public class OsZbSupplierEquityInfoRepository : BaseRepository<OsZbSupplierEquityInfo>, IOsZbSupplierEquityInfoRepository, IBaseRepository<OsZbSupplierEquityInfo>
	{
	}
	public class OsZbSupplierFinanceInfoRepository : BaseAbstractRepository<OsZbSupplierFinanceInfo>, IOsZbSupplierFinanceInfoRepository, IBaseRepository<OsZbSupplierFinanceInfo>
	{
		public OsZbSupplierFinanceInfoRepository()
		{
		}

		public OsZbSupplierFinanceInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbSupplierLabeqpInfoRepository : BaseAbstractRepository<OsZbSupplierLabeqpInfo>, IOsZbSupplierLabeqpInfoRepository, IBaseRepository<OsZbSupplierLabeqpInfo>
	{
	}
	public class OsZbSupplierMeqpInfoRepository : BaseAbstractRepository<OsZbSupplierMeqpInfo>, IOsZbSupplierMeqpInfoRepository, IBaseRepository<OsZbSupplierMeqpInfo>
	{
	}
	public class OsZbSupplierProductInfoRepository : BaseAbstractRepository<OsZbSupplierProductInfo>, IOsZbSupplierProductInfoRepository, IBaseRepository<OsZbSupplierProductInfo>
	{
		public OsZbSupplierProductInfoRepository()
		{
		}

		public OsZbSupplierProductInfoRepository(DbDataSource ds)
			: base(ds?.SqliteDbLocation, ds?.SqliteDbName)
		{
		}

		public bool ExportToExcelUsePage(OsZbSupplierProductInfo product, string exportFolder)
		{
			string text = XmlHelper.GetXMlContentById("ViewBidMapper", "selectOnGoods", (string str) => true).Replace("{projectNo}", product.ProjectNo);
			string path = "templates\\summary\\上架商品信息汇总.xlsx";
			string text2 = Path.Combine(Environment.CurrentDirectory, path);
			SqlWrapper<OsZbSupplierProductInfo> sqlWrapper = SqlWrapperFactory.Instance<OsZbSupplierProductInfo>().Eq((Expression<Func<OsZbSupplierProductInfo, object>>)((OsZbSupplierProductInfo p) => p.ProjectNo), WhereCondition.AND, (object)product.ProjectNo, SqlSymbol.None);
			int num = SearchCount(sqlWrapper);
			int num2 = 60000;
			int num3 = num / num2 + ((num % num2 != 0) ? 1 : 0);
			Dictionary<string, object> dataSourceKV = new Dictionary<string, object>();
			for (int i = 0; i < num3; i++)
			{
				string savePath = Path.Combine(exportFolder, DateTime.Now.ToString("yyyyMMdd_") + $"上架商品信息_{i + 1}.xlsx");
				DataTable dataTable = SQLiteLibrary.SelectDataBySql(sqliteDbLocation, sqliteDbName, text.Replace("{offset}", (i * num2).ToString()).Replace("{pageSize}", num2.ToString()));
				dataTable.TableName = "sjsp";
				LogsHelper.Debug("上架商品/产品 条目数:" + dataTable?.Rows.Count);
				if (dataTable != null && dataTable.Rows.Count > 0)
				{
					AsposeExcel.TemplateToExcel(text2, dataTable, dataSourceKV, savePath);
				}
			}
			return true;
		}
	}
	public class OsZbSupplierReportInfoRepository : BaseAbstractRepository<OsZbSupplierReportInfo>, IOsZbSupplierReportInfoRepository, IBaseRepository<OsZbSupplierReportInfo>
	{
	}
	public class OsZbSupplierSalesInfoRepository : BaseAbstractRepository<OsZbSupplierSalesInfo>, IOsZbSupplierSalesInfoRepository, IBaseRepository<OsZbSupplierSalesInfo>
	{
	}
	public class SysExportRecordRepository : BaseRepository<SysExportRecord>, ISysExportRecordRepository, IBaseRepository<SysExportRecord>
	{
	}
	public class SysFileInfoRepository : BaseAbstractRepository<SysFileInfo>, ISysFileInfoRepository, IBaseRepository<SysFileInfo>
	{
		public SysFileInfoRepository()
		{
		}

		public SysFileInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}

		public void InsertAndStoreFile(List<SysFileInfo> fileList, FileDelStrategy fileDelStrategy = FileDelStrategy.RelatedKeyAndRelatedIdAndHashCode)
		{
			Expression<Func<SysFileInfo, object>>[] array = fileDelStrategy switch
			{
				FileDelStrategy.None => null, 
				FileDelStrategy.ID => new Expression<Func<SysFileInfo, object>>[1]
				{
					(SysFileInfo p) => p.Id
				}, 
				FileDelStrategy.RelatedPage => new Expression<Func<SysFileInfo, object>>[1]
				{
					(SysFileInfo p) => p.RelatedPage
				}, 
				FileDelStrategy.RelatedKey => new Expression<Func<SysFileInfo, object>>[1]
				{
					(SysFileInfo p) => p.RelatedKey
				}, 
				FileDelStrategy.RelatedId => new Expression<Func<SysFileInfo, object>>[1]
				{
					(SysFileInfo p) => p.RelatedId
				}, 
				FileDelStrategy.HashCode => new Expression<Func<SysFileInfo, object>>[1]
				{
					(SysFileInfo p) => p.HashCode
				}, 
				FileDelStrategy.RelatedKeyAndRelatedId => new Expression<Func<SysFileInfo, object>>[2]
				{
					(SysFileInfo p) => p.RelatedKey,
					(SysFileInfo p) => p.RelatedId
				}, 
				(FileDelStrategy)20 => new Expression<Func<SysFileInfo, object>>[2]
				{
					(SysFileInfo p) => p.RelatedKey,
					(SysFileInfo p) => p.HashCode
				}, 
				FileDelStrategy.RelatedKeyAndRelatedIdAndHashCode => new Expression<Func<SysFileInfo, object>>[3]
				{
					(SysFileInfo p) => p.RelatedKey,
					(SysFileInfo p) => p.RelatedId,
					(SysFileInfo p) => p.HashCode
				}, 
				FileDelStrategy.RelatedPageAndRelatedKeyAndRelatedId => new Expression<Func<SysFileInfo, object>>[3]
				{
					(SysFileInfo p) => p.RelatedPage,
					(SysFileInfo p) => p.RelatedKey,
					(SysFileInfo p) => p.RelatedId
				}, 
				FileDelStrategy.RelatedPageAndRelatedKey => new Expression<Func<SysFileInfo, object>>[2]
				{
					(SysFileInfo p) => p.RelatedPage,
					(SysFileInfo p) => p.RelatedKey
				}, 
				_ => new Expression<Func<SysFileInfo, object>>[2]
				{
					(SysFileInfo p) => p.RelatedKey,
					(SysFileInfo p) => p.HashCode
				}, 
			};
			if (array != null)
			{
				string deleteSql = SQLiteSqlUtils.CreateDeleteSql(fileList, array);
				SQLiteLibrary.DeleteBySql(sqliteDbLocation, sqliteDbName, deleteSql);
			}
			Dictionary<string, string> extraParams = new Dictionary<string, string> { { "binaryFile", "BINARY_FILE" } };
			SQLiteLibrary.InsertByParams(fileList, extraParams);
		}

		public IList<SysFileInfo> SearchBillCountInfo(List<string> parentIds, bool queryBinaryFile = false)
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (string parentId in parentIds)
			{
				stringBuilder.Append(parentId.StartsWith("'") ? (parentId + ",") : ("'" + parentId + "',"));
			}
			StringBuilder stringBuilder2 = new StringBuilder();
			stringBuilder2.AppendLine("SELECT t1.PARENT_ID as RELATED_ID,'已上传数(' || count(t0.ID) || ')' as uploadCount,'bill' as RELATED_KEY ");
			if (queryBinaryFile)
			{
				stringBuilder2.Append(",t0.BINARY_FILE");
			}
			stringBuilder2.AppendLine(" FROM SYS_FILE_INFO t0 ");
			stringBuilder2.AppendLine(" INNER JOIN OS_ZB_BILL_INFO t1 on iif(t1.BILL_CODE is not null, t1.BILL_CODE, '') || '_' || t1.BILL_NUMBER = t0.RELATED_ID ");
			stringBuilder2.AppendLine(" WHERE t1.PARENT_ID IN(" + stringBuilder.ToString().TrimEnd(',') + ") GROUP BY t1.PARENT_ID");
			return SQLiteLibrary.SelectBySql<SysFileInfo>(sqliteDbLocation, sqliteDbName, stringBuilder2.ToString());
		}

		public IList<SysFileInfo> SearchCountByRelatedPageAndIds(string currentRelatedPage, List<string> relatedIds, bool queryBinaryFile = false)
		{
			SysFileInfo t = new SysFileInfo
			{
				RelatedPage = currentRelatedPage
			};
			List<Expression<Func<SysFileInfo, object>>> list = new List<Expression<Func<SysFileInfo, object>>>();
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.Id));
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedPage));
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedKey));
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.FileDispName));
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.LocationPath));
			list.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.RelatedId));
			List<Expression<Func<SysFileInfo, object>>> list2 = list;
			if (queryBinaryFile)
			{
				list2.Add((Expression<Func<SysFileInfo, object>>)((SysFileInfo p) => p.BinaryFile));
			}
			StringBuilder stringBuilder = new StringBuilder();
			foreach (string relatedId in relatedIds)
			{
				if (relatedId.StartsWith("'"))
				{
					stringBuilder.Append(relatedId + ",");
				}
				else
				{
					stringBuilder.Append("'" + relatedId + "',");
				}
			}
			Expression<Func<SysFileInfo, object>>[] whereColums = new Expression<Func<SysFileInfo, object>>[1]
			{
				(SysFileInfo p) => p.RelatedPage
			};
			string text = SQLiteSqlUtils.CreateSelectSql2(t, " '已上传数('||COUNT(ID)||')' as uploadCount", null, groupById: false, list2.ToArray(), whereColums);
			text = text + " AND RELATED_ID IN (" + stringBuilder.ToString().TrimEnd(',') + ") GROUP BY RELATED_ID,RELATED_KEY";
			Console.Write("fileSql:\r\n" + text);
			return SQLiteLibrary.SelectBySql<SysFileInfo>(sqliteDbLocation, sqliteDbName, text);
		}

		public IList<SysFileInfo> SearchListWithMakeBidBook(OsZbPurchaseProjectInfo project, SysFileInfo query)
		{
			Expression<Func<OsZbPurchaseProjectInfo, object>>[] columns = new Expression<Func<OsZbPurchaseProjectInfo, object>>[8]
			{
				(OsZbPurchaseProjectInfo p) => p.Id,
				(OsZbPurchaseProjectInfo p) => p.ProjectNo,
				(OsZbPurchaseProjectInfo p) => p.ProjectName,
				(OsZbPurchaseProjectInfo p) => p.MarkNo,
				(OsZbPurchaseProjectInfo p) => p.MarkName,
				(OsZbPurchaseProjectInfo p) => p.PackName,
				(OsZbPurchaseProjectInfo p) => p.SubPack,
				(OsZbPurchaseProjectInfo p) => p.ProjectTitle
			};
			Expression<Func<OsZbPurchaseProjectInfo, object>>[] whereColums = new Expression<Func<OsZbPurchaseProjectInfo, object>>[3]
			{
				(OsZbPurchaseProjectInfo p) => p.MarkNo,
				(OsZbPurchaseProjectInfo p) => p.PackName,
				(OsZbPurchaseProjectInfo p) => p.Selected
			};
			string text = SQLiteSqlUtils.CreateSelectSql2(project, null, null, groupById: false, columns, whereColums);
			text += " group by MARK_NO,PACK_NAME";
			List<OsZbPurchaseProjectInfo> list = SQLiteLibrary.SelectBySql<OsZbPurchaseProjectInfo>(sqliteDbLocation, sqliteDbName, text);
			string selectColumnSql = " '已上传数(' || count(0) || ')' as uploadCount,null as ProjectTitle ";
			string text2 = string.Join(",", ((IEnumerable<OsZbPurchaseProjectInfo>)list).Select<OsZbPurchaseProjectInfo, string>((Func<OsZbPurchaseProjectInfo, string>)((OsZbPurchaseProjectInfo p) => "'" + p.ProjectNo + "&&" + p.MarkNo + "&&" + p.PackName + "'")));
			Expression<Func<SysFileInfo, object>>[] columns2 = new Expression<Func<SysFileInfo, object>>[6]
			{
				(SysFileInfo p) => p.Id,
				(SysFileInfo p) => p.RelatedPage,
				(SysFileInfo p) => p.RelatedKey,
				(SysFileInfo p) => p.FileDispName,
				(SysFileInfo p) => p.LocationPath,
				(SysFileInfo p) => p.RelatedId
			};
			Expression<Func<SysFileInfo, object>>[] whereColums2 = new Expression<Func<SysFileInfo, object>>[1]
			{
				(SysFileInfo p) => p.RelatedPage
			};
			string text3 = SQLiteSqlUtils.CreateSelectSql2(query, selectColumnSql, null, groupById: false, columns2, whereColums2);
			text3 = text3 + " AND RELATED_ID IN (" + text2 + ") GROUP BY RELATED_PAGE,RELATED_ID ";
			IList<SysFileInfo> list2 = SQLiteLibrary.SelectBySql<SysFileInfo>(sqliteDbLocation, sqliteDbName, text3);
			Dictionary<string, SysFileInfo> dictionary = list2.ToDictionaryByLocal<SysFileInfo, string, SysFileInfo>((SysFileInfo p) => p.RelatedId ?? "-", (SysFileInfo p) => p);
			foreach (OsZbPurchaseProjectInfo item in list)
			{
				string key = item.ProjectNo + "&&" + item.MarkNo + "&&" + item.PackName;
				if (!dictionary.ContainsKey(key))
				{
					list2.Add(new SysFileInfo
					{
						RelatedPage = query.RelatedPage,
						RelatedKey = item.ProjectNo + "&&" + item.MarkNo + "&&" + item.PackName,
						RelatedId = item.ProjectNo + "&&" + item.MarkNo + "&&" + item.PackName,
						UploadCount = "已上传数(0)",
						ProjectTitle = item.PackName
					});
				}
				else
				{
					dictionary[key].ProjectTitle = item.PackName;
				}
			}
			return list2.OrderBy<SysFileInfo, int>((SysFileInfo p) => (p.ProjectTitle != null) ? int.Parse(p.ProjectTitle.Replace("包", "")) : 0).ToList();
		}
	}
	public class SysModelConfigRepository : BaseAbstractRepository<SysModelConfig>, ISysModelConfigRepository, IBaseRepository<SysModelConfig>
	{
		public SysModelConfigRepository()
		{
		}

		public SysModelConfigRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class SysModuleLableRepository : BaseAbstractRepository<SysModuleLable>, ISysModuleLableRepository, IBaseRepository<SysModuleLable>
	{
		public SysModuleLableRepository()
		{
		}

		public SysModuleLableRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class TStatementInfoRepository : BaseAbstractRepository<TStatementInfo>, ITStatementInfoRepository, IBaseRepository<TStatementInfo>
	{
		public TStatementInfoRepository(string dbLocation, string dbName)
			: base(dbLocation, dbName)
		{
		}
	}
	public class OsZbSupplierBiaoAuthPersonInfoRepository : BaseAbstractRepository<OsZbSupplierBiaoAuthPersonInfo>, IOsZbSupplierBiaoAuthPersonInfoRepository, IBaseRepository<OsZbSupplierBiaoAuthPersonInfo>
	{
		public OsZbSupplierBiaoAuthPersonInfoRepository()
		{
		}

		public OsZbSupplierBiaoAuthPersonInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
	public class OsZbSupplierInfoRepository : BaseAbstractRepository<OsZbSupplierInfo>, IOsZbSupplierInfoRepository, IBaseRepository<OsZbSupplierInfo>
	{
		public OsZbSupplierInfoRepository()
		{
		}

		public OsZbSupplierInfoRepository(DbDataSource ds)
			: base(ds)
		{
		}
	}
}
namespace MyCommon.Repository.ibase
{
	public interface IOsModelFileOwerInfoRepository : IBaseRepository<OsModelFileOwerInfo>
	{
	}
	public interface IOsSupplierModelConfigRepository : IBaseRepository<OsSupplierModelConfig>
	{
	}
	public interface IOsZbBadBehaviorInfoRepository : IBaseRepository<OsZbBadBehaviorInfo>
	{
	}
	public interface IOsZbBiaoModelFileInfoRepository : IBaseRepository<OsZbBiaoModelFileInfo>
	{
	}
	public interface IOsZbBiaoPersonRelationsRepository : IBaseRepository<OsZbBiaoPersonRelations>
	{
	}
	public interface IOsZbBiaoTechExtraInfoRepository : IBaseRepository<OsZbBiaoTechExtraInfo>
	{
	}
	public interface IOsZbBiaoTechParamGoodSettingRepository : IBaseRepository<OsZbBiaoTechParamGoodSetting>
	{
	}
	public interface IOsZbModelFileInfoRepository : IBaseRepository<OsZbModelFileInfo>
	{
	}
	public interface IOsZbPersonRelationsRepository : IBaseRepository<OsZbPersonRelations>
	{
	}
	public interface IOsZbSupplierCommonResponseInfoRepository : IBaseRepository<OsZbSupplierCommonResponseInfo>
	{
		void ExportToJsonFile(OsZbSupplierCommonResponseInfo resInfo, string saveRootPath);

		void GenResposeData(OsZbSupplierCommonResponseInfo responseInfo);
	}
	public interface IBaseBillRepository : IBaseRepository<OsZbBaseBillInfo>
	{
		BendPageInfo<OsZbBillInfo> SearchWithBill(BendPageInfo<OsZbBillInfo> pageInfo, string selectColumnSql = null, string joinTableSql = null, Expression<Func<OsZbBillInfo, object>>[] whereColums = null, string groupby = null);
	}
	public interface IOsTechSpecifyBookRepository : IBaseRepository<OsTechSpecifyBook>
	{
		bool CreatePdfs(ref string selectedPath, IEnumerable<OsTechSpecifyBook> items, int printZoom, PageOrientationType printOrientation, out List<string> outFiles);

		string ExportExcelFiles(OsTechSpecifyBook book, string selectedPath);

		bool ImportStandardPdfFiles(SqlWrapper<OsTechSpecifyBook> sqlWrapper, IEnumerable<string> fileNames, OsTechSpecifyBook curBook = null);

		void InsertAndStoreFile(List<OsTechSpecifyBook> fileList, bool deleteByRelatedId = true);

		bool UploadFile(SysFileInfo fileInfoVo, OsTechSpecifyBook book, string fileNameTxtFlag = null, int fileMaxSize = 2046);

		R MigrateExportData(OsTechSpecifyBook techSpecifyBook, string saveFile);

		R MigrateImportData(OsTechSpecifyBook techSpecifyBook, string saveFile, Func<string, OsTechSpecifyBook, bool> checkExcelEdit = null);
	}
	public interface IOsZbBiaoCoreStaffRepository : IBaseRepository<OsZbBiaoCoreStaff>
	{
	}
	public interface IOsZbBillInfoRepository : IBaseRepository<OsZbBillInfo>
	{
		bool ExportToExcelUsePage(OsZbBillInfo osZbBillInfo, string projectNo, string exportFolder);
	}
	public interface IOsZbCoreStaffRepository : IBaseRepository<OsZbCoreStaff>
	{
	}
	public interface IOsZbMaterialListRepository : IBaseRepository<OsZbMaterialList>
	{
	}
	public interface IOsZbPurchaseProjectInfoRepository : IBaseRepository<OsZbPurchaseProjectInfo>
	{
		bool ExportToExcelWithProjectList(string flag);

		bool ImportExcelWithProjectAndGoods();

		IList<OsZbPurchaseProjectInfo> SelectMarkAllPack(OsZbPurchaseProjectInfo query);
	}
	public interface IOsZbSTechResponseInfoRepository : IBaseRepository<OsZbSTechResponseInfo>
	{
	}
	public interface IOsZbSupplierBiaoDurationResponseRepository : IBaseRepository<OsZbSupplierBiaoDurationResponse>
	{
	}
	public interface IOsZbSupplierCreditInfoRepository : IBaseRepository<OsZbSupplierCreditInfo>
	{
	}
	public interface IOsZbSupplierBiaoLabeqpInfoRepository : IBaseRepository<OsZbSupplierBiaoLabeqpInfo>
	{
	}
	public interface IOsZbSupplierBiaoMeqpInfoRepository : IBaseRepository<OsZbSupplierBiaoMeqpInfo>
	{
	}
	public interface IOsZbSupplierBiaoReportInfoRepository : IBaseRepository<OsZbSupplierBiaoReportInfo>
	{
	}
	public interface IOsZbSupplierBiaoSalesInfoRepository : IBaseRepository<OsZbSupplierBiaoSalesInfo>
	{
		bool ExportToExcelUsePage(OsZbSupplierBiaoSalesInfo sale, string selectedPath);
	}
	public interface IOsZbSupplierBidAttachmentRepository : IBaseRepository<OsZbSupplierBidAttachment>
	{
		void CheckAttachIsUpload(string markNo, string packName, string supplierId);
	}
	public interface IOsZbSupplierDepositDetailRepository : IBaseRepository<OsZbSupplierDepositDetail>
	{
	}
	public interface IOsZbSupplierDepositRepository : IBaseRepository<OsZbSupplierDeposit>
	{
	}
	public interface IOsZbSupplierEquityInfoRepository : IBaseRepository<OsZbSupplierEquityInfo>
	{
	}
	public interface IOsZbSupplierFinanceInfoRepository : IBaseRepository<OsZbSupplierFinanceInfo>
	{
	}
	public interface IOsZbSupplierLabeqpInfoRepository : IBaseRepository<OsZbSupplierLabeqpInfo>
	{
	}
	public interface IOsZbSupplierMeqpInfoRepository : IBaseRepository<OsZbSupplierMeqpInfo>
	{
	}
	public interface IOsZbSupplierProductInfoRepository : IBaseRepository<OsZbSupplierProductInfo>
	{
		bool ExportToExcelUsePage(OsZbSupplierProductInfo product, string exportFolder);
	}
	public interface IOsZbSupplierReportInfoRepository : IBaseRepository<OsZbSupplierReportInfo>
	{
	}
	public interface IOsZbSupplierSalesInfoRepository : IBaseRepository<OsZbSupplierSalesInfo>
	{
	}
	public interface ISysExportRecordRepository : IBaseRepository<SysExportRecord>
	{
	}
	public interface ISysFileInfoRepository : IBaseRepository<SysFileInfo>
	{
		void InsertAndStoreFile(List<SysFileInfo> fileList, FileDelStrategy fileDelStrategy = FileDelStrategy.RelatedKeyAndRelatedIdAndHashCode);

		IList<SysFileInfo> SearchBillCountInfo(List<string> parentIds, bool queryBinaryFile = false);

		IList<SysFileInfo> SearchCountByRelatedPageAndIds(string currentRelatedPage, List<string> relatedIds, bool queryBinaryFile = false);

		IList<SysFileInfo> SearchListWithMakeBidBook(OsZbPurchaseProjectInfo project, SysFileInfo query);
	}
	public interface ISysModelConfigRepository : IBaseRepository<SysModelConfig>
	{
	}
	public interface ISysModuleLableRepository : IBaseRepository<SysModuleLable>
	{
	}
	public interface ITStatementInfoRepository : IBaseRepository<TStatementInfo>
	{
	}
	public interface IOsZbSupplierBiaoCreditInfoRepository : IBaseRepository<OsZbSupplierBiaoCreditInfo>
	{
		List<OsZbSupplierCreditInfo> SearchCompanyAwardsForBus(OsZbPurchaseProjectInfo currentProjectInfo, BidBookType bidBookType);

		List<OsZbSupplierBiaoCreditInfo> SearchCompanyAwardsForTech(OsZbPurchaseProjectInfo currentProjectInfo, BidBookType bidBookType);
	}
	public interface IZbBidAttachRecordRepository : IBaseRepository<ZbBidAttachRecord>
	{
		void SplitDbAndUpload(string dbFile, OsZbPurchaseProjectInfo projectInfo, ZbBidAttachRecord record, Action<int> progressCallBack);

		void WithdrawRecord(ZbBidAttachRecord record, OsZbPurchaseProjectInfo projectInfo);

		void DownLoadMarkFiles(string rootPath, ZbBidAttachRecord record, OsZbPurchaseProjectInfo info, out string saveFile, Action<int> progressCallBack);
	}
	public interface IOsZbSupplierBiaoAuthPersonInfoRepository : IBaseRepository<OsZbSupplierBiaoAuthPersonInfo>
	{
	}
	public interface IOsZbSupplierInfoRepository : IBaseRepository<OsZbSupplierInfo>
	{
	}
}
namespace MyCommon.Repository.common
{
	public abstract class BaseAbstractRepository<TEntity> : IBaseRepository<TEntity> where TEntity : CommonEntity
	{
		protected readonly string sqliteDbName;

		protected readonly string sqliteDbLocation;

		protected static string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "templates\\\\");

		public BaseAbstractRepository()
		{
			sqliteDbName = ConfigHelper.GetValueByKey("sqliteDBName");
			sqliteDbLocation = ConfigHelper.GetValueByKey("sqliteDBLocation");
		}

		public BaseAbstractRepository(DbDataSource ds)
		{
			sqliteDbLocation = ds?.SqliteDbLocation;
			sqliteDbName = ds?.SqliteDbName;
		}

		public BaseAbstractRepository(string sqliteDbLocation, string sqliteDbName)
		{
			this.sqliteDbLocation = sqliteDbLocation;
			this.sqliteDbName = sqliteDbName;
		}

		public virtual IList<TEntity> Search(TEntity query, string selectColumnSql = null, Expression<Func<TEntity, object>>[] columns = null, string joinTableSql = null, Expression<Func<TEntity, object>>[] whereColums = null, string groupby = null)
		{
			whereColums = whereColums ?? new Expression<Func<TEntity, object>>[0];
			bool groupById = string.IsNullOrEmpty(groupby);
			string text = SQLiteSqlUtils.CreateSelectSql2(query, selectColumnSql, joinTableSql, groupById, columns, whereColums);
			if (!string.IsNullOrEmpty(groupby))
			{
				text = text + " " + groupby + " ";
			}
			if (query.GetType().GetProperty("CreateTime") != (PropertyInfo)null)
			{
				text += " ORDER BY CREATE_TIME";
			}
			return SQLiteLibrary.SelectBySql<BindingList<TEntity>, TEntity>(sqliteDbLocation, sqliteDbName, text);
		}

		public virtual IList<TEntity> Search(SqlWrapper<TEntity> sqlWrapper)
		{
			string text = sqlWrapper.Build(SqlStrategy.Select);
			Console.WriteLine("当前sql: " + text);
			return SQLiteLibrary.SelectBySql<BindingList<TEntity>, TEntity>(sqliteDbLocation, sqliteDbName, text);
		}

		public virtual BendPageInfo<TEntity> Search(SqlWrapper<TEntity> sqlWrapper, BendPageInfo<TEntity> pageInfo)
		{
			sqlWrapper.PageNumber = pageInfo.CurPage;
			sqlWrapper.PageSize = pageInfo.PageSize;
			IList<TEntity> recordList = Search(sqlWrapper);
			string selectSql = SQLiteSqlUtils.CreatePageTotalCountSql(sqlWrapper);
			int num = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, selectSql);
			int totalPage = num / pageInfo.PageSize + ((num % pageInfo.PageSize != 0) ? 1 : 0);
			return new BendPageInfo<TEntity>
			{
				CurPage = pageInfo.CurPage,
				PageSize = pageInfo.PageSize,
				TotalPage = totalPage,
				RecordList = recordList,
				TotalRecord = num
			};
		}

		public virtual BendPageInfo<TEntity> SearchByOtherEntity<OtherEntity>(SqlWrapper<OtherEntity> sqlWrapper, BendPageInfo<TEntity> pageInfo) where OtherEntity : CommonEntity
		{
			sqlWrapper.PageNumber = pageInfo.CurPage;
			sqlWrapper.PageSize = pageInfo.PageSize;
			IList<TEntity> recordList = SearchOther<OtherEntity, TEntity>(sqlWrapper);
			string selectSql = SQLiteSqlUtils.CreatePageTotalCountSql(sqlWrapper);
			int num = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, selectSql);
			int totalPage = num / pageInfo.PageSize + ((num % pageInfo.PageSize != 0) ? 1 : 0);
			return new BendPageInfo<TEntity>
			{
				CurPage = pageInfo.CurPage,
				PageSize = pageInfo.PageSize,
				TotalPage = totalPage,
				RecordList = recordList,
				TotalRecord = num
			};
		}

		public virtual BendPageInfo<TEntity> Search(BendPageInfo<TEntity> pageInfo, string selectColumnSql = null, string joinTableSql = null, Expression<Func<TEntity, object>>[] whereColums = null, string groupby = null, string whereSqlFragment = null)
		{
			whereColums = whereColums ?? new Expression<Func<TEntity, object>>[0];
			TEntity searchEntity = pageInfo.SearchEntity;
			bool groupById = string.IsNullOrEmpty(groupby);
			string text = SQLiteSqlUtils.CreateSelectSql2(searchEntity, selectColumnSql, joinTableSql, groupById, null, whereColums);
			string text2 = SQLiteSqlUtils.CreateCountSql(searchEntity, whereColums);
			if (!string.IsNullOrEmpty(groupby))
			{
				text = text + " " + whereSqlFragment + " " + groupby + " ";
				text2 = text2 + " " + whereSqlFragment + " " + groupby + " ";
				text2 = "select count(0)  from ( " + text2.Replace("count(0)", "null") + " ) ";
			}
			text += " ORDER BY CREATE_TIME";
			int num = ((pageInfo.CurPage - 1 > 0) ? (pageInfo.CurPage - 1) : 0);
			text += $" limit {num * pageInfo.PageSize},{pageInfo.PageSize} ";
			IList<TEntity> recordList = SQLiteLibrary.SelectBySql<BindingList<TEntity>, TEntity>(sqliteDbLocation, sqliteDbName, text);
			int num2 = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, text2);
			int totalPage = num2 / pageInfo.PageSize + ((num2 % pageInfo.PageSize != 0) ? 1 : 0);
			return new BendPageInfo<TEntity>
			{
				CurPage = pageInfo.CurPage,
				PageSize = pageInfo.PageSize,
				TotalPage = totalPage,
				RecordList = recordList,
				TotalRecord = num2
			};
		}

		public virtual void ExportToExcel(string _fileName, SqlWrapper<TEntity> sqlWrapper, ExpertOption expertOption = null)
		{
			IList<TEntity> sourceData = Search(sqlWrapper);
			string fileName = DateTime.Now.ToString("yyyyMMddhhmmss") + "_" + _fileName + ".xlsx";
			AsposeExcel.ExportTemplateToExcel(fileName, expertOption?.HeadContent, sourceData, expertOption?.BusinessId, delegate(Worksheet wk)
			{
				wk.AutoFitColumns();
			}, expertOption?.OpenOffice ?? true);
		}

		public virtual bool ImportExcel(out IList<TEntity> entities, int titleRow = 0, Func<TEntity, int, string> CheckAction = null, string businessId = null, int startSheet = 0)
		{
			MessageInfo<TEntity> messageInfo = ExcelReadUtil.ReadExcel<TEntity>(startSheet, titleRow, businessId, CheckAction);
			entities = messageInfo.Record;
			if (messageInfo.ExistError)
			{
				string message = ((messageInfo.ErrorInfo.Length > 1024) ? (messageInfo.ErrorInfo.Substring(0, 1024) + "\r\n.......") : messageInfo.ErrorInfo);
				throw new Exception(message);
			}
			return messageInfo.Record?.Any() ?? false;
		}

		public TEntity AddOrUpdate(TEntity entity, Func<TEntity, string> primaryKeyAction, string primaryPropName = "Id", Expression<Func<TEntity, object>>[] whereColums = null, string aliasPrimaryColumn = null)
		{
			aliasPrimaryColumn = aliasPrimaryColumn ?? primaryPropName;
			string text = primaryKeyAction?.Invoke(entity);
			bool flag = text == null;
			if (whereColums != null && whereColums.Any())
			{
				string selectSql = SQLiteSqlUtils.CreateCountSql(entity, whereColums) + (flag ? "" : (" AND " + aliasPrimaryColumn + "!='" + text + "'"));
				int num = SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, selectSql);
				if (num > 0)
				{
					throw new CheckoutException("数据已存在不得新增或修改");
				}
			}
			if (flag)
			{
				typeof(TEntity).GetProperty(primaryPropName)?.SetValue(entity, Guid.NewGuid().ToString("N"), null);
				string nonQuerySql = SQLiteSqlUtils.CreateInsertSql(entity);
				SQLiteLibrary.ExcuteSql(nonQuerySql, compressDb: false, sqliteDbLocation, sqliteDbName);
			}
			else
			{
				string nonQuerySql2 = SQLiteSqlUtils.CreateUpdateSql(entity, new string[1] { primaryPropName });
				SQLiteLibrary.ExcuteSql(nonQuerySql2, compressDb: false, sqliteDbLocation, sqliteDbName);
			}
			return entity;
		}

		public bool DeleteByPrimaryKeysWithMakeBook(OsZbPurchaseProjectInfo project, BidBookType businessOrTechFlag = BidBookType.BusinessBidBook, params string[] primaryKeys)
		{
			if (project != null)
			{
				OsZbSupplierBidAttachment a = new OsZbSupplierBidAttachment();
				ObjectUtil.CopyPop(project, ref a);
				a.PackName = (((businessOrTechFlag == BidBookType.BusinessBidBook && project.BusinessFileWay == "PACK") || (businessOrTechFlag == BidBookType.TechBidBook && project.SkillFileWay == "PACK")) ? project.PackName : null);
				string text = ((businessOrTechFlag == BidBookType.BusinessBidBook) ? "'shangwu','businiess'" : "'jishu','skill'");
				Expression<Func<OsZbSupplierBidAttachment, object>>[] whereColums = new Expression<Func<OsZbSupplierBidAttachment, object>>[5]
				{
					(OsZbSupplierBidAttachment p) => p.IsDelete,
					(OsZbSupplierBidAttachment p) => p.MarkNo,
					(OsZbSupplierBidAttachment p) => p.SupplierId,
					(OsZbSupplierBidAttachment p) => p.FileType,
					(OsZbSupplierBidAttachment p) => p.PackName
				};
				string text2 = SQLiteSqlUtils.CreateCountSql(a, whereColums);
				text2 = text2 + " AND FILE_TYPE in (" + text + ") ";
				int num = SQLiteLibrary.SelectCount(text2, sqliteDbLocation, sqliteDbName);
				if (num > 0)
				{
					throw new Exception("该项目标书已上传，不得进行删除操作！");
				}
			}
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < primaryKeys.Length; i++)
			{
				if (primaryKeys[i] == null)
				{
					throw new ArgumentNullException("primaryKeys");
				}
				if (primaryKeys[i].StartsWith("'"))
				{
					stringBuilder.Append(primaryKeys[i] + ",");
				}
				else
				{
					stringBuilder.Append("'" + primaryKeys[i] + "',");
				}
			}
			Type typeFromHandle = typeof(TEntity);
			TableNameAttribute[] array = (TableNameAttribute[])typeFromHandle.GetCustomAttributes(typeof(TableNameAttribute), inherit: false);
			TableNameAttribute tableNameAttribute = ((array != null) ? array[0] : null);
			SQLiteLibrary.DeleteBySql(sqliteDbLocation, sqliteDbName, "DELETE  FROM " + tableNameAttribute.Value + " WHERE ID IN (" + stringBuilder.ToString().TrimEnd(',') + ")");
			return true;
		}

		public bool ExecuteSqlByTransaction(params string[] sqls)
		{
			return SQLiteLibrary.ExecuteSqlByTransaction(sqliteDbLocation, sqliteDbName, sqls);
		}

		public bool DeleteByPrimaryKeys(params string[] primaryKeys)
		{
			Type typeFromHandle = typeof(TEntity);
			return DeleteByPrimaryKeysWithMakeBook(null, BidBookType.BusinessBidBook, primaryKeys);
		}

		public bool AddBatch(IList<TEntity> entities)
		{
			SQLiteLibrary.InsertBatch(entities, sqliteDbLocation, sqliteDbName);
			return true;
		}

		public bool AddBatch(SqlWrapper<TEntity> sqlWrapper, IEnumerable<TEntity> collections, Expression<Func<TEntity, object>>[] ignoreColumns = null)
		{
			string nonQuerySql = sqlWrapper.BuildWithInsert(collections, ignoreColumns);
			SQLiteLibrary.ExcuteSql(nonQuerySql, compressDb: false, sqliteDbLocation, sqliteDbName);
			return true;
		}

		public bool UpdateBatch(List<TEntity> entities, Expression<Func<TEntity, object>>[] columns = null, params Expression<Func<TEntity, object>>[] whereColums)
		{
			string nonQuerySql = SQLiteSqlUtils.CreateUpdateSqlByEntity(entities, columns, whereColums);
			SQLiteLibrary.ExcuteSql(nonQuerySql, compressDb: false, sqliteDbLocation, sqliteDbName);
			return true;
		}

		public int SearchCount(SqlWrapper<TEntity> sqlWrapper)
		{
			string text = SearchByFunc(sqlWrapper, SqlFunctionEnum.Count, null);
			Console.WriteLine("当前sql:" + text);
			return SQLiteLibrary.SelectCount(text, sqliteDbLocation, sqliteDbName);
		}

		protected string SearchByFunc(SqlWrapper<TEntity> sqlWrapper, SqlFunctionEnum sqlFunctionEnum, string sqlFuncColumn)
		{
			return sqlWrapper.Build(SqlStrategy.Select, sqlFunctionEnum, sqlFuncColumn);
		}

		public bool DeleteBatch(List<TEntity> list, params Expression<Func<TEntity, object>>[] whereColums)
		{
			string deleteSql = SQLiteSqlUtils.CreateDeleteSql(list, whereColums);
			SQLiteLibrary.DeleteBySql(sqliteDbLocation, sqliteDbName, deleteSql);
			return true;
		}

		public bool AddItem(TEntity tEntity)
		{
			string nonQuerySql = SQLiteSqlUtils.CreateInsertSql(tEntity);
			SQLiteLibrary.ExcuteSql(nonQuerySql, compressDb: false, sqliteDbLocation, sqliteDbName);
			return true;
		}

		public bool UpdateItem(SqlWrapper<TEntity> sqlWrapper)
		{
			string text = sqlWrapper.Build(SqlStrategy.Update);
			Console.WriteLine("当前sql:\r\n" + text);
			SQLiteLibrary.ExcuteSql(text, compressDb: false, sqliteDbLocation, sqliteDbName);
			return true;
		}

		public bool DeleteItem(SqlWrapper<TEntity> sqlWrapper)
		{
			string deleteSql = sqlWrapper.Build(SqlStrategy.Delete);
			SQLiteLibrary.DeleteBySql(sqliteDbLocation, sqliteDbName, deleteSql);
			return true;
		}

		public bool DeleteNormalItem(SqlWrapper<TEntity> sqlWrapper)
		{
			string sql = sqlWrapper.Build(SqlStrategy.Delete);
			SQLiteLibrary.ExcuteSql(sqliteDbLocation, sqliteDbName, sql);
			return true;
		}

		public TEntity SearchOne(SqlWrapper<TEntity> sqlWrapper)
		{
			string text = sqlWrapper.Build(SqlStrategy.Select);
			Console.WriteLine("当前sql: " + text);
			return SQLiteLibrary.SelectOneBySql<TEntity>(text, sqliteDbLocation, sqliteDbName);
		}

		public virtual IList<TOtherEntity> SearchCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity
		{
			string text = sqlWrapper.Build(SqlStrategy.Select);
			Console.WriteLine("当前sql: " + text);
			return SQLiteLibrary.SelectBySql<BindingList<TOtherEntity>, TOtherEntity>(sqliteDbLocation, sqliteDbName, text);
		}

		public virtual TOtherEntity SearchOneCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity
		{
			string text = sqlWrapper.Build(SqlStrategy.Select);
			Console.WriteLine("当前sql: " + text);
			return SQLiteLibrary.SelectOneBySql<TOtherEntity>(text, sqliteDbLocation, sqliteDbName);
		}

		public virtual int SearchCountCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity
		{
			string selectSql = sqlWrapper.Build(SqlStrategy.Select, SqlFunctionEnum.Count);
			return SQLiteLibrary.SelectCount(selectSql);
		}

		public IList<TResult> SearchOther<TOtherEntity, TResult>(SqlWrapper<TOtherEntity> sqlWrapper) where TOtherEntity : CommonEntity where TResult : CommonEntity
		{
			string text = sqlWrapper.Build(SqlStrategy.Select);
			Console.WriteLine("当前sql: " + text);
			return SQLiteLibrary.SelectBySql<BindingList<TResult>, TResult>(sqliteDbLocation, sqliteDbName, text);
		}

		public bool VacuumWithSqlite()
		{
			SQLiteLibrary.ExcuteSql(sqliteDbLocation, sqliteDbName, "VACUUM;");
			return true;
		}

		public void UpdateAndStoreFile(IEnumerable<TEntity> list, Expression<Func<TEntity, object>>[] updateCols, Expression<Func<TEntity, object>>[] whereCols)
		{
			SQLiteLibrary.UpdateByParams(list, updateCols, whereCols, sqliteDbLocation, sqliteDbName);
		}

		public void DownloadExcelTemplate(string _fileName, IList<TEntity> list, ExpertOption expertOption = null)
		{
			string fileName = DateTime.Now.ToString("yyyyMMddhhmmss") + "_" + _fileName + ".xlsx";
			AsposeExcel.ExportTemplateToExcel(fileName, expertOption?.HeadContent, list, expertOption?.BusinessId, delegate(Worksheet wk)
			{
				wk.AutoFitColumns();
			}, expertOption?.OpenOffice ?? true);
		}
	}
	public class BaseRepository<TEntity> : BaseAbstractRepository<TEntity> where TEntity : CommonEntity
	{
	}
}
namespace MyCommon.Repository.enums
{
	public enum FileDelStrategy
	{
		None = 0,
		ID = 1,
		RelatedPage = 2,
		RelatedKey = 4,
		RelatedId = 8,
		HashCode = 16,
		RelatedKeyAndRelatedId = 12,
		RelatedPageAndRelatedKey = 6,
		RelatedKeyAndRelatedIdAndHashCode = 28,
		RelatedPageAndRelatedKeyAndRelatedId = 14
	}
}
namespace MyCommon.Repository.dataSource
{
	public class DefaultDbDataSource : DbDataSource
	{
		private string _sqliteDbLocation = ConfigHelper.GetValueByKey("sqliteDBLocation");

		private string _sqliteDbName = ConfigHelper.GetValueByKey("sqliteDBName");

		public string SqliteDbLocation => _sqliteDbLocation;

		public string SqliteDbName => _sqliteDbName;

		public DefaultDbDataSource()
		{
		}

		public DefaultDbDataSource(string sqliteDbLocation, string sqliteDbName)
		{
			_sqliteDbLocation = sqliteDbLocation;
			_sqliteDbName = sqliteDbName;
		}
	}
	public class ManageBaseDbDataSource : DbDataSource
	{
		private string _sqliteDbLocation = ConfigHelper.GetValueByKey("dbRootFolder");

		private string _sqliteDbName = ConfigHelper.GetValueByKey("baseDBName");

		public string SqliteDbLocation => _sqliteDbLocation;

		public string SqliteDbName => _sqliteDbName;
	}
	public class ManageParseDbDataSource : DbDataSource
	{
		private string _sqliteDbLocation = ConfigHelper.GetValueByKey("dbRootFolder");

		private string _sqliteDbName = ConfigHelper.GetValueByKey("dbName");

		public string SqliteDbLocation => _sqliteDbLocation;

		public string SqliteDbName => _sqliteDbName;
	}
}
namespace MyCommon.Repository.dataSource.Base
{
	public interface DbDataSource
	{
		string SqliteDbLocation { get; }

		string SqliteDbName { get; }
	}
}
namespace MyCommon.Repository.dataContext
{
	public class MigrateDataContext : DbDataContext
	{
		public IOsZbPurchaseProjectInfoRepository ProjectRepository { get; protected set; }

		public ISysFileInfoRepository SysFileRepository { get; protected set; }

		public IOsTechSpecifyBookRepository TechSpecifyBookRepository { get; protected set; }

		public ISysModelConfigRepository ModelConfigRepository { get; protected set; }

		public IOsZbBadBehaviorInfoRepository BadBehaviorRepository { get; protected set; }

		public MigrateDataContext(string sqliteDbLocation, string sqliteDbName)
		{
			DefaultDbDataSource ds = new DefaultDbDataSource(sqliteDbLocation, sqliteDbName);
			ProjectRepository = new OsZbPurchaseProjectInfoRepository(ds);
			SysFileRepository = new SysFileInfoRepository(ds);
			TechSpecifyBookRepository = new OsTechSpecifyBookRepository(ds);
			ModelConfigRepository = new SysModelConfigRepository(ds);
			BadBehaviorRepository = new OsZbBadBehaviorInfoRepository(ds);
		}
	}
	public class BidDataContext : DbDataContext
	{
		public IOsZbPurchaseProjectInfoRepository ProjectRepository { get; protected set; }

		public IOsZbSupplierInfoRepository SupplierRepository { get; protected set; }

		public ISysFileInfoRepository SysFileRepository { get; protected set; }

		public IOsZbSupplierBiaoFinanceInfoRepository BiaoFinanceRepository { get; }

		public IZbBidAttachRecordRepository AttachRecordRepository { get; }

		public IOsZbSupplierAuthPersonInfoRepository AuthPersonRepository { get; }

		public IOsZbSupplierBiaoSalesInfoRepository BiaoSalesRepository { get; }

		public IOsZbSupplierBidAttachmentRepository BidAttachRepository { get; }

		public IOsZbMaterialListRepository MaterialListRepository { get; }

		public ISysExportRecordRepository ExportRecordRepository { get; }

		public IOsZbBillInfoRepository BillRepository { get; }

		public IOsZbSupplierEquityInfoRepository EquityRepository { get; }

		public IOsZbSupplierBiaoEquityInfoRepository BiaoEquityRepository { get; }

		public IOsZbSupplierDepositRepository DepositRepository { get; }

		public IOsZbSupplierDepositDetailRepository DepositDetailRepository { get; }

		public IOsTechSpecifyBookRepository TechSpecifyBookRepository { get; protected set; }

		public ISysModelConfigRepository ModelConfigRepository { get; protected set; }

		public ISysModuleLableRepository ModuleLableRepository { get; private set; }

		public IOsZbSupplierBiaoDurationResponseRepository BiaoDurationResponseRepository { get; private set; }

		public IOsZbSTechResponseInfoRepository STechResponseInfoRepository { get; private set; }

		public IOsZbSupplierFinanceInfoRepository FinanceRepository { get; private set; }

		public IOsZbBadBehaviorInfoRepository BadBehaviorInfoRepository { get; private set; }

		public IOsZbPersonRelationsRepository PersonRelationsRepository { get; private set; }

		public IOsZbBiaoPersonRelationsRepository BiaoPersonRelationsRepository { get; private set; }

		public IOsSupplierModelConfigRepository SupplierModelConfigRepository { get; private set; }

		public IOsZbModelFileInfoRepository ModelFileRepository { get; private set; }

		public IOsZbBiaoModelFileInfoRepository BiaoModelFileRepository { get; private set; }

		public IOsZbBiaoTechParamGoodSettingRepository BiaoTechParamGoodSettingRepository { get; private set; }

		public IOsZbBiaoTechExtraInfoRepository BiaoTechExtraRepository { get; private set; }

		public IOsZbSupplierCommonResponseInfoRepository CommonResponseRepository { get; private set; }

		protected BidDataContext()
		{
			DefaultDbDataSource ds = (DefaultDbDataSource)(base.CurDbDataSource = new DefaultDbDataSource());
			ProjectRepository = new OsZbPurchaseProjectInfoRepository(ds);
			SysFileRepository = new SysFileInfoRepository();
			BiaoFinanceRepository = new OsZbSupplierBiaoFinanceInfoRepository();
			AuthPersonRepository = new OsZbSupplierAuthPersonInfoRepository();
			BiaoSalesRepository = new OsZbSupplierBiaoSalesInfoRepository();
			BidAttachRepository = new OsZbSupplierBidAttachmentRepository();
			MaterialListRepository = new OsZbMaterialListRepository();
			ExportRecordRepository = new SysExportRecordRepository();
			BillRepository = new OsZbBillInfoRepository();
			EquityRepository = new OsZbSupplierEquityInfoRepository();
			DepositRepository = new OsZbSupplierDepositRepository();
			DepositDetailRepository = new OsZbSupplierDepositDetailRepository();
			TechSpecifyBookRepository = new OsTechSpecifyBookRepository(ds);
			ModelConfigRepository = new SysModelConfigRepository(ds);
			ModuleLableRepository = new SysModuleLableRepository(ds);
			BiaoDurationResponseRepository = new OsZbSupplierBiaoDurationResponseRepository();
			STechResponseInfoRepository = new OsZbSTechResponseInfoRepository();
			FinanceRepository = new OsZbSupplierFinanceInfoRepository();
			BiaoEquityRepository = new OsZbSupplierBiaoEquityInfoRepository();
			BadBehaviorInfoRepository = new OsZbBadBehaviorInfoRepository(ds);
			SupplierRepository = new OsZbSupplierInfoRepository(ds);
			PersonRelationsRepository = new OsZbPersonRelationsRepository(ds);
			BiaoPersonRelationsRepository = new OsZbBiaoPersonRelationsRepository(ds);
			SupplierModelConfigRepository = new OsSupplierModelConfigRepository(ds);
			ModelFileRepository = new OsZbModelFileInfoRepository(ds);
			BiaoModelFileRepository = new OsZbBiaoModelFileInfoRepository(ds);
			BiaoTechParamGoodSettingRepository = new OsZbBiaoTechParamGoodSettingRepository(ds);
			BiaoTechExtraRepository = new OsZbBiaoTechExtraInfoRepository(ds);
			CommonResponseRepository = new OsZbSupplierCommonResponseInfoRepository(ds);
		}
	}
	public class DbDataContext
	{
		public DbDataSource CurDbDataSource { get; set; }

		public DbDataContext()
		{
		}

		public DbDataContext(DbDataSource curDbDataSource)
		{
			CurDbDataSource = curDbDataSource;
		}
	}
	public static class DbDataContextFactory
	{
		private static ConcurrentDictionary<Type, DbDataContext> dataContextDict = new ConcurrentDictionary<Type, DbDataContext>();

		private static BindingFlags flags = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;

		public static TDataContext GetDbDataContext<TDataContext>() where TDataContext : DbDataContext
		{
			Type type = typeof(TDataContext);
			return (TDataContext)dataContextDict.GetOrAdd(type, (Func<Type, DbDataContext>)delegate
			{
				ConstructorInfo constructorInfo = ((IEnumerable<ConstructorInfo>)type.GetConstructors(flags)).Where((Func<ConstructorInfo, bool>)((ConstructorInfo p) => p.GetParameters().Length == 0)).FirstOrDefault();
				return (DbDataContext)constructorInfo.Invoke(null);
			});
		}
	}
	public class ManageBaseDataContext : DbDataContext
	{
		public IOsZbPurchaseProjectInfoRepository ProjectRepository { get; private set; }

		public IOsZbMaterialListRepository MaterialListRepository { get; private set; }

		public IOsTechSpecifyBookRepository TechSpecifyBookRepository { get; private set; }

		public ISysModelConfigRepository SysModelConfigRepository { get; private set; }

		public IOsZbBadBehaviorInfoRepository BadBehaviorRepository { get; private set; }

		private ManageBaseDataContext()
		{
			ManageBaseDbDataSource ds = new ManageBaseDbDataSource();
			ProjectRepository = new OsZbPurchaseProjectInfoRepository(ds);
			TechSpecifyBookRepository = new OsTechSpecifyBookRepository(ds);
			SysModelConfigRepository = new SysModelConfigRepository(ds);
			MaterialListRepository = new OsZbMaterialListRepository(ds);
			BadBehaviorRepository = new OsZbBadBehaviorInfoRepository(ds);
		}
	}
	public class ManageParseDataContext : DbDataContext
	{
		public IOsZbPurchaseProjectInfoRepository ProjectRepository { get; private set; }

		public IOsZbBillInfoRepository BillInfoRepository { get; private set; }

		public IOsZbSupplierProductInfoRepository ProductInfoRepository { get; private set; }

		public IOsZbSupplierBiaoSalesInfoRepository BiaoSaleRepository { get; private set; }

		public IOsZbSupplierBiaoEquityInfoRepository BiaoEquityRepository { get; private set; }

		public IOsZbBadBehaviorInfoRepository BadBehaviorRepository { get; private set; }

		public IOsZbSupplierCommonResponseInfoRepository CommonResponseRepository { get; private set; }

		private ManageParseDataContext()
		{
			ManageParseDbDataSource ds = new ManageParseDbDataSource();
			ProjectRepository = new OsZbPurchaseProjectInfoRepository(ds);
			BillInfoRepository = new OsZbBillInfoRepository(ds);
			ProductInfoRepository = new OsZbSupplierProductInfoRepository(ds);
			BiaoSaleRepository = new OsZbSupplierBiaoSalesInfoRepository(ds);
			BiaoEquityRepository = new OsZbSupplierBiaoEquityInfoRepository(ds);
			BadBehaviorRepository = new OsZbBadBehaviorInfoRepository(ds);
			CommonResponseRepository = new OsZbSupplierCommonResponseInfoRepository(ds);
		}
	}
}
