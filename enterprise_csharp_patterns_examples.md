# Enterprise C# Patterns Examples

Based on the analysis of the decompiled code, here are concrete examples of valuable enterprise C# patterns and techniques:

## 1. Repository Pattern with Generics

```csharp
public interface IBaseRepository<TEntity> where TEntity : CommonEntity
{
    IList<TEntity> Search(SqlWrapper<TEntity> sqlWrapper);
    bool Add(TEntity entity);
    bool Update(TEntity entity);
    bool Delete(TEntity entity);
    bool DeleteItem(SqlWrapper<TEntity> sqlWrapper);
    int SearchCount(SqlWrapper<TEntity> sqlWrapper);
    TEntity SearchOne(SqlWrapper<TEntity> sqlWrapper);
    
    // Generic cross-entity operations
    IList<TOtherEntity> SearchCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) 
        where TOtherEntity : CommonEntity;
    
    TOtherEntity SearchOneCommon<TOtherEntity>(SqlWrapper<TOtherEntity> sqlWrapper) 
        where TOtherEntity : CommonEntity;
}
```

**Key Benefits:**
- Generic interface allows reuse across different entity types
- Constraint `where TEntity : CommonEntity` ensures type safety
- Abstracts data access logic from business logic
- Supports both single-entity and cross-entity operations

## 2. Strategy Pattern for Validation

```csharp
public interface BaseVerifyStrategy
{
    bool Verify(object obj1, object obj2, object obj3);
}

public class VerifyChain
{
    internal List<BaseVerifyStrategy> VerifyStragegies { get; set; }
    
    public bool Verify(object obj1, object obj2, object obj3)
    {
        VerifyStragegies?.ForEach(delegate(BaseVerifyStrategy x)
        {
            x.Verify(obj1, obj2, obj3);
        });
        return true;
    }
}
```

**Key Benefits:**
- Pluggable verification strategies
- Chain of responsibility pattern implementation
- Easy to add new verification rules without modifying existing code
- Flexible parameter passing with object parameters

## 3. Expression Trees for Dynamic Queries

```csharp
// Example from the SearchAndBindingAttach method
SqlWrapper<SysModelConfig> sqlWrapper = SqlWrapperFactory.Instance<SysModelConfig>()
    .Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.MarkNo), 
        WhereCondition.AND, (object)currentProjectInfo.MarkNo, SqlSymbol.None)
    .Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.PackName), 
        WhereCondition.AND, (object)currentProjectInfo.PackName, SqlSymbol.None)
    .Eq((Expression<Func<SysModelConfig, object>>)((SysModelConfig v) => v.ModelCode), 
        WhereCondition.AND, (object)"FINANCE_REPORT_YEAR", SqlSymbol.None);
```

**Key Benefits:**
- Compile-time checked queries
- IntelliSense support for property names
- Refactoring-safe database queries
- Fluent API for building complex queries

## 4. LINQ and Functional Programming Patterns

```csharp
// Data transformation and filtering
List<string> source = (from v in sysModelConfig?.Value?.Replace("年", "").Replace("，", ",").Split(',', '，')
    orderby v
    select v).ToList();

// Dictionary creation using extension methods
Dictionary<string, string> dictionary = source.ToDictionaryByLocal<string, string, string>(
    (string v) => v.Trim(), 
    (string v) => v);

// Complex grouping and dictionary creation
Dictionary<string, List<SysFileInfo>> dictionary3 = (from p in source3
    group p by p.RelatedId + p.RelatedKey)
    .ToDictionary<IGrouping<string, SysFileInfo>, string, List<SysFileInfo>>(
        (IGrouping<string, SysFileInfo> v) => v.Key, 
        (IGrouping<string, SysFileInfo> v) => v.ToList());
```

**Key Benefits:**
- Declarative data transformation
- Readable and maintainable code
- Efficient data processing
- Custom extension methods for domain-specific operations

## 5. Dependency Injection and Constructor Overloading

```csharp
public class OsModelFileOwerInfoRepository : BaseAbstractRepository<OsModelFileOwerInfo>, 
    IOsModelFileOwerInfoRepository, IBaseRepository<OsModelFileOwerInfo>
{
    public OsModelFileOwerInfoRepository()
    {
    }
    
    public OsModelFileOwerInfoRepository(DbDataSource ds) : base(ds)
    {
    }
}
```

**Key Benefits:**
- Multiple constructor options for different scenarios
- Supports dependency injection frameworks
- Flexible initialization patterns
- Clean separation of concerns

## 6. Interface Segregation and Inheritance

```csharp
// Multiple interface implementation
public class OsZbSupplierBiaoFinanceInfoRepository : 
    BaseAbstractRepository<OsZbSupplierBiaoFinanceInfo>, 
    IOsZbSupplierBiaoFinanceInfoRepository, 
    IBaseRepository<OsZbSupplierBiaoFinanceInfo>
{
    // Specific interface method
    public List<OsZbSupplierBiaoFinanceInfo> SearchAndBindingAttach(
        OsZbPurchaseProjectInfo currentProjectInfo, 
        string modelName)
    {
        // Complex business logic implementation
    }
}
```

**Key Benefits:**
- Interface segregation principle
- Multiple inheritance through interfaces
- Specific business logic methods alongside generic operations
- Clear contract definition

## 7. Error Handling and Validation

```csharp
// Business rule validation with descriptive error messages
if (num5 != list.Count - 1)
{
    throw new Exception(modelName + "选择的数据年份不连续");
}

if (num3 > Convert.ToInt32(item.YearSj.Replace("年", "").Trim()))
{
    throw new Exception(modelName + "成立年份在发标时指定年份内,引用数据年份不得早于或等于成立时间，指定年份:" + sysModelConfig.Value);
}
```

**Key Benefits:**
- Domain-specific error messages
- Business rule enforcement
- Contextual error information
- Fail-fast validation approach

## 8. Namespace Organization

```csharp
namespace MyCommon.Repository.strategy    // Strategy pattern implementations
namespace MyCommon.Repository.Repository.impl    // Repository implementations
namespace MyCommon.Repository.Repository.ibase   // Repository interfaces
namespace MyCommon.Repository.impl       // General implementations
```

**Key Benefits:**
- Clear separation of concerns
- Logical grouping of related functionality
- Easy navigation and maintenance
- Consistent naming conventions

## Summary

These patterns demonstrate enterprise-level C# development practices:

1. **Repository Pattern** - Clean data access abstraction
2. **Strategy Pattern** - Flexible validation and business rules
3. **Expression Trees** - Type-safe dynamic queries
4. **LINQ** - Functional programming for data transformation
5. **Dependency Injection** - Loose coupling and testability
6. **Interface Segregation** - Clean contracts and multiple inheritance
7. **Proper Error Handling** - Business-focused exception handling
8. **Namespace Organization** - Maintainable code structure

These patterns are excellent examples for enterprise C# development and can be adapted for similar business applications.
