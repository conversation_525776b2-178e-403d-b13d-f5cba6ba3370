#!/usr/bin/env python3
"""
分析参考文档的脚本

查看正确的文档结构和格式
"""

import sys
import os
from docx import Document

def analyze_document(doc_path, doc_name):
    """
    分析单个参考文档
    
    Args:
        doc_path: 文档路径
        doc_name: 文档名称
    """
    print(f"\n{'='*60}")
    print(f"分析文档: {doc_name}")
    print(f"路径: {doc_path}")
    print(f"{'='*60}")
    
    if not os.path.exists(doc_path):
        print("❌ 文档不存在")
        return
    
    try:
        doc = Document(doc_path)
        
        # 基本信息
        print(f"总段落数: {len(doc.paragraphs)}")
        print(f"总表格数: {len(doc.tables)}")
        
        # 分析段落内容
        non_empty_paragraphs = [p for p in doc.paragraphs if p.text.strip()]
        print(f"非空段落数: {len(non_empty_paragraphs)}")
        
        if non_empty_paragraphs:
            print(f"\n前10个段落内容:")
            for i, paragraph in enumerate(non_empty_paragraphs[:10]):
                print(f"  {i+1:2d}. {paragraph.text.strip()}")
        
        # 分析表格结构
        print(f"\n表格详细信息:")
        for table_idx, table in enumerate(doc.tables):
            print(f"\n表格 {table_idx + 1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 显示表格的前5行
            if table.rows:
                print(f"  前5行内容:")
                for row_idx, row in enumerate(table.rows[:5]):
                    cells_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip().replace('\n', ' ').replace('\r', ' ')
                        if len(cell_text) > 30:
                            cell_text = cell_text[:27] + "..."
                        cells_text.append(cell_text)
                    print(f"    {row_idx + 1}: {' | '.join(cells_text)}")
            
            # 检查是否有投标人填写的字段
            fill_count = 0
            for row in table.rows:
                for cell in row.cells:
                    if any(phrase in cell.text for phrase in ["投标人填写", "(投标人填写)", "（投标人填写）"]):
                        fill_count += 1
            
            if fill_count > 0:
                print(f"  ⚠️  包含 {fill_count} 个投标人填写字段")
            else:
                print(f"  ✅ 所有字段均已填写")
        
        # 分析文档结构模式
        print(f"\n文档结构分析:")
        
        # 检查是否有标题
        title_paragraphs = []
        for p in doc.paragraphs:
            if p.style and 'title' in p.style.name.lower():
                title_paragraphs.append(p.text.strip())
        
        if title_paragraphs:
            print(f"  标题段落: {len(title_paragraphs)}")
            for title in title_paragraphs[:3]:
                print(f"    - {title}")
        
        # 检查段落样式
        styles = set()
        for p in doc.paragraphs:
            if p.style:
                styles.add(p.style.name)
        
        print(f"  使用的段落样式: {len(styles)}")
        for style in sorted(styles):
            print(f"    - {style}")
        
        return {
            'paragraphs': len(doc.paragraphs),
            'tables': len(doc.tables),
            'non_empty_paragraphs': len(non_empty_paragraphs),
            'styles': styles
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None

def main():
    """
    主函数
    """
    print("=" * 80)
    print("参考文档分析工具")
    print("=" * 80)
    
    # 参考文档路径
    reference_docs = [
        {
            'path': r"F:\2025\7月\国网安徽\投标文件\皖采云\技术参数特征表\24815-00005.docx",
            'name': "技术参数特征表（参考）"
        },
        {
            'path': r"F:\2025\7月\国网安徽\投标文件\皖采云\组件材料配置表\24815-00005.docx",
            'name': "组件材料配置表（参考）"
        }
    ]
    
    results = []
    
    for doc_info in reference_docs:
        result = analyze_document(doc_info['path'], doc_info['name'])
        if result:
            results.append({
                'name': doc_info['name'],
                'result': result
            })
    
    # 对比分析
    if len(results) == 2:
        print(f"\n{'='*60}")
        print("对比分析")
        print(f"{'='*60}")
        
        tech_params = results[0]['result']
        component_config = results[1]['result']
        
        print(f"技术参数表: {tech_params['paragraphs']} 段落, {tech_params['tables']} 表格")
        print(f"组件配置表: {component_config['paragraphs']} 段落, {component_config['tables']} 表格")
        
        # 样式对比
        tech_styles = tech_params['styles']
        comp_styles = component_config['styles']
        
        print(f"\n样式对比:")
        print(f"  技术参数表样式: {len(tech_styles)}")
        print(f"  组件配置表样式: {len(comp_styles)}")
        print(f"  共同样式: {len(tech_styles & comp_styles)}")
        print(f"  差异样式: {len(tech_styles ^ comp_styles)}")
        
        if tech_styles ^ comp_styles:
            print(f"  差异样式列表:")
            for style in sorted(tech_styles ^ comp_styles):
                print(f"    - {style}")
    
    print(f"\n{'='*80}")
    print("分析完成")

if __name__ == "__main__":
    main()
