using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Windows.Forms;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("BaseUserControls")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("BaseUserControls")]
[assembly: AssemblyCopyright("Copyright ©  2022")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("1eb58a82-f156-40e4-a839-9b7615ac0784")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace BaseUserControls;

public class RadioControl : UserControl
{
	private string radio1Text = "有效";

	private string radio2Text = "无效";

	private string radio3Text = "";

	private IContainer components = null;

	private Panel panel1;

	private RadioButton radioButton2;

	private RadioButton radioButton1;

	private RadioButton radioButton3;

	public string Radio1Text
	{
		get
		{
			return radio1Text;
		}
		set
		{
			radio1Text = value;
			radioButton1.Text = Radio1Text;
			ResetControlsPos();
		}
	}

	public string Radio2Text
	{
		get
		{
			return radio2Text;
		}
		set
		{
			radio2Text = value;
			radioButton2.Text = radio2Text;
			ResetControlsPos();
		}
	}

	public string Radio3Text
	{
		get
		{
			return radio3Text;
		}
		set
		{
			radio3Text = value;
			radioButton3.Text = radio3Text;
			ResetControlsPos();
		}
	}

	public RadioControl()
	{
		InitializeComponent();
	}

	public void ResetControlsPos()
	{
		radioButton2.Left = radioButton1.Right + 5;
		if (string.IsNullOrWhiteSpace(Radio3Text))
		{
			base.Width = radioButton2.Right + 10;
			radioButton3.Visible = false;
		}
		else
		{
			radioButton3.Left = radioButton2.Right + 5;
			radioButton3.Visible = true;
			base.Width = radioButton3.Right + 10;
		}
		base.Height = radioButton1.Height + 5;
	}

	private void RadioControl_Load(object sender, EventArgs e)
	{
		radioButton1.AutoCheck = true;
		radioButton2.AutoCheck = true;
		radioButton3.AutoCheck = true;
	}

	public string GetCheckedValue()
	{
		if (radioButton1.Checked)
		{
			return radioButton1.Text;
		}
		if (radioButton2.Checked)
		{
			return radioButton2.Text;
		}
		if (radioButton3.Checked)
		{
			return radioButton3.Text;
		}
		return null;
	}

	public void SetDataBinding(object dataSource, string dataMember)
	{
		if (base.DataBindings["Tag"] != null)
		{
			return;
		}
		base.DataBindings.Add("Tag", dataSource, dataMember);
		if (dataSource is DataTable dataTable && dataTable != null && dataTable.Rows?.Count > 0)
		{
			string text = dataTable.Rows[0].Field<string>(dataMember) ?? "";
			if (text.Equals(radioButton1.Text))
			{
				radioButton1.AutoCheck = true;
				radioButton1.Checked = true;
			}
			else if (text.Equals(radioButton2.Text))
			{
				radioButton2.AutoCheck = true;
				radioButton2.Checked = true;
			}
			else if (text.Equals(radioButton3.Text))
			{
				radioButton3.AutoCheck = true;
				radioButton3.Checked = true;
			}
		}
	}

	private void radioButton1_CheckedChanged(object sender, EventArgs e)
	{
		RadioButton radioButton = sender as RadioButton;
		if (radioButton.Checked)
		{
			base.Tag = radioButton.Text;
		}
	}

	public void ResetStatus()
	{
		radioButton1.Checked = false;
		radioButton2.Checked = false;
		radioButton3.Checked = false;
	}

	private void panel1_FontChanged(object sender, EventArgs e)
	{
		radioButton2.Left = radioButton1.Right + 5;
		if (string.IsNullOrWhiteSpace(Radio3Text))
		{
			base.Width = radioButton2.Right + 10;
			radioButton3.Visible = false;
		}
		else
		{
			radioButton3.Left = radioButton2.Right + 5;
			radioButton3.Visible = true;
			base.Width = radioButton3.Right + 30;
		}
		base.Height = radioButton1.Height + 5;
	}

	public void CheckTheRadio(string text)
	{
		if (text != null)
		{
			if (radioButton1.Text == text)
			{
				radioButton1.Checked = true;
			}
			else if (radioButton2.Text == text)
			{
				radioButton2.Checked = true;
			}
			else if (radioButton3.Text == text)
			{
				radioButton3.Checked = true;
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.panel1 = new System.Windows.Forms.Panel();
		this.radioButton3 = new System.Windows.Forms.RadioButton();
		this.radioButton2 = new System.Windows.Forms.RadioButton();
		this.radioButton1 = new System.Windows.Forms.RadioButton();
		this.panel1.SuspendLayout();
		base.SuspendLayout();
		this.panel1.Controls.Add(this.radioButton3);
		this.panel1.Controls.Add(this.radioButton2);
		this.panel1.Controls.Add(this.radioButton1);
		this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.panel1.Location = new System.Drawing.Point(0, 0);
		this.panel1.Name = "panel1";
		this.panel1.Size = new System.Drawing.Size(260, 30);
		this.panel1.TabIndex = 0;
		this.panel1.FontChanged += new System.EventHandler(panel1_FontChanged);
		this.radioButton3.AutoCheck = false;
		this.radioButton3.AutoSize = true;
		this.radioButton3.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
		this.radioButton3.Location = new System.Drawing.Point(159, 5);
		this.radioButton3.Name = "radioButton3";
		this.radioButton3.Size = new System.Drawing.Size(98, 22);
		this.radioButton3.TabIndex = 0;
		this.radioButton3.Text = "&占位的非联合&";
		this.radioButton3.UseVisualStyleBackColor = true;
		this.radioButton3.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
		this.radioButton2.AutoCheck = false;
		this.radioButton2.AutoSize = true;
		this.radioButton2.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
		this.radioButton2.Location = new System.Drawing.Point(83, 5);
		this.radioButton2.Name = "radioButton2";
		this.radioButton2.Size = new System.Drawing.Size(62, 22);
		this.radioButton2.TabIndex = 0;
		this.radioButton2.Text = "不满足";
		this.radioButton2.UseVisualStyleBackColor = true;
		this.radioButton2.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
		this.radioButton1.AutoCheck = false;
		this.radioButton1.AutoSize = true;
		this.radioButton1.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
		this.radioButton1.Location = new System.Drawing.Point(16, 5);
		this.radioButton1.Name = "radioButton1";
		this.radioButton1.Size = new System.Drawing.Size(50, 22);
		this.radioButton1.TabIndex = 0;
		this.radioButton1.Text = "满足";
		this.radioButton1.UseVisualStyleBackColor = true;
		this.radioButton1.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.Controls.Add(this.panel1);
		base.Name = "RadioControl";
		base.Size = new System.Drawing.Size(260, 30);
		base.Load += new System.EventHandler(RadioControl_Load);
		this.panel1.ResumeLayout(false);
		this.panel1.PerformLayout();
		base.ResumeLayout(false);
	}
}
