#define DEBUG
using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Design;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using WinformsUserControl.Args;
using WinformsUserControl.Collections;
using WinformsUserControl.Common;
using WinformsUserControl.Controls;
using WinformsUserControl.Controls.Base;
using WinformsUserControl.Controls.Common;
using WinformsUserControl.Enums;
using WinformsUserControl.Helpers;
using WinformsUserControl.Properties;
using WinformsUserControl.Styles;
using WinformsUserControl.UserControls;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("WinformsUserControl")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("WinformsUserControl")]
[assembly: AssemblyCopyright("Copyright ©  2023")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("20348a86-d672-49c7-a7b5-0faba2d7f432")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace WinformsUserControl.Properties
{
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "********")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		private static ResourceManager resourceMan;

		private static CultureInfo resourceCulture;

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (resourceMan == null)
				{
					ResourceManager resourceManager = new ResourceManager("WinformsUserControl.Properties.Resources", typeof(Resources).Assembly);
					resourceMan = resourceManager;
				}
				return resourceMan;
			}
		}

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return resourceCulture;
			}
			set
			{
				resourceCulture = value;
			}
		}

		internal static Bitmap backward_selected
		{
			get
			{
				object @object = ResourceManager.GetObject("backward_selected", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap backward_unselected
		{
			get
			{
				object @object = ResourceManager.GetObject("backward_unselected", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap forward_selected
		{
			get
			{
				object @object = ResourceManager.GetObject("forward_selected", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap forward_unselected
		{
			get
			{
				object @object = ResourceManager.GetObject("forward_unselected", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap next
		{
			get
			{
				object @object = ResourceManager.GetObject("next", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap pre
		{
			get
			{
				object @object = ResourceManager.GetObject("pre", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal Resources()
		{
		}
	}
}
namespace WinformsUserControl.Helpers
{
	public class GraphicsPathHelper
	{
		public static GraphicsPath CreatePath(Rectangle rect, int radius, RoundStyle style, bool correction)
		{
			GraphicsPath graphicsPath = new GraphicsPath();
			int num = (correction ? 1 : 0);
			switch (style)
			{
			case RoundStyle.None:
				graphicsPath.AddRectangle(rect);
				break;
			case RoundStyle.All:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				break;
			case RoundStyle.Left:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right - num, rect.Bottom - num);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				break;
			case RoundStyle.Right:
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddLine(rect.X, rect.Bottom - num, rect.X, rect.Y);
				break;
			case RoundStyle.Top:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddLine(rect.Right - num, rect.Bottom - num, rect.X, rect.Bottom - num);
				break;
			case RoundStyle.Bottom:
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				graphicsPath.AddLine(rect.X, rect.Y, rect.Right - num, rect.Y);
				break;
			}
			graphicsPath.CloseFigure();
			return graphicsPath;
		}
	}
}
namespace WinformsUserControl.Enums
{
	public enum ControlState
	{
		Normal,
		Hover,
		Pressed
	}
	public enum RoundStyle
	{
		None,
		Top,
		Bottom,
		Left,
		Right,
		All
	}
}
namespace WinformsUserControl.Styles
{
	internal class ControlStyle
	{
		private int fontSize;

		private Color foreColor;

		private Color backColor;

		private int width;

		private int height;

		public int FontSize
		{
			get
			{
				return fontSize;
			}
			set
			{
				fontSize = value;
			}
		}

		public Color ForeColor
		{
			get
			{
				return foreColor;
			}
			set
			{
				foreColor = value;
			}
		}

		public Color BackColor
		{
			get
			{
				return backColor;
			}
			set
			{
				backColor = value;
			}
		}

		public int Width
		{
			get
			{
				return width;
			}
			set
			{
				width = value;
			}
		}

		public int Height
		{
			get
			{
				return height;
			}
			set
			{
				height = value;
			}
		}
	}
	internal class ControlStyleDictionary<Key>
	{
		private Dictionary<Key, ControlStyle> controlStyleDict = new Dictionary<Key, ControlStyle>();

		public void Add(Key name, ControlStyle style)
		{
			controlStyleDict[name] = style;
		}

		public bool Contain(Key name)
		{
			return controlStyleDict.ContainsKey(name);
		}

		public ControlStyle TryGetValue(Key name)
		{
			ControlStyle value = null;
			controlStyleDict.TryGetValue(name, out value);
			return value;
		}
	}
}
namespace WinformsUserControl.UserControls
{
	public class SearchTabControl : UserControl
	{
		private readonly Color defaultTabSelectForeColor = Color.FromArgb(230, 245, 244);

		private readonly Color defaultTabSelectBgColor = Color.FromArgb(137, 170, 235);

		private string searchLableText;

		private TabInfoCollection tabPages;

		private ControlStyleDictionary<Control> controlStyleDict = new ControlStyleDictionary<Control>();

		private bool showSearchArea = true;

		private IContainer components = null;

		private FlowLayoutPanel panel2;

		private Button clearBtn;

		private Button searchBtn;

		private TextBoxLineInput textBoxLineInput1;

		private TextBox searchTextBox;

		private Button button2;

		private Panel tabControlSelectorOutLayout;

		private FlowLayoutPanel tabControlSelector;

		private Panel panel5;

		private Button ForwardBtn;

		private Button BackwardBtn;

		[Description("搜索事件")]
		public Func<string, ICollection<TabInfo>> SearchEven { get; set; }

		[Description("点击tab事件")]
		public TabEventHandler TabClickEven { get; set; }

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchLableText")]
		[Description("字体颜色")]
		[DefaultValue(typeof(Color), "Black")]
		public Color SearchLableForeColor
		{
			get
			{
				return textBoxLineInput1.ForeColor;
			}
			set
			{
				textBoxLineInput1.ForeColor = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchLableText")]
		[Description("文本内容")]
		[DefaultValue("搜索栏")]
		public string SearchLableText
		{
			get
			{
				return searchLableText;
			}
			set
			{
				searchLableText = (string.IsNullOrEmpty(value) ? "搜索栏" : value);
				textBoxLineInput1.LabelTitle = searchLableText;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchLableText")]
		[Description("字体")]
		public Font SearchLableFont
		{
			get
			{
				return textBoxLineInput1.Font;
			}
			set
			{
				textBoxLineInput1.Font = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchLableText")]
		[Description("宽度")]
		public int SearchLableWidth
		{
			get
			{
				return textBoxLineInput1.LabelTitleWidth;
			}
			set
			{
				textBoxLineInput1.LabelTitleWidth = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchTextBox")]
		[Description("字体颜色")]
		[DefaultValue(typeof(Color), "Black")]
		public Color SearchTextBoxForeColor
		{
			get
			{
				return searchTextBox.ForeColor;
			}
			set
			{
				searchTextBox.ForeColor = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchTextBox")]
		[Description("文本内容")]
		[DefaultValue("搜索栏")]
		public string SearchTextBoxText
		{
			get
			{
				return searchTextBox.Text;
			}
			set
			{
				searchTextBox.Text = (string.IsNullOrEmpty(value) ? "" : value);
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchTextBox")]
		[Description("字体")]
		public Font SearchTextBoxFont
		{
			get
			{
				return searchTextBox.Font;
			}
			set
			{
				searchTextBox.Font = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchTextBox")]
		[Description("宽度")]
		public int SearchTextBoxWidth
		{
			get
			{
				return searchTextBox.Width;
			}
			set
			{
				searchTextBox.Width = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchBtn")]
		[Description("字体颜色")]
		[DefaultValue(typeof(Color), "Black")]
		public Color SearchBtnForeColor
		{
			get
			{
				return searchBtn.ForeColor;
			}
			set
			{
				searchBtn.ForeColor = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchBtn")]
		[Description("文本内容")]
		[DefaultValue("搜索栏")]
		public string SearchBtnText
		{
			get
			{
				return searchBtn.Text;
			}
			set
			{
				searchBtn.Text = (string.IsNullOrEmpty(value) ? "" : value);
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchBtn")]
		[Description("字体")]
		public Font SearchBtnFont
		{
			get
			{
				return searchBtn.Font;
			}
			set
			{
				searchBtn.Font = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchBtn")]
		[Description("宽度")]
		public int SearchBtnWidth
		{
			get
			{
				return searchBtn.Width;
			}
			set
			{
				searchBtn.Width = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchBtn")]
		[Description("可见性")]
		public bool SearchBtnVisible
		{
			get
			{
				return searchBtn.Visible;
			}
			set
			{
				searchBtn.Visible = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("ClearBtn")]
		[Description("字体颜色")]
		[DefaultValue(typeof(Color), "Black")]
		public Color ClearBtnForeColor
		{
			get
			{
				return clearBtn.ForeColor;
			}
			set
			{
				clearBtn.ForeColor = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("ClearBtn")]
		[Description("文本内容")]
		[DefaultValue("搜索栏")]
		public string ClearBtnText
		{
			get
			{
				return clearBtn.Text;
			}
			set
			{
				clearBtn.Text = (string.IsNullOrEmpty(value) ? "" : value);
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("ClearBtn")]
		[Description("字体")]
		public Font ClearBtnFont
		{
			get
			{
				return clearBtn.Font;
			}
			set
			{
				clearBtn.Font = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("ClearBtn")]
		[Description("宽度")]
		public int ClearBtnWidth
		{
			get
			{
				return clearBtn.Width;
			}
			set
			{
				clearBtn.Width = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("ClearBtn")]
		[Description("可见性")]
		public bool ClearBtnVisible
		{
			get
			{
				return clearBtn.Visible;
			}
			set
			{
				clearBtn.Visible = value;
			}
		}

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
		[Browsable(true)]
		[Category("TabPageList")]
		[Description("TabPageList所在内容")]
		public TabInfoCollection TabPages
		{
			get
			{
				return tabPages;
			}
			set
			{
				tabPages = value;
			}
		}

		[Browsable(true)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
		[Category("SearchArea")]
		[Description("是否展示搜索区域")]
		public bool ShowSearchArea
		{
			get
			{
				return showSearchArea;
			}
			set
			{
				showSearchArea = value;
				panel2.Visible = showSearchArea;
			}
		}

		public SearchTabControl()
		{
			InitializeComponent();
			IList<TabInfo> list = new List<TabInfo>
			{
				new TabInfo
				{
					Name = "winfTab_001",
					Text = "winfTab_001"
				},
				new TabInfo
				{
					Name = "winfTab_002",
					Text = "winfTab_002"
				},
				new TabInfo
				{
					Name = "winfTab_003",
					Text = "winfTab_003"
				}
			};
			tabPages = new TabInfoCollection(list, this);
		}

		private void searchBtn_Click(object sender, EventArgs e)
		{
			ICollection<TabInfo> collection = SearchEven?.Invoke(searchTextBox.Text);
			if (collection != null && collection.Any())
			{
				tabPages.Clear();
				foreach (TabInfo item in collection)
				{
					tabPages.Add(item);
				}
			}
			ShowTabControlSelector(tabPages);
			ForwardBtn.Tag = 1;
			BackwardBtn.Tag = 1;
		}

		private void ShowTabControlSelector(TabInfoCollection list)
		{
			tabControlSelector.Controls.Clear();
			int num = 0;
			foreach (TabInfo item in list)
			{
				Button button = new Button
				{
					Height = 25
				};
				button.AutoSize = true;
				button.AutoSizeMode = AutoSizeMode.GrowAndShrink;
				if (!string.IsNullOrWhiteSpace(item.Name))
				{
					button.Name = item.Name;
				}
				button.Text = item.Text;
				button.Anchor = AnchorStyles.Top | AnchorStyles.Left;
				button.FlatStyle = FlatStyle.Flat;
				button.Font = button2.Font;
				button.FlatAppearance.MouseDownBackColor = button2.FlatAppearance.MouseDownBackColor;
				button.FlatAppearance.MouseOverBackColor = button2.FlatAppearance.MouseOverBackColor;
				button.FlatAppearance.BorderSize = 0;
				button.Padding = new Padding(1, 0, 0, 0);
				button.Tag = item;
				button.Top = 0;
				button.Click += Button_Click;
				tabControlSelector.Controls.Add(button);
				num += button.ClientSize.Width + 5;
				controlStyleDict.Add(button, new ControlStyle
				{
					BackColor = button.BackColor,
					ForeColor = button.ForeColor
				});
			}
			int num2 = 3;
			int num3 = 30;
			while (tabControlSelector.Width < num && --num2 > 0)
			{
				num3 += 35;
				num -= tabControlSelector.Width;
			}
			tabControlSelectorOutLayout.Height = num3;
			if (tabControlSelector.Controls.Count > 0)
			{
				Button_Click(tabControlSelector.Controls[0], null);
			}
		}

		private void Button_Click(object sender, EventArgs e)
		{
			try
			{
				ControlCollection controls = tabControlSelector.Controls;
				foreach (Control item in controls)
				{
					item.BackColor = controlStyleDict.TryGetValue(item).BackColor;
					item.ForeColor = controlStyleDict.TryGetValue(item).ForeColor;
				}
				if (sender is Button button)
				{
					button.BackColor = defaultTabSelectForeColor;
					button.ForeColor = defaultTabSelectBgColor;
					int num = tabControlSelector.Controls.IndexOf(button);
					TabClickEven?.Invoke(button, new TabEventArgs((TabInfo)button.Tag));
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		private void clearBtn_Click(object sender, EventArgs e)
		{
			searchTextBox.Clear();
			searchBtn_Click(sender, e);
		}

		private void BackwardBtn_Click(object sender, EventArgs e)
		{
			TabSkip_Click(sender, e);
		}

		private void ForwardBtn_Click(object sender, EventArgs e)
		{
			TabSkip_Click(sender, e);
		}

		private void TabSkip_Click(object sender, EventArgs e)
		{
			try
			{
				Button button = sender as Button;
				bool flag = button.Name == "ForwardBtn";
				int num = 0;
				ControlCollection controls = tabControlSelector.Controls;
				foreach (Control item in controls)
				{
					if (item.Visible)
					{
						num++;
					}
				}
				if (num <= 0)
				{
					return;
				}
				if (flag)
				{
					button.BackgroundImage = (button.Focused ? Resources.forward_selected : Resources.forward_unselected);
					BackwardBtn.BackgroundImage = Resources.backward_unselected;
				}
				else
				{
					ForwardBtn.BackgroundImage = Resources.forward_unselected;
					button.BackgroundImage = (button.Focused ? Resources.backward_selected : Resources.backward_unselected);
				}
				if (!flag || num >= 3)
				{
					int num2 = Convert.ToInt32(button.Tag?.ToString() ?? "1");
					if (flag && num2 < tabControlSelector.Controls.Count)
					{
						tabControlSelector.Controls[num2].Visible = false;
						num2++;
					}
					else if (!flag && num2 >= 1)
					{
						tabControlSelector.Controls[num2 - 1].Visible = true;
						num2--;
					}
					ForwardBtn.Tag = num2;
					BackwardBtn.Tag = num2;
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		private void SearchTabControl_Load(object sender, EventArgs e)
		{
			ShowTabControlSelector(tabPages);
			searchBtn_Click(sender, e);
		}

		private void panel2_VisibleChanged(object sender, EventArgs e)
		{
			int num = (panel2.Visible ? panel2.Height : (-panel2.Height));
			base.Height += num;
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.panel2 = new System.Windows.Forms.FlowLayoutPanel();
			this.searchBtn = new System.Windows.Forms.Button();
			this.clearBtn = new System.Windows.Forms.Button();
			this.button2 = new System.Windows.Forms.Button();
			this.tabControlSelectorOutLayout = new System.Windows.Forms.Panel();
			this.tabControlSelector = new System.Windows.Forms.FlowLayoutPanel();
			this.panel5 = new System.Windows.Forms.Panel();
			this.ForwardBtn = new System.Windows.Forms.Button();
			this.BackwardBtn = new System.Windows.Forms.Button();
			this.textBoxLineInput1 = new WinformsUserControl.Controls.TextBoxLineInput();
			this.searchTextBox = new System.Windows.Forms.TextBox();
			this.panel2.SuspendLayout();
			this.tabControlSelectorOutLayout.SuspendLayout();
			this.panel5.SuspendLayout();
			this.textBoxLineInput1.SuspendLayout();
			base.SuspendLayout();
			this.panel2.BackColor = System.Drawing.Color.White;
			this.panel2.Controls.Add(this.textBoxLineInput1);
			this.panel2.Controls.Add(this.searchBtn);
			this.panel2.Controls.Add(this.clearBtn);
			this.panel2.Controls.Add(this.button2);
			this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
			this.panel2.Location = new System.Drawing.Point(0, 0);
			this.panel2.Name = "panel2";
			this.panel2.Size = new System.Drawing.Size(1081, 46);
			this.panel2.TabIndex = 3;
			this.panel2.VisibleChanged += new System.EventHandler(panel2_VisibleChanged);
			this.searchBtn.BackColor = System.Drawing.Color.Teal;
			this.searchBtn.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(0, 192, 0);
			this.searchBtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
			this.searchBtn.Font = new System.Drawing.Font("宋体", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.searchBtn.ForeColor = System.Drawing.Color.White;
			this.searchBtn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.searchBtn.Location = new System.Drawing.Point(644, 12);
			this.searchBtn.Margin = new System.Windows.Forms.Padding(15, 12, 3, 3);
			this.searchBtn.Name = "searchBtn";
			this.searchBtn.Size = new System.Drawing.Size(59, 30);
			this.searchBtn.TabIndex = 119;
			this.searchBtn.Tag = "";
			this.searchBtn.Text = "搜索";
			this.searchBtn.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.searchBtn.UseVisualStyleBackColor = false;
			this.searchBtn.Click += new System.EventHandler(searchBtn_Click);
			this.clearBtn.BackColor = System.Drawing.Color.LightSlateGray;
			this.clearBtn.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(0, 192, 0);
			this.clearBtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
			this.clearBtn.Font = new System.Drawing.Font("宋体", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.clearBtn.ForeColor = System.Drawing.Color.White;
			this.clearBtn.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.clearBtn.Location = new System.Drawing.Point(721, 12);
			this.clearBtn.Margin = new System.Windows.Forms.Padding(15, 12, 3, 3);
			this.clearBtn.Name = "clearBtn";
			this.clearBtn.Size = new System.Drawing.Size(59, 30);
			this.clearBtn.TabIndex = 120;
			this.clearBtn.Tag = "";
			this.clearBtn.Text = "清除";
			this.clearBtn.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.clearBtn.UseVisualStyleBackColor = false;
			this.clearBtn.Click += new System.EventHandler(clearBtn_Click);
			this.button2.FlatAppearance.BorderSize = 0;
			this.button2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.Teal;
			this.button2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(0, 192, 192);
			this.button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
			this.button2.ForeColor = System.Drawing.Color.Red;
			this.button2.Location = new System.Drawing.Point(786, 3);
			this.button2.Name = "button2";
			this.button2.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.button2.Size = new System.Drawing.Size(108, 23);
			this.button2.TabIndex = 6;
			this.button2.Text = "仅占位";
			this.button2.UseVisualStyleBackColor = false;
			this.button2.Visible = false;
			this.tabControlSelectorOutLayout.BackColor = System.Drawing.Color.White;
			this.tabControlSelectorOutLayout.Controls.Add(this.tabControlSelector);
			this.tabControlSelectorOutLayout.Controls.Add(this.panel5);
			this.tabControlSelectorOutLayout.Dock = System.Windows.Forms.DockStyle.Top;
			this.tabControlSelectorOutLayout.Location = new System.Drawing.Point(0, 46);
			this.tabControlSelectorOutLayout.Name = "tabControlSelectorOutLayout";
			this.tabControlSelectorOutLayout.Size = new System.Drawing.Size(1081, 30);
			this.tabControlSelectorOutLayout.TabIndex = 18;
			this.tabControlSelector.BackColor = System.Drawing.Color.White;
			this.tabControlSelector.Dock = System.Windows.Forms.DockStyle.Fill;
			this.tabControlSelector.Location = new System.Drawing.Point(0, 0);
			this.tabControlSelector.Name = "tabControlSelector";
			this.tabControlSelector.Size = new System.Drawing.Size(1040, 30);
			this.tabControlSelector.TabIndex = 6;
			this.panel5.Controls.Add(this.ForwardBtn);
			this.panel5.Controls.Add(this.BackwardBtn);
			this.panel5.Dock = System.Windows.Forms.DockStyle.Right;
			this.panel5.Location = new System.Drawing.Point(1040, 0);
			this.panel5.Name = "panel5";
			this.panel5.Size = new System.Drawing.Size(41, 30);
			this.panel5.TabIndex = 8;
			this.ForwardBtn.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
			this.ForwardBtn.BackColor = System.Drawing.Color.White;
			this.ForwardBtn.BackgroundImage = WinformsUserControl.Properties.Resources.forward_unselected;
			this.ForwardBtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
			this.ForwardBtn.FlatAppearance.BorderSize = 0;
			this.ForwardBtn.FlatAppearance.CheckedBackColor = System.Drawing.Color.Green;
			this.ForwardBtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
			this.ForwardBtn.ForeColor = System.Drawing.Color.LightGray;
			this.ForwardBtn.Location = new System.Drawing.Point(23, 6);
			this.ForwardBtn.Name = "ForwardBtn";
			this.ForwardBtn.Size = new System.Drawing.Size(16, 20);
			this.ForwardBtn.TabIndex = 25;
			this.ForwardBtn.Tag = "0";
			this.ForwardBtn.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
			this.ForwardBtn.UseVisualStyleBackColor = false;
			this.ForwardBtn.Click += new System.EventHandler(ForwardBtn_Click);
			this.BackwardBtn.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
			this.BackwardBtn.BackColor = System.Drawing.Color.White;
			this.BackwardBtn.BackgroundImage = WinformsUserControl.Properties.Resources.backward_unselected;
			this.BackwardBtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
			this.BackwardBtn.FlatAppearance.BorderSize = 0;
			this.BackwardBtn.FlatAppearance.CheckedBackColor = System.Drawing.Color.Green;
			this.BackwardBtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
			this.BackwardBtn.ForeColor = System.Drawing.Color.Transparent;
			this.BackwardBtn.Location = new System.Drawing.Point(3, 6);
			this.BackwardBtn.Name = "BackwardBtn";
			this.BackwardBtn.Size = new System.Drawing.Size(16, 20);
			this.BackwardBtn.TabIndex = 24;
			this.BackwardBtn.Tag = "1";
			this.BackwardBtn.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText;
			this.BackwardBtn.UseVisualStyleBackColor = false;
			this.BackwardBtn.Click += new System.EventHandler(BackwardBtn_Click);
			this.textBoxLineInput1.BackColor = System.Drawing.Color.Transparent;
			this.textBoxLineInput1.Controls.Add(this.searchTextBox);
			this.textBoxLineInput1.Font = new System.Drawing.Font("宋体", 9f);
			this.textBoxLineInput1.ForeColor = System.Drawing.SystemColors.HotTrack;
			this.textBoxLineInput1.InitName = "lineInput";
			this.textBoxLineInput1.InputBox = this.searchTextBox;
			this.textBoxLineInput1.LabelTitle = "文件名称";
			this.textBoxLineInput1.LabelTitleHeight = 30;
			this.textBoxLineInput1.LabelTitleTop = 6;
			this.textBoxLineInput1.LabelTitleWidth = 70;
			this.textBoxLineInput1.Location = new System.Drawing.Point(15, 5);
			this.textBoxLineInput1.Margin = new System.Windows.Forms.Padding(15, 5, 3, 3);
			this.textBoxLineInput1.MRequired = false;
			this.textBoxLineInput1.Name = "textBoxLineInput1";
			this.textBoxLineInput1.RemarkPanel = null;
			this.textBoxLineInput1.Size = new System.Drawing.Size(611, 38);
			this.textBoxLineInput1.TabIndex = 118;
			this.searchTextBox.BackColor = System.Drawing.Color.White;
			this.searchTextBox.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.searchTextBox.Font = new System.Drawing.Font("宋体", 11f);
			this.searchTextBox.Location = new System.Drawing.Point(95, 10);
			this.searchTextBox.Margin = new System.Windows.Forms.Padding(0, 10, 3, 3);
			this.searchTextBox.Name = "searchTextBox";
			this.searchTextBox.Size = new System.Drawing.Size(507, 24);
			this.searchTextBox.TabIndex = 2;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.Controls.Add(this.tabControlSelectorOutLayout);
			base.Controls.Add(this.panel2);
			base.Name = "SearchTabControl";
			base.Size = new System.Drawing.Size(1081, 80);
			base.Load += new System.EventHandler(SearchTabControl_Load);
			this.panel2.ResumeLayout(false);
			this.tabControlSelectorOutLayout.ResumeLayout(false);
			this.panel5.ResumeLayout(false);
			this.textBoxLineInput1.ResumeLayout(false);
			this.textBoxLineInput1.PerformLayout();
			base.ResumeLayout(false);
		}
	}
	public class RadioControl : UserControl
	{
		private string radio1Text = "有效";

		private string radio2Text = "无效";

		private string radio3Text = "";

		private IContainer components = null;

		private Panel panel1;

		private RadioButton radioButton2;

		private RadioButton radioButton1;

		private RadioButton radioButton3;

		public string Radio1Text
		{
			get
			{
				return radio1Text;
			}
			set
			{
				radio1Text = value;
				radioButton1.Text = Radio1Text;
				ResetControlsPos();
			}
		}

		public string Radio2Text
		{
			get
			{
				return radio2Text;
			}
			set
			{
				radio2Text = value;
				radioButton2.Text = radio2Text;
				ResetControlsPos();
			}
		}

		public string Radio3Text
		{
			get
			{
				return radio3Text;
			}
			set
			{
				radio3Text = value;
				radioButton3.Text = radio3Text;
				ResetControlsPos();
			}
		}

		public RadioControl()
		{
			InitializeComponent();
		}

		public void ResetControlsPos()
		{
			radioButton2.Left = radioButton1.Right + 5;
			if (string.IsNullOrWhiteSpace(Radio3Text))
			{
				base.Width = radioButton2.Right + 10;
				radioButton3.Visible = false;
			}
			else
			{
				radioButton3.Left = radioButton2.Right + 5;
				radioButton3.Visible = true;
				base.Width = radioButton3.Right + 10;
			}
			base.Height = radioButton1.Height + 5;
		}

		private void RadioControl_Load(object sender, EventArgs e)
		{
			radioButton1.AutoCheck = true;
			radioButton2.AutoCheck = true;
			radioButton3.AutoCheck = true;
		}

		public string GetCheckedValue()
		{
			if (radioButton1.Checked)
			{
				return radioButton1.Text;
			}
			if (radioButton2.Checked)
			{
				return radioButton2.Text;
			}
			if (radioButton3.Checked)
			{
				return radioButton3.Text;
			}
			return null;
		}

		public void SetDataBinding(object dataSource, string dataMember)
		{
			if (base.DataBindings["Tag"] != null)
			{
				return;
			}
			base.DataBindings.Add("Tag", dataSource, dataMember);
			if (dataSource is DataTable dataTable && dataTable != null && dataTable.Rows?.Count > 0)
			{
				string text = dataTable.Rows[0].Field<string>(dataMember) ?? "";
				if (text.Equals(radioButton1.Text))
				{
					radioButton1.AutoCheck = true;
					radioButton1.Checked = true;
				}
				else if (text.Equals(radioButton2.Text))
				{
					radioButton2.AutoCheck = true;
					radioButton2.Checked = true;
				}
				else if (text.Equals(radioButton3.Text))
				{
					radioButton3.AutoCheck = true;
					radioButton3.Checked = true;
				}
			}
		}

		private void radioButton1_CheckedChanged(object sender, EventArgs e)
		{
			RadioButton radioButton = sender as RadioButton;
			if (radioButton.Checked)
			{
				base.Tag = radioButton.Text;
			}
		}

		public void ResetStatus()
		{
			radioButton1.Checked = false;
			radioButton2.Checked = false;
			radioButton3.Checked = false;
		}

		private void panel1_FontChanged(object sender, EventArgs e)
		{
			radioButton2.Left = radioButton1.Right + 5;
			if (string.IsNullOrWhiteSpace(Radio3Text))
			{
				base.Width = radioButton2.Right + 10;
				radioButton3.Visible = false;
			}
			else
			{
				radioButton3.Left = radioButton2.Right + 5;
				radioButton3.Visible = true;
				base.Width = radioButton3.Right + 30;
			}
			base.Height = radioButton1.Height + 5;
		}

		public void CheckTheRadio(string text)
		{
			if (text != null)
			{
				if (radioButton1.Text == text)
				{
					radioButton1.Checked = true;
				}
				else if (radioButton2.Text == text)
				{
					radioButton2.Checked = true;
				}
				else if (radioButton3.Text == text)
				{
					radioButton3.Checked = true;
				}
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.panel1 = new System.Windows.Forms.Panel();
			this.radioButton3 = new System.Windows.Forms.RadioButton();
			this.radioButton2 = new System.Windows.Forms.RadioButton();
			this.radioButton1 = new System.Windows.Forms.RadioButton();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.panel1.Controls.Add(this.radioButton3);
			this.panel1.Controls.Add(this.radioButton2);
			this.panel1.Controls.Add(this.radioButton1);
			this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panel1.Location = new System.Drawing.Point(0, 0);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(260, 30);
			this.panel1.TabIndex = 0;
			this.panel1.FontChanged += new System.EventHandler(panel1_FontChanged);
			this.radioButton3.AutoCheck = false;
			this.radioButton3.AutoSize = true;
			this.radioButton3.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.radioButton3.Location = new System.Drawing.Point(159, 5);
			this.radioButton3.Name = "radioButton3";
			this.radioButton3.Size = new System.Drawing.Size(98, 22);
			this.radioButton3.TabIndex = 0;
			this.radioButton3.Text = "&占位的非联合&";
			this.radioButton3.UseVisualStyleBackColor = true;
			this.radioButton3.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
			this.radioButton2.AutoCheck = false;
			this.radioButton2.AutoSize = true;
			this.radioButton2.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.radioButton2.Location = new System.Drawing.Point(83, 5);
			this.radioButton2.Name = "radioButton2";
			this.radioButton2.Size = new System.Drawing.Size(62, 22);
			this.radioButton2.TabIndex = 0;
			this.radioButton2.Text = "不满足";
			this.radioButton2.UseVisualStyleBackColor = true;
			this.radioButton2.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
			this.radioButton1.AutoCheck = false;
			this.radioButton1.AutoSize = true;
			this.radioButton1.Font = new System.Drawing.Font("思源黑体 Normal", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 134);
			this.radioButton1.Location = new System.Drawing.Point(16, 5);
			this.radioButton1.Name = "radioButton1";
			this.radioButton1.Size = new System.Drawing.Size(50, 22);
			this.radioButton1.TabIndex = 0;
			this.radioButton1.Text = "满足";
			this.radioButton1.UseVisualStyleBackColor = true;
			this.radioButton1.CheckedChanged += new System.EventHandler(radioButton1_CheckedChanged);
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.Controls.Add(this.panel1);
			base.Name = "RadioControl";
			base.Size = new System.Drawing.Size(260, 30);
			base.Load += new System.EventHandler(RadioControl_Load);
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			base.ResumeLayout(false);
		}
	}
	public class UploadUserControl : UserControl
	{
		private bool init = false;

		private int sourceWidth;

		private int sourceHeight;

		private int singleWidth = 135;

		private int singleHeight = 135;

		private int displayRowCount = 3;

		private int displayColumnCount = 5;

		private int imgCount = 10;

		private double imgSize = 0.5;

		private int pdfCount = 1;

		private double pdfSize = 10.0;

		private Func<List<Control>> _searchDataAction;

		private IContainer components = null;

		private Button button1;

		protected FlowLayoutPanel flowLayoutPanel1;

		public Func<string, int, double, int, double, List<Control>> UploadAction { get; set; }

		public Func<List<Control>> SearchDataAction
		{
			get
			{
				return _searchDataAction;
			}
			set
			{
				_searchDataAction = value;
				List<Control> subControls = _searchDataAction?.Invoke();
				SetSubControls(subControls);
			}
		}

		[Browsable(true)]
		[Description("展示的行数")]
		public int DisplayRowCount
		{
			get
			{
				return displayRowCount;
			}
			set
			{
				displayRowCount = value;
			}
		}

		[Browsable(true)]
		[Description("展示的列数")]
		public int DisplayColumnCount
		{
			get
			{
				return displayColumnCount;
			}
			set
			{
				displayColumnCount = value;
			}
		}

		[Browsable(true)]
		[Description("上传按钮的宽度")]
		public int SingleWidth
		{
			get
			{
				return singleWidth;
			}
			set
			{
				if (value < 80 || value > 800)
				{
					throw new ArgumentOutOfRangeException("SingleWidth的值范围为[80-800]");
				}
				singleWidth = value;
				button1.Width = (int)((double)value * 0.9);
				if (!init)
				{
					base.Width = singleWidth;
				}
			}
		}

		[Browsable(true)]
		[Description("上传按钮的高度")]
		public int SingleHeight
		{
			get
			{
				return singleHeight;
			}
			set
			{
				if (value < 80 || value > 800)
				{
					throw new ArgumentOutOfRangeException("SingleHeight的值范围为[80-800]");
				}
				singleHeight = value;
				button1.Height = (int)((double)value * 0.9);
				if (!init)
				{
					base.Height = SingleHeight;
				}
			}
		}

		[Browsable(true)]
		[Description("上传的图片总数")]
		public int ImgCount
		{
			get
			{
				return imgCount;
			}
			set
			{
				imgCount = value;
			}
		}

		[Browsable(true)]
		[Description("上传的单个图片大小限制")]
		public double ImgSize
		{
			get
			{
				return imgSize;
			}
			set
			{
				imgSize = value;
			}
		}

		[Browsable(true)]
		[Description("上传的PDF总数")]
		public int PdfCount
		{
			get
			{
				return pdfCount;
			}
			set
			{
				pdfCount = value;
			}
		}

		[Browsable(true)]
		[Description("上传的单个PDF大小限制")]
		public double PdfSize
		{
			get
			{
				return pdfSize;
			}
			set
			{
				pdfSize = value;
			}
		}

		public UploadUserControl()
		{
			InitializeComponent();
			if (!init)
			{
				base.Width = SingleWidth;
				base.Height = SingleHeight;
			}
			sourceWidth = base.Width;
			sourceHeight = base.Height;
			button1.Width = (int)((double)SingleWidth * 0.9);
			button1.Height = (int)((double)SingleHeight * 0.9);
		}

		public UploadUserControl(int imgCount, double imgSize, int pdfCount, double pdfSize)
			: this()
		{
			ImgCount = imgCount;
			ImgSize = imgSize;
			PdfCount = pdfCount;
			PdfSize = pdfSize;
		}

		private void UploadUserControl_Load(object sender, EventArgs e)
		{
			init = true;
		}

		public void SetSubControls(List<Control> controls)
		{
			try
			{
				if (controls == null)
				{
					return;
				}
				int num = controls.Count + flowLayoutPanel1.Controls.Count;
				base.Width = ((num >= DisplayColumnCount) ? (SingleWidth * DisplayColumnCount) : (SingleWidth * num));
				int num2 = num / DisplayColumnCount + ((num % DisplayColumnCount != 0) ? 1 : 0);
				int num3 = 0;
				int num4 = 0;
				foreach (Control control in controls)
				{
					decimal d = decimal.Divide(SingleWidth, control.Width);
					control.Width = SingleWidth;
					control.Height = (int)decimal.Multiply(control.Height, d);
					num3 = control.Width;
					num4 = control.Height;
					control.Margin = new Padding(0);
					flowLayoutPanel1.Controls.Add(control);
				}
				int num5 = ((num4 <= 0) ? SingleHeight : num4);
				base.Height = ((num2 >= DisplayRowCount) ? ((int)((float)(num5 * DisplayRowCount) * 1.1f)) : ((int)((float)(num5 * num2) * 1.1f)));
				button1.Visible = num <= pdfCount + imgCount;
				if (!button1.Visible)
				{
					base.Width -= (int)((double)button1.Width * 0.865);
				}
				else if (controls.Count == 1)
				{
					base.Width = sourceWidth;
					base.Height = sourceHeight;
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		private void button1_Click(object sender, EventArgs e)
		{
			try
			{
				int num = PdfCount + ImgCount;
				if (flowLayoutPanel1.Controls.Count - 1 >= num)
				{
					MessageBox.Show($"当前上传文件个数最多为{num}个", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
					return;
				}
				string text = "文件|*.pdf;*.PDF;*.png;*.jpg;*jpeg;";
				if (PdfCount <= 0)
				{
					text = "文件|*.png;*.jpg;*jpeg;";
				}
				if (ImgCount <= 0)
				{
					text = "文件|*.pdf;*.PDF;";
				}
				List<Control> list = UploadAction?.Invoke(text, PdfCount, PdfSize, ImgCount, ImgSize);
				if (list != null)
				{
					SetSubControls(list);
					MessageBox.Show("上传成功");
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		private void UploadUserControl_Resize(object sender, EventArgs e)
		{
		}

		public int RemoveTheFile(Control control)
		{
			int result = flowLayoutPanel1.Controls.IndexOf(control) - 1;
			flowLayoutPanel1.Controls.Remove(control);
			if (flowLayoutPanel1.Controls.Count <= 1)
			{
				button1.Visible = true;
			}
			return result;
		}

		public ControlCollection GetSubControls()
		{
			return flowLayoutPanel1.Controls;
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WinformsUserControl.UserControls.UploadUserControl));
			this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
			this.button1 = new System.Windows.Forms.Button();
			this.flowLayoutPanel1.SuspendLayout();
			base.SuspendLayout();
			this.flowLayoutPanel1.AutoScroll = true;
			this.flowLayoutPanel1.BackColor = System.Drawing.Color.FromArgb(244, 244, 244);
			this.flowLayoutPanel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
			this.flowLayoutPanel1.Controls.Add(this.button1);
			this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.flowLayoutPanel1.Location = new System.Drawing.Point(0, 0);
			this.flowLayoutPanel1.Name = "flowLayoutPanel1";
			this.flowLayoutPanel1.Size = new System.Drawing.Size(80, 80);
			this.flowLayoutPanel1.TabIndex = 0;
			this.button1.BackColor = System.Drawing.SystemColors.Control;
			this.button1.BackgroundImage = (System.Drawing.Image)resources.GetObject("button1.BackgroundImage");
			this.button1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
			this.button1.Location = new System.Drawing.Point(0, 0);
			this.button1.Margin = new System.Windows.Forms.Padding(0);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(80, 80);
			this.button1.TabIndex = 0;
			this.button1.UseVisualStyleBackColor = false;
			this.button1.Click += new System.EventHandler(button1_Click);
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.BackgroundImage = (System.Drawing.Image)resources.GetObject("$this.BackgroundImage");
			this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center;
			base.Controls.Add(this.flowLayoutPanel1);
			this.DoubleBuffered = true;
			base.Name = "UploadUserControl";
			base.Size = new System.Drawing.Size(80, 80);
			base.Load += new System.EventHandler(UploadUserControl_Load);
			base.Resize += new System.EventHandler(UploadUserControl_Resize);
			this.flowLayoutPanel1.ResumeLayout(false);
			base.ResumeLayout(false);
		}
	}
}
namespace WinformsUserControl.Controls
{
	public class ButtonExt : Button
	{
		private Color _baseColor = Color.FromArgb(0, 128, 128);

		private ControlState _controlState;

		private int _imageWidth = 30;

		private RoundStyle _roundStyle = RoundStyle.All;

		private int _radius = 10;

		private static readonly ContentAlignment RightAlignment = (ContentAlignment)1092;

		private static readonly ContentAlignment LeftAlignment = (ContentAlignment)273;

		[DefaultValue(typeof(Color), "0, 128, 128")]
		public Color BaseColor
		{
			get
			{
				return _baseColor;
			}
			set
			{
				_baseColor = value;
				Invalidate();
			}
		}

		[DefaultValue(18)]
		public int ImageWidth
		{
			get
			{
				return _imageWidth;
			}
			set
			{
				if (value != _imageWidth)
				{
					_imageWidth = ((value < 1) ? 1 : value);
					Invalidate();
				}
			}
		}

		[DefaultValue(typeof(RoundStyle), "1")]
		public RoundStyle RoundStyle
		{
			get
			{
				return _roundStyle;
			}
			set
			{
				if (_roundStyle != value)
				{
					_roundStyle = value;
					Invalidate();
				}
			}
		}

		[DefaultValue(8)]
		public int Radius
		{
			get
			{
				return _radius;
			}
			set
			{
				if (_radius != value)
				{
					_radius = ((value < 4) ? 4 : value);
					Invalidate();
				}
			}
		}

		public ControlState ControlState
		{
			get
			{
				return _controlState;
			}
			set
			{
				if (_controlState != value)
				{
					_controlState = value;
					Invalidate();
				}
			}
		}

		protected virtual int CheckRectWidth => ImageWidth;

		public ButtonExt()
		{
			SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.SupportsTransparentBackColor | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
		}

		protected override void OnMouseEnter(EventArgs e)
		{
			base.OnMouseEnter(e);
			ControlState = ControlState.Hover;
		}

		protected override void OnMouseLeave(EventArgs e)
		{
			base.OnMouseLeave(e);
			ControlState = ControlState.Normal;
		}

		protected override void OnMouseDown(MouseEventArgs e)
		{
			base.OnMouseDown(e);
			if (e.Button == MouseButtons.Left && e.Clicks == 1)
			{
				ControlState = ControlState.Pressed;
			}
		}

		protected override void OnMouseUp(MouseEventArgs e)
		{
			base.OnMouseUp(e);
			if (e.Button == MouseButtons.Left && e.Clicks == 1)
			{
				if (base.ClientRectangle.Contains(e.Location))
				{
					ControlState = ControlState.Hover;
				}
				else
				{
					ControlState = ControlState.Normal;
				}
			}
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			base.OnPaintBackground(e);
			Graphics graphics = e.Graphics;
			CalculateRect(out var circleRect, out var textRect);
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			Color baseColor = _baseColor;
			Color baseColor2;
			Color borderColor;
			if (base.Enabled)
			{
				switch (ControlState)
				{
				case ControlState.Hover:
					baseColor2 = GetColor(_baseColor, 0, -35, -24, -30);
					borderColor = _baseColor;
					break;
				case ControlState.Pressed:
					baseColor2 = GetColor(_baseColor, 0, -35, -24, -9);
					borderColor = _baseColor;
					break;
				default:
					baseColor2 = _baseColor;
					borderColor = _baseColor;
					break;
				}
			}
			else
			{
				baseColor2 = SystemColors.ControlDark;
				borderColor = SystemColors.ControlDark;
			}
			RenderBackgroundInternal(graphics, base.ClientRectangle, baseColor2, borderColor, baseColor, RoundStyle, Radius, 0f, drawBorder: false, drawGlass: true, LinearGradientMode.Vertical);
			if (base.Image != null)
			{
				graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
				graphics.DrawImage(base.Image, circleRect, 0, 0, base.Image.Width, base.Image.Height, GraphicsUnit.Pixel);
			}
			TextRenderer.DrawText(graphics, Text, Font, textRect, ForeColor, GetTextFormatFlags(TextAlign, RightToLeft == RightToLeft.Yes));
		}

		private TextFormatFlags GetTextFormatFlags(ContentAlignment alignment, bool rightToleft)
		{
			TextFormatFlags textFormatFlags = TextFormatFlags.SingleLine | TextFormatFlags.WordBreak;
			if (rightToleft)
			{
				textFormatFlags |= TextFormatFlags.Right | TextFormatFlags.RightToLeft;
			}
			switch (alignment)
			{
			case ContentAlignment.BottomCenter:
				textFormatFlags |= TextFormatFlags.Bottom | TextFormatFlags.HorizontalCenter;
				break;
			case ContentAlignment.BottomLeft:
				textFormatFlags |= TextFormatFlags.Bottom;
				break;
			case ContentAlignment.BottomRight:
				textFormatFlags |= TextFormatFlags.Bottom | TextFormatFlags.Right;
				break;
			case ContentAlignment.MiddleCenter:
				textFormatFlags |= TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
				break;
			case ContentAlignment.MiddleLeft:
				textFormatFlags |= TextFormatFlags.VerticalCenter;
				break;
			case ContentAlignment.MiddleRight:
				textFormatFlags |= TextFormatFlags.Right | TextFormatFlags.VerticalCenter;
				break;
			case ContentAlignment.TopCenter:
				textFormatFlags |= TextFormatFlags.HorizontalCenter;
				break;
			case ContentAlignment.TopLeft:
				textFormatFlags |= TextFormatFlags.Default;
				break;
			case ContentAlignment.TopRight:
				textFormatFlags |= TextFormatFlags.Right;
				break;
			}
			return textFormatFlags;
		}

		private void CalculateRect(out Rectangle circleRect, out Rectangle textRect)
		{
			ContentAlignment contentAlignment = ContentAlignment.MiddleLeft;
			circleRect = new Rectangle(0, 0, CheckRectWidth, CheckRectWidth);
			textRect = Rectangle.Empty;
			bool flag = (LeftAlignment & contentAlignment) != 0;
			bool flag2 = (RightAlignment & contentAlignment) != 0;
			bool flag3 = RightToLeft == RightToLeft.Yes;
			if ((flag && !flag3) || (flag2 && flag3))
			{
				switch (contentAlignment)
				{
				case ContentAlignment.TopLeft:
				case ContentAlignment.TopRight:
					circleRect.Y = 2;
					break;
				case ContentAlignment.MiddleLeft:
				case ContentAlignment.MiddleRight:
					circleRect.Y = (base.Height - CheckRectWidth) / 2;
					break;
				case ContentAlignment.BottomLeft:
				case ContentAlignment.BottomRight:
					circleRect.Y = base.Height - CheckRectWidth - 2;
					break;
				}
				circleRect.X = 1;
				textRect = new Rectangle(circleRect.Right + 2, 0, base.Width - circleRect.Right - 4, base.Height);
				return;
			}
			if ((flag2 && !flag3) || (flag && flag3))
			{
				switch (contentAlignment)
				{
				case ContentAlignment.TopLeft:
				case ContentAlignment.TopRight:
					circleRect.Y = 2;
					break;
				case ContentAlignment.MiddleLeft:
				case ContentAlignment.MiddleRight:
					circleRect.Y = (base.Height - CheckRectWidth) / 2;
					break;
				case ContentAlignment.BottomLeft:
				case ContentAlignment.BottomRight:
					circleRect.Y = base.Height - CheckRectWidth - 2;
					break;
				}
				circleRect.X = base.Width - CheckRectWidth - 1;
				textRect = new Rectangle(2, 0, base.Width - CheckRectWidth - 6, base.Height);
				return;
			}
			switch (contentAlignment)
			{
			case ContentAlignment.TopCenter:
				circleRect.Y = 2;
				textRect.Y = circleRect.Bottom + 2;
				textRect.Height = base.Height - CheckRectWidth - 6;
				break;
			case ContentAlignment.MiddleCenter:
				circleRect.Y = (base.Height - CheckRectWidth) / 2;
				textRect.Y = 0;
				textRect.Height = base.Height;
				break;
			case ContentAlignment.BottomCenter:
				circleRect.Y = base.Height - CheckRectWidth - 2;
				textRect.Y = 0;
				textRect.Height = base.Height - CheckRectWidth - 6;
				break;
			}
			circleRect.X = (base.Width - CheckRectWidth) / 2;
			textRect.X = 2;
			textRect.Width = base.Width - 4;
		}

		private Color GetColor(Color colorBase, int a, int r, int g, int b)
		{
			int a2 = colorBase.A;
			int r2 = colorBase.R;
			int g2 = colorBase.G;
			int b2 = colorBase.B;
			a = ((a + a2 <= 255) ? Math.Max(a + a2, 0) : 255);
			r = ((r + r2 <= 255) ? Math.Max(r + r2, 0) : 255);
			g = ((g + g2 <= 255) ? Math.Max(g + g2, 0) : 255);
			b = ((b + b2 <= 255) ? Math.Max(b + b2, 0) : 255);
			return Color.FromArgb(a, r, g, b);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, int roundWidth, float basePosition, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			if (drawBorder)
			{
				rect.Width--;
				rect.Height--;
			}
			using LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, mode);
			Color[] colors = new Color[4]
			{
				GetColor(baseColor, 0, 35, 24, 9),
				GetColor(baseColor, 0, 0, 0, 0),
				baseColor,
				GetColor(baseColor, 0, 0, 0, 0)
			};
			ColorBlend colorBlend = new ColorBlend();
			colorBlend.Positions = new float[4] { 0f, basePosition, basePosition, 1f };
			colorBlend.Colors = colors;
			linearGradientBrush.InterpolationColors = colorBlend;
			if (style == RoundStyle.None)
			{
				return;
			}
			using (GraphicsPath path = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: true))
			{
				g.FillPath(linearGradientBrush, path);
			}
			if (baseColor.A <= 80)
			{
				return;
			}
			Rectangle rect2 = rect;
			if (mode == LinearGradientMode.Vertical)
			{
				rect2.Height = (int)((float)rect2.Height * basePosition);
			}
			else
			{
				rect2.Width = (int)((float)rect.Width * basePosition);
			}
			using GraphicsPath path2 = GraphicsPathHelper.CreatePath(rect2, roundWidth, RoundStyle.All, correction: false);
			using SolidBrush brush = new SolidBrush(Color.FromArgb(80, 255, 255, 255));
			g.FillPath(brush, path2);
		}
	}
	public class CheckBoxMult : BaseMultControl<CheckBox>
	{
	}
	public class CircularProgressBar : Panel
	{
		private int _progress;

		public int Progress
		{
			get
			{
				return _progress;
			}
			set
			{
				if (value < 0)
				{
					value = 0;
				}
				if (value > 100)
				{
					value = 100;
				}
				_progress = value;
				Invalidate();
			}
		}

		public CircularProgressBar()
		{
			DoubleBuffered = true;
			SetStyle(ControlStyles.ResizeRedraw | ControlStyles.OptimizedDoubleBuffer, value: true);
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			int num = Math.Min(base.Width, base.Height) - 1;
			int num2 = num / 2;
			int num3 = base.Width / 2;
			int num4 = base.Height / 2;
			using (Pen pen = new Pen(Color.LightGray, 10f))
			{
				e.Graphics.DrawEllipse(pen, num3 - num2, num4 - num2, num, num);
			}
			int sweepAngle = 360 * Progress / 100;
			using (Pen pen2 = new Pen(Color.Blue, 10f))
			{
				e.Graphics.DrawArc(pen2, num3 - num2, num4 - num2, num, num, -90, sweepAngle);
			}
			using Font font = new Font("Arial", 12f, FontStyle.Bold);
			using Brush brush = new SolidBrush(Color.Black);
			string s = Progress + "%";
			e.Graphics.DrawString(s, font, brush, (float)num3 - e.Graphics.MeasureString(s, font).Width / 2f, (float)num4 - e.Graphics.MeasureString(s, font).Height / 2f);
		}
	}
	[ToolboxItem(true)]
	[Description("行填写ComboBox控件")]
	public class ComboBoxLineInput : BaseLineInput<ComboBox>
	{
	}
	[Description("通用的行填写控件")]
	public class CommonLineInput : BaseLineInput<Control>
	{
	}
	[ToolboxItem(true)]
	[Description("行填写DateTimePicker控件")]
	public class DateTimeLineInput : BaseLineInput<DateTimePicker>
	{
	}
	public class GroupByDataGrid : DataGridView
	{
		private string _columnCheckBoxTitle = "选择";

		private string _columnCheckBoxName = "select_check";

		private string _columnCheckBoxDataPropName = "D_Selected";

		public Dictionary<string, string> ShowUploadInfoColumnsDict = new Dictionary<string, string>();

		private string[] _showUploadInfoColumns;

		[Browsable(true)]
		[Description("需要合并的列")]
		public string[] NeedMergeColumns { get; set; }

		[Browsable(true)]
		[Description("合并列是否只读")]
		public bool MergeColumnsReadOnly { get; set; }

		[Browsable(false)]
		[Description("需要某几项值相同 前面才合并")]
		public Dictionary<string, List<string>> MergeConditonDict { get; set; }

		[Browsable(false)]
		[Description("需要展示上传信息的列")]
		public string[] ShowUploadInfoColumns
		{
			private get
			{
				return _showUploadInfoColumns;
			}
			set
			{
				_showUploadInfoColumns = value;
				foreach (string text in value)
				{
					if (!ShowUploadInfoColumnsDict.ContainsKey(text))
					{
						ShowUploadInfoColumnsDict.Add(text, text);
					}
				}
			}
		}

		public GroupByDataGrid()
		{
			base.BorderStyle = BorderStyle.Fixed3D;
			base.BackgroundColor = Color.FromArgb(243, 244, 247);
			base.ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
			{
				ForeColor = SystemColors.WindowText,
				SelectionBackColor = SystemColors.Highlight,
				SelectionForeColor = SystemColors.HighlightText,
				Font = new Font("宋体", 9f, FontStyle.Regular, GraphicsUnit.Point, 134, gdiVerticalFont: false),
				WrapMode = DataGridViewTriState.True,
				Alignment = DataGridViewContentAlignment.MiddleCenter
			};
			base.DefaultCellStyle = new DataGridViewCellStyle
			{
				BackColor = SystemColors.Window,
				ForeColor = SystemColors.ControlText,
				SelectionBackColor = Color.FromArgb(255, 0, 188, 212),
				SelectionForeColor = SystemColors.HighlightText,
				Font = new Font("宋体", 9f, FontStyle.Regular, GraphicsUnit.Point, 134, gdiVerticalFont: false),
				WrapMode = DataGridViewTriState.False,
				Alignment = DataGridViewContentAlignment.MiddleLeft
			};
			CommonDataGridViewRowStyle();
			base.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
			base.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
			base.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.EnableResizing;
			base.ColumnHeadersHeight = 30;
			base.AutoGenerateColumns = false;
			base.AllowUserToAddRows = false;
			base.AllowUserToOrderColumns = true;
		}

		protected override void OnCellFormatting(DataGridViewCellFormattingEventArgs args)
		{
			base.OnCellFormatting(args);
			if (args.RowIndex == 0)
			{
				return;
			}
			Dictionary<string, string> dictionary = NeedMergeColumns?.ToDictionary<string, string, string>((string x) => x, (string x) => x);
			if (dictionary != null && dictionary.ContainsKey(base.Columns[args.ColumnIndex].Name) && IsRepeatedCellValue(args.RowIndex, args.ColumnIndex))
			{
				args.Value = string.Empty;
				base.Rows[args.RowIndex].Cells[args.ColumnIndex].ReadOnly = MergeColumnsReadOnly;
				base.Rows[args.RowIndex - 1].Cells[args.ColumnIndex].ReadOnly = MergeColumnsReadOnly;
				if (base.Rows[args.RowIndex].Cells[args.ColumnIndex] is DataGridViewButtonCell dataGridViewButtonCell)
				{
					dataGridViewButtonCell.Style.NullValue = null;
					dataGridViewButtonCell.Style.BackColor = base.DefaultCellStyle.BackColor;
					dataGridViewButtonCell.Style.SelectionBackColor = base.DefaultCellStyle.SelectionBackColor;
				}
				base.Rows[args.RowIndex].Cells[args.ColumnIndex].Tag = "hidden";
				args.FormattingApplied = true;
			}
		}

		private bool IsRepeatedCellValue(int rowIndex, int colIndex)
		{
			DataGridViewCell dataGridViewCell = base.Rows[rowIndex].Cells[colIndex];
			DataGridViewCell dataGridViewCell2 = base.Rows[rowIndex - 1].Cells[colIndex];
			string name = base.Columns[colIndex].Name;
			bool flag = true;
			Dictionary<string, List<string>> mergeConditonDict = MergeConditonDict;
			if (mergeConditonDict != null && mergeConditonDict.TryGetValue(name, out var value))
			{
				foreach (string item in value)
				{
					if (!IsRepeatedCellValue(rowIndex, base.Rows[rowIndex].Cells[item].ColumnIndex))
					{
						flag = false;
					}
				}
			}
			if (EqualityComparer<object>.Default.Equals(dataGridViewCell.Value, dataGridViewCell2.Value) && flag)
			{
				return true;
			}
			return false;
		}

		protected override void OnCellPainting(DataGridViewCellPaintingEventArgs args)
		{
			base.OnCellPainting(args);
			args.AdvancedBorderStyle.Bottom = DataGridViewAdvancedCellBorderStyle.None;
			Dictionary<string, string> dictionary = NeedMergeColumns?.ToDictionary<string, string, string>((string x) => x, (string x) => x);
			if (args.RowIndex < 1 || args.ColumnIndex < 0)
			{
				return;
			}
			if (dictionary != null && dictionary.ContainsKey(base.Columns[args.ColumnIndex].Name) && IsRepeatedCellValue(args.RowIndex, args.ColumnIndex))
			{
				args.AdvancedBorderStyle.Top = DataGridViewAdvancedCellBorderStyle.None;
				if (base.Rows[args.RowIndex].Cells[args.ColumnIndex] is DataGridViewButtonCell)
				{
					args.AdvancedBorderStyle.All = DataGridViewAdvancedCellBorderStyle.None;
					args.PaintBackground(args.CellBounds, cellsPaintSelectionBackground: false);
					args.Handled = true;
				}
			}
			else
			{
				args.AdvancedBorderStyle.Top = base.AdvancedCellBorderStyle.Top;
			}
		}

		protected override void OnCellContentClick(DataGridViewCellEventArgs e)
		{
			base.OnCellContentClick(e);
			if (e.RowIndex >= 0 && e.ColumnIndex >= 0 && base.Columns[e.ColumnIndex].Name == _columnCheckBoxName && base.Rows[e.RowIndex].Cells[e.ColumnIndex].Value is bool flag)
			{
				base.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = !flag;
			}
		}

		public virtual void RevertChooseAll()
		{
			foreach (DataGridViewRow item in (IEnumerable)base.Rows)
			{
				if (item.Cells[_columnCheckBoxName]?.Value is bool flag)
				{
					item.Cells[_columnCheckBoxName].Value = !flag;
				}
			}
		}

		public void CommonDataGridViewRowStyle(float head_fontSize = 10.5f, float content_fontSize = 11.2f, bool buttonStyle = true)
		{
			base.RowsDefaultCellStyle.ForeColor = Color.Black;
			base.RowsDefaultCellStyle.BackColor = Color.White;
			base.RowTemplate.DefaultCellStyle.SelectionBackColor = Color.FromArgb(215, 233, 246);
			base.RowTemplate.DefaultCellStyle.SelectionForeColor = Color.Black;
			if (!buttonStyle)
			{
				return;
			}
			DataGridViewColumnCollection columns = base.Columns;
			foreach (DataGridViewColumn item in columns)
			{
				if (item is DataGridViewButtonColumn dataGridViewButtonColumn)
				{
					dataGridViewButtonColumn.CellTemplate.Style.BackColor = Color.FromArgb(243, 244, 247);
					dataGridViewButtonColumn.FlatStyle = FlatStyle.Popup;
				}
			}
		}

		protected override void OnCellValueChanged(DataGridViewCellEventArgs e)
		{
			base.OnCellValueChanged(e);
			if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
			{
				DataGridViewRow dataGridViewRow = base.Rows[e.RowIndex];
				InvalidateCell(dataGridViewRow.Cells[e.ColumnIndex]);
			}
		}

		protected override void OnDataBindingComplete(DataGridViewBindingCompleteEventArgs e)
		{
			base.OnDataBindingComplete(e);
		}
	}
	public class PageUserControl : UserControl
	{
		private int[] pageSizeArray;

		private int curPageIndex = 1;

		private int totalPage;

		private int totalRecord;

		private IContainer components = null;

		private FlowLayoutPanel flowLayoutPanel1;

		private ToolStrip pageToolStrip;

		private ToolStripLabel toolStripLabel3;

		private ToolStripLabel curPageTxt2;

		private ToolStripSeparator toolStripSeparator5;

		private ToolStripLabel toolStripLabel6;

		private ToolStripLabel totalPagesTxt2;

		private ToolStripSeparator toolStripSeparator6;

		private ToolStripLabel toolStripLabel8;

		private ToolStripLabel totalRecordsTxt2;

		private ToolStripSeparator toolStripSeparator7;

		private ToolStripButton prePageTxt2;

		private ToolStripButton nextPageTxt2;

		private ToolStripSeparator toolStripSeparator8;

		private ToolStripComboBox PageSizeComboBox2;

		[Browsable(true)]
		[Description("查询的回调")]
		public Action SearchCallBack { get; set; } = null;


		[Browsable(true)]
		[Description("选中的下拉页码选中下标")]
		public int SelectedIndex { get; private set; }

		[Browsable(true)]
		[Description("页码信息")]
		public int[] PageSizeArray
		{
			get
			{
				return pageSizeArray;
			}
			set
			{
				pageSizeArray = value;
				if (pageSizeArray != null)
				{
					int[] array = pageSizeArray;
					foreach (int num in array)
					{
						PageSizeComboBox2.Items.Add("每页" + num + "条");
					}
					PageSizeComboBox2.SelectedIndex = SelectedIndex;
				}
			}
		}

		[Browsable(true)]
		[Description("页大小")]
		public int PageSize => PageSizeArray[SelectedIndex];

		[Browsable(true)]
		[Description("当前页面")]
		public int CurPageIndex
		{
			get
			{
				return curPageIndex;
			}
			set
			{
				//IL_001b: Unknown result type (might be due to invalid IL or missing references)
				//IL_0025: Expected O, but got Unknown
				curPageIndex = value;
				if (base.IsHandleCreated)
				{
					Invoke((Delegate)(Action)delegate
					{
						curPageTxt2.Text = $"第{curPageIndex}页";
					});
				}
				else
				{
					curPageTxt2.Text = $"第{curPageIndex}页";
				}
			}
		}

		[Browsable(true)]
		[Description("总页码")]
		public int TotalPage
		{
			get
			{
				return totalPage;
			}
			set
			{
				//IL_0034: Unknown result type (might be due to invalid IL or missing references)
				//IL_003e: Expected O, but got Unknown
				totalPage = value;
				if (base.IsHandleCreated)
				{
					Invoke((Delegate)(Action)delegate
					{
						totalPagesTxt2.Text = $"共{value}页";
					});
				}
				else
				{
					totalPagesTxt2.Text = $"共{value}页";
				}
			}
		}

		[Browsable(true)]
		[Description("总记录数")]
		public int TotalRecord
		{
			get
			{
				return totalRecord;
			}
			set
			{
				//IL_0034: Unknown result type (might be due to invalid IL or missing references)
				//IL_003e: Expected O, but got Unknown
				totalRecord = value;
				if (base.IsHandleCreated)
				{
					Invoke((Delegate)(Action)delegate
					{
						totalRecordsTxt2.Text = $"{value}条";
						TotalPage = ((value % PageSize == 0) ? (value / PageSize) : (value / PageSize + 1));
					});
				}
				else
				{
					totalRecordsTxt2.Text = $"{value}条";
				}
			}
		}

		public PageUserControl()
		{
			InitializeComponent();
		}

		public void Init()
		{
			CurPageIndex = 1;
			Action searchCallBack = SearchCallBack;
			if (searchCallBack != null)
			{
				searchCallBack.BeginInvoke((AsyncCallback)delegate(IAsyncResult asyncCb)
				{
					SearchCallBack.EndInvoke(asyncCb);
				}, (object)null);
			}
		}

		private void PrePage_Click(object sender, EventArgs e)
		{
			if (!(sender is ToolStripItem))
			{
				return;
			}
			ToolStrip owner = (sender as ToolStripItem).Owner;
			if (CurPageIndex - 1 > 0)
			{
				CurPageIndex--;
				Action searchCallBack = SearchCallBack;
				if (searchCallBack != null)
				{
					searchCallBack.Invoke();
				}
			}
			else
			{
				MessageBox.Show("已经是第一页了");
			}
		}

		private void NextPage_Click(object sender, EventArgs e)
		{
			if (!(sender is ToolStripItem))
			{
				return;
			}
			if (CurPageIndex + 1 <= TotalPage)
			{
				CurPageIndex++;
				Action searchCallBack = SearchCallBack;
				if (searchCallBack != null)
				{
					searchCallBack.Invoke();
				}
			}
			else
			{
				MessageBox.Show("已经是最后一页了");
			}
		}

		private void PageSizeComboBox2_SelectedIndexChanged(object sender, EventArgs e)
		{
			SelectedIndex = PageSizeComboBox2.SelectedIndex;
			CurPageIndex = 1;
			Action searchCallBack = SearchCallBack;
			if (searchCallBack != null)
			{
				searchCallBack.Invoke();
			}
		}

		private void pageToolStrip_Paint(object sender, PaintEventArgs e)
		{
			if ((sender as ToolStrip).RenderMode == ToolStripRenderMode.System)
			{
				Rectangle clip = new Rectangle(0, 0, pageToolStrip.Width - 8, pageToolStrip.Height - 8);
				e.Graphics.SetClip(clip);
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
			this.pageToolStrip = new System.Windows.Forms.ToolStrip();
			this.toolStripLabel3 = new System.Windows.Forms.ToolStripLabel();
			this.curPageTxt2 = new System.Windows.Forms.ToolStripLabel();
			this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
			this.toolStripLabel6 = new System.Windows.Forms.ToolStripLabel();
			this.totalPagesTxt2 = new System.Windows.Forms.ToolStripLabel();
			this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
			this.toolStripLabel8 = new System.Windows.Forms.ToolStripLabel();
			this.totalRecordsTxt2 = new System.Windows.Forms.ToolStripLabel();
			this.toolStripSeparator7 = new System.Windows.Forms.ToolStripSeparator();
			this.prePageTxt2 = new System.Windows.Forms.ToolStripButton();
			this.nextPageTxt2 = new System.Windows.Forms.ToolStripButton();
			this.toolStripSeparator8 = new System.Windows.Forms.ToolStripSeparator();
			this.PageSizeComboBox2 = new System.Windows.Forms.ToolStripComboBox();
			this.flowLayoutPanel1.SuspendLayout();
			this.pageToolStrip.SuspendLayout();
			base.SuspendLayout();
			this.flowLayoutPanel1.Controls.Add(this.pageToolStrip);
			this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.flowLayoutPanel1.Location = new System.Drawing.Point(0, 0);
			this.flowLayoutPanel1.Name = "flowLayoutPanel1";
			this.flowLayoutPanel1.Size = new System.Drawing.Size(551, 34);
			this.flowLayoutPanel1.TabIndex = 0;
			this.pageToolStrip.Dock = System.Windows.Forms.DockStyle.Fill;
			this.pageToolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
			this.pageToolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[13]
			{
				this.toolStripLabel3, this.curPageTxt2, this.toolStripSeparator5, this.toolStripLabel6, this.totalPagesTxt2, this.toolStripSeparator6, this.toolStripLabel8, this.totalRecordsTxt2, this.toolStripSeparator7, this.prePageTxt2,
				this.nextPageTxt2, this.toolStripSeparator8, this.PageSizeComboBox2
			});
			this.pageToolStrip.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.HorizontalStackWithOverflow;
			this.pageToolStrip.Location = new System.Drawing.Point(0, 0);
			this.pageToolStrip.Name = "pageToolStrip";
			this.pageToolStrip.Size = new System.Drawing.Size(551, 32);
			this.pageToolStrip.TabIndex = 4;
			this.pageToolStrip.Text = "信息分页";
			this.pageToolStrip.Paint += new System.Windows.Forms.PaintEventHandler(pageToolStrip_Paint);
			this.toolStripLabel3.Name = "toolStripLabel3";
			this.toolStripLabel3.Size = new System.Drawing.Size(59, 29);
			this.toolStripLabel3.Text = "当前页数:";
			this.curPageTxt2.ForeColor = System.Drawing.Color.Tomato;
			this.curPageTxt2.Name = "curPageTxt2";
			this.curPageTxt2.Size = new System.Drawing.Size(39, 29);
			this.curPageTxt2.Tag = "1";
			this.curPageTxt2.Text = "第1页";
			this.toolStripSeparator5.Name = "toolStripSeparator5";
			this.toolStripSeparator5.Size = new System.Drawing.Size(6, 32);
			this.toolStripLabel6.Name = "toolStripLabel6";
			this.toolStripLabel6.Size = new System.Drawing.Size(47, 29);
			this.toolStripLabel6.Text = "总页数:";
			this.totalPagesTxt2.Name = "totalPagesTxt2";
			this.totalPagesTxt2.Size = new System.Drawing.Size(39, 29);
			this.totalPagesTxt2.Text = "共1页";
			this.toolStripSeparator6.Name = "toolStripSeparator6";
			this.toolStripSeparator6.Size = new System.Drawing.Size(6, 32);
			this.toolStripLabel8.Name = "toolStripLabel8";
			this.toolStripLabel8.Size = new System.Drawing.Size(59, 29);
			this.toolStripLabel8.Text = "总记录数:";
			this.totalRecordsTxt2.ForeColor = System.Drawing.Color.Teal;
			this.totalRecordsTxt2.Name = "totalRecordsTxt2";
			this.totalRecordsTxt2.Size = new System.Drawing.Size(27, 29);
			this.totalRecordsTxt2.Text = "0条";
			this.toolStripSeparator7.Name = "toolStripSeparator7";
			this.toolStripSeparator7.Size = new System.Drawing.Size(6, 32);
			this.prePageTxt2.Image = WinformsUserControl.Properties.Resources.pre;
			this.prePageTxt2.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.prePageTxt2.Name = "prePageTxt2";
			this.prePageTxt2.RightToLeftAutoMirrorImage = true;
			this.prePageTxt2.Size = new System.Drawing.Size(64, 29);
			this.prePageTxt2.Text = "上一页";
			this.prePageTxt2.Click += new System.EventHandler(PrePage_Click);
			this.nextPageTxt2.Image = WinformsUserControl.Properties.Resources.next;
			this.nextPageTxt2.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.nextPageTxt2.Name = "nextPageTxt2";
			this.nextPageTxt2.Size = new System.Drawing.Size(64, 29);
			this.nextPageTxt2.Text = "下一页";
			this.nextPageTxt2.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
			this.nextPageTxt2.Click += new System.EventHandler(NextPage_Click);
			this.toolStripSeparator8.Name = "toolStripSeparator8";
			this.toolStripSeparator8.Size = new System.Drawing.Size(6, 32);
			this.PageSizeComboBox2.FlatStyle = System.Windows.Forms.FlatStyle.Standard;
			this.PageSizeComboBox2.Font = new System.Drawing.Font("Microsoft YaHei UI", 10f);
			this.PageSizeComboBox2.ForeColor = System.Drawing.Color.SaddleBrown;
			this.PageSizeComboBox2.Name = "PageSizeComboBox2";
			this.PageSizeComboBox2.Size = new System.Drawing.Size(100, 32);
			this.PageSizeComboBox2.SelectedIndexChanged += new System.EventHandler(PageSizeComboBox2_SelectedIndexChanged);
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.Controls.Add(this.flowLayoutPanel1);
			base.Name = "PageUserControl";
			base.Size = new System.Drawing.Size(551, 34);
			this.flowLayoutPanel1.ResumeLayout(false);
			this.flowLayoutPanel1.PerformLayout();
			this.pageToolStrip.ResumeLayout(false);
			this.pageToolStrip.PerformLayout();
			base.ResumeLayout(false);
		}
	}
	public class ProgressBarExt : ProgressBar
	{
		public ProgressBarExt()
		{
			SetStyle(ControlStyles.UserPaint, value: true);
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			SolidBrush solidBrush = null;
			Rectangle rectangle = new Rectangle(0, 0, base.Width, base.Height);
			rectangle.Height -= 4;
			rectangle.Width = (int)((double)rectangle.Width * ((double)base.Value / (double)base.Maximum)) - 4;
			solidBrush = new SolidBrush(Color.Coral);
			e.Graphics.FillRectangle(solidBrush, 2, 2, rectangle.Width, rectangle.Height);
		}
	}
	public class RadioBtnMult : BaseMultControl<RadioButton>
	{
	}
	public class RadioBtnMultLineInput : BaseLineInput<RadioBtnMult>
	{
	}
	public enum TextValueType
	{
		Text = 0,
		Number = 2,
		Letters = 4,
		NumberOrLetters = 6,
		TelPhone = 8
	}
	public class TextBoxExt : TextBox
	{
		[Description("值类型")]
		private TextValueType valueType = TextValueType.Text;

		private double minValue = -**********.0;

		private double maxValue = **********.0;

		private int precision = 0;

		private bool _nullable = true;

		public TextValueType ValueType
		{
			get
			{
				return valueType;
			}
			set
			{
				valueType = value;
				MaxLength = ((valueType == TextValueType.Number) ? 32 : MaxLength);
			}
		}

		[Description("校验规则")]
		public string PatternRule { get; set; }

		[Description("吸附的ErrorProvider")]
		public ErrorProvider ErrorProvider { get; set; }

		[Description("错误提示")]
		public string WarnMsg { get; set; }

		[Description("是否可以为空（默认可以）")]
		public bool Nullable
		{
			get
			{
				return _nullable;
			}
			set
			{
				_nullable = value;
			}
		}

		[Description("小数位")]
		public int Precision
		{
			get
			{
				return precision;
			}
			set
			{
				precision = value;
				if (precision < 0)
				{
					throw new ArgumentOutOfRangeException("the precision's value not less than '0'");
				}
			}
		}

		[Description("值范围-最小值")]
		public double MinValue
		{
			get
			{
				return minValue;
			}
			set
			{
				minValue = value;
				if (value > MaxValue)
				{
					throw new ArgumentOutOfRangeException("the MinValue's value don't greater than MaxValue's value");
				}
			}
		}

		[Description("值范围-最大值")]
		public double MaxValue
		{
			get
			{
				return maxValue;
			}
			set
			{
				maxValue = value;
				if (value < MinValue)
				{
					throw new ArgumentOutOfRangeException("the MaxValue's value don't less than MinValue's value");
				}
			}
		}

		public TextBoxExt()
		{
			MaxLength = ((valueType == TextValueType.Number) ? (MaxLength.ToString().Length + Precision) : MaxLength);
		}

		protected override void OnKeyPress(KeyPressEventArgs e)
		{
			try
			{
				if (!e.Handled)
				{
					string value = e.KeyChar.ToString();
					int num = base.SelectionStart;
					int num2 = SelectionLength;
					string text;
					if ("\u0016".Equals(value))
					{
						text = Clipboard.GetText();
					}
					else if ("\u0018".Equals(value))
					{
						text = "";
					}
					else
					{
						if ("\u001a".Equals(value) || "\u0003".Equals(value) || "\b".Equals(value) || "\r".Equals(value) || "\t".Equals(value))
						{
							return;
						}
						text = e.KeyChar.ToString();
					}
					string text2 = "";
					text2 = ((num2 == 0) ? Text.Insert(num, text) : (Text.Substring(0, num) + text + Text.Substring(num + num2)));
					string text3 = null;
					switch (valueType)
					{
					case TextValueType.Text:
						text3 = PatternRule;
						break;
					case TextValueType.Number:
						text3 = ((minValue >= 0.0) ? "^" : "^[-]?") + "([1-9]\\d{0,32}|0)";
						text3 = ((precision == 0) ? (text3 + "$") : (text3 + "(\\.)?\\d{0," + precision + "}$"));
						break;
					case TextValueType.Letters:
						text3 = "^[a-zA-Z]{1,99999}$";
						break;
					case TextValueType.NumberOrLetters:
						text3 = "^[a-zA-Z0-9]{1,99999}$";
						break;
					case TextValueType.TelPhone:
						text3 = "^1\\d{0,10}$";
						break;
					}
					if (text3 != null && !string.IsNullOrEmpty(text2) && !Regex.IsMatch(text2, text3))
					{
						e.Handled = true;
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			base.OnKeyPress(e);
		}

		protected override void OnValidating(CancelEventArgs e)
		{
			if (!Nullable && string.IsNullOrWhiteSpace(Text))
			{
				string value = WarnMsg ?? "当前项不能为空";
				ErrorProvider?.SetError(this, value);
				e.Cancel = ErrorProvider != null;
			}
			if (string.IsNullOrWhiteSpace(Text))
			{
				base.OnValidating(e);
				return;
			}
			string pattern = null;
			TextValueType textValueType = valueType;
			TextValueType textValueType2 = textValueType;
			if (textValueType2 == TextValueType.TelPhone)
			{
				pattern = "^1\\d{10}$";
			}
			if (valueType == TextValueType.TelPhone && !Regex.IsMatch(Text, pattern))
			{
				string value2 = WarnMsg ?? "当前项不是正确的手机号";
				ErrorProvider?.SetError(this, value2);
				e.Cancel = ErrorProvider != null;
			}
			else if (valueType == TextValueType.Number)
			{
				try
				{
					double num = Convert.ToDouble(Text);
					if (num < MinValue || num > MaxValue)
					{
						throw new ArgumentOutOfRangeException($"当前值不在{MinValue}-{MaxValue}之间");
					}
					ErrorProvider?.SetError(this, null);
					e.Cancel = false;
				}
				catch (FormatException)
				{
					string value3 = WarnMsg ?? "当前项不是正确的数字";
					ErrorProvider?.SetError(this, value3);
					e.Cancel = ErrorProvider != null;
				}
				catch (ArgumentOutOfRangeException ex2)
				{
					string value4 = WarnMsg ?? ex2.Message;
					ErrorProvider?.SetError(this, value4);
					e.Cancel = ErrorProvider != null;
				}
			}
			else
			{
				ErrorProvider?.SetError(this, null);
				e.Cancel = false;
			}
			base.OnValidating(e);
		}

		protected override void OnValidated(EventArgs e)
		{
			base.OnValidated(e);
		}
	}
	[ToolboxItem(true)]
	[Description("行填写RichTextBox控件")]
	public class RichTextBoxLineInput : BaseLineInput<RichTextBox>
	{
	}
	[ToolboxItem(true)]
	public class TextBoxLineInput : BaseLineInput<TextBox>
	{
	}
}
namespace WinformsUserControl.Controls.FontControls
{
	public class FontDropDownControl : ComboBox
	{
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public new object Items
		{
			get
			{
				return null;
			}
			set
			{
			}
		}

		public FontFamilyInfo SelectedFontFamily
		{
			get
			{
				return (FontFamilyInfo)base.SelectedItem;
			}
			set
			{
				base.SelectedItem = value;
			}
		}

		public FontDropDownControl()
		{
			base.DropDownStyle = ComboBoxStyle.DropDownList;
			base.DrawMode = DrawMode.OwnerDrawFixed;
			base.DropDownHeight = 500;
			base.ItemHeight = 20;
			base.DrawItem += ComboBox_DrawItem;
			FontFamily[] families = FontFamily.Families;
			foreach (FontFamily item in families)
			{
				base.Items.Add(item);
			}
		}

		private void ComboBox_DrawItem(object sender, DrawItemEventArgs e)
		{
			Graphics graphics = e.Graphics;
			e.DrawBackground();
			if (e.Index >= 0 && e.Index < base.Items.Count)
			{
				FontUIToolkit.DrawFontItem(graphics, (FontFamilyInfo)base.Items[e.Index], e.Bounds, (e.State & DrawItemState.Selected) == DrawItemState.Selected);
			}
		}
	}
	public class FontListBox : ListBox
	{
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public new ObjectCollection Items => base.Items;

		public FontListBox()
		{
			DrawMode = DrawMode.OwnerDrawFixed;
			ItemHeight = 20;
			base.DrawItem += FontListBox_DrawItem;
			FontFamily[] families = FontFamily.Families;
			foreach (FontFamily item in families)
			{
				base.Items.Add(item);
			}
		}

		protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
		{
			return false;
		}

		protected override bool ProcessDialogChar(char charCode)
		{
			int num = (((FontFamilyInfo)base.SelectedItem).Names.Any((string n) => n[0] == charCode) ? (base.SelectedIndex + 1) : 0);
			for (int i = num; i < base.Items.Count; i++)
			{
				if (((FontFamilyInfo)base.Items[i]).Names.Any((string n) => n[0] == charCode))
				{
					base.SelectedIndex = i;
					break;
				}
			}
			return base.ProcessDialogChar(charCode);
		}

		private void FontListBox_DrawItem(object sender, DrawItemEventArgs e)
		{
			Graphics graphics = e.Graphics;
			e.DrawBackground();
			FontUIToolkit.DrawFontItem(graphics, (FontFamilyInfo)base.Items[e.Index], e.Bounds, (e.State & DrawItemState.Selected) == DrawItemState.Selected);
		}
	}
	public class FontToolStripDropDown : ToolStripComboBox
	{
		private string[] fontFamilyNames;

		[DefaultValue(500)]
		public new int DropDownHeight
		{
			get
			{
				return base.DropDownHeight;
			}
			set
			{
				base.DropDownHeight = value;
			}
		}

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public new object Items
		{
			get
			{
				return null;
			}
			set
			{
			}
		}

		[Description("此属性可设置你需要字体作为选项")]
		[Browsable(false)]
		[Editor("System.Windows.Forms.Design.ListControlStringCollectionEditor, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a", typeof(UITypeEditor))]
		public string[] FontFamilyNames
		{
			get
			{
				return fontFamilyNames;
			}
			set
			{
				fontFamilyNames = value;
				if (fontFamilyNames == null || fontFamilyNames.Length == 0)
				{
					return;
				}
				ComboBox.ObjectCollection items = base.ComboBox.Items;
				int index = 0;
				Dictionary<string, int> dictionary = fontFamilyNames.ToDictionary<string, string, int>((string p) => p, (string p) => ++index);
				IDictionary<int, FontFamilyInfo> dictionary2 = new SortedDictionary<int, FontFamilyInfo>();
				foreach (FontFamilyInfo item in items)
				{
					if (dictionary.TryGetValue(item.FontFamily.Name, out var value2))
					{
						dictionary2[value2] = item;
					}
				}
				base.ComboBox.Items.Clear();
				ComboBox.ObjectCollection items2 = base.ComboBox.Items;
				object[] items3 = ((IEnumerable<KeyValuePair<int, FontFamilyInfo>>)dictionary2).Select<KeyValuePair<int, FontFamilyInfo>, FontFamilyInfo>((Func<KeyValuePair<int, FontFamilyInfo>, FontFamilyInfo>)((KeyValuePair<int, FontFamilyInfo> p) => p.Value)).ToArray();
				items2.AddRange(items3);
				if (base.ComboBox.Items.Count > 0)
				{
					base.ComboBox.Text = fontFamilyNames.First();
				}
			}
		}

		public FontToolStripDropDown()
		{
			base.ComboBox.DrawMode = DrawMode.OwnerDrawFixed;
			base.ComboBox.DropDownHeight = 400;
			base.ComboBox.DrawItem += ComboBox_DrawItem;
			FontFamily[] families = FontFamily.Families;
			foreach (FontFamily fontFamily in families)
			{
				base.ComboBox.Items.Add(new FontFamilyInfo(fontFamily));
			}
			if (base.ComboBox.Items.Count > 0)
			{
				base.ComboBox.Text = Font.FontFamily.Name;
			}
		}

		private void ComboBox_DrawItem(object sender, DrawItemEventArgs e)
		{
			Graphics graphics = e.Graphics;
			e.DrawBackground();
			FontUIToolkit.DrawFontItem(graphics, (FontFamilyInfo)base.ComboBox.Items[e.Index], e.Bounds, (e.State & DrawItemState.Selected) == DrawItemState.Selected);
		}
	}
}
namespace WinformsUserControl.Controls.Base
{
	public interface BaseInput
	{
		string LabelTitle { get; set; }

		bool MRequired { get; set; }

		string InitName { get; set; }

		int LabelTitleWidth { get; set; }

		int LabelTitleTop { get; set; }

		int LabelTitleHeight { get; set; }
	}
	[ToolboxItem(true)]
	public class BaseLineInput<TControl> : FlowLayoutPanel, BaseInput where TControl : Control
	{
		private string _initName = "lineInput";

		private string _labelTitle = "标题栏";

		private bool m_required = true;

		private int _labelTitleWidth = 70;

		private int _labelTitleHeight = 30;

		private TControl _inputBox;

		private Panel _remarkPanel;

		private int _labelTitleTop = 3;

		[Category("标题栏")]
		[Description("配置的单选框")]
		[Editor("System.ComponentModel.Design.MultilineStringEditor, System.Design, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a", typeof(UITypeEditor))]
		[SettingsBindable(true)]
		public string LabelTitle
		{
			get
			{
				return _labelTitle;
			}
			set
			{
				_labelTitle = value;
				CreateInstanceLabelTitle(value);
			}
		}

		[Category("必填项")]
		[Description("必填项")]
		public bool MRequired
		{
			get
			{
				return m_required;
			}
			set
			{
				m_required = value;
				CreateInstanceRequire();
			}
		}

		public virtual TControl InputBox
		{
			get
			{
				return _inputBox;
			}
			set
			{
				_inputBox = value;
				CreateInstanceControl(value);
			}
		}

		public string InitName
		{
			get
			{
				return _initName;
			}
			set
			{
				_initName = value;
			}
		}

		public int LabelTitleWidth
		{
			get
			{
				return _labelTitleWidth;
			}
			set
			{
				_labelTitleWidth = value;
				CreateInstanceLabelTitle(LabelTitle);
			}
		}

		public int LabelTitleTop
		{
			get
			{
				return _labelTitleTop;
			}
			set
			{
				_labelTitleTop = value;
				CreateInstanceLabelTitle(LabelTitle);
				CreateInstanceRequire();
			}
		}

		public int LabelTitleHeight
		{
			get
			{
				return _labelTitleHeight;
			}
			set
			{
				_labelTitleHeight = value;
				CreateInstanceLabelTitle(LabelTitle);
			}
		}

		public Panel RemarkPanel
		{
			get
			{
				return _remarkPanel;
			}
			set
			{
				if (InputBox == null)
				{
					CreateInstanceControl(this);
				}
				_remarkPanel = value;
				base.Controls.Add(value);
			}
		}

		private void CreateInstanceControl(Control control)
		{
			if (control != null)
			{
				control.Left = 3;
				if (!base.Controls.ContainsKey(control.Name))
				{
					base.Controls.Add(control);
				}
			}
		}

		private void CreateInstanceLabelTitle(string text)
		{
			if (base.Controls.ContainsKey("lable_" + InitName))
			{
				base.Controls["lable_" + InitName].Text = text;
				base.Controls["lable_" + InitName].Width = LabelTitleWidth;
				base.Controls["lable_" + InitName].Height = LabelTitleHeight;
				base.Controls["lable_" + InitName].Margin = new Padding(3, LabelTitleTop, 3, 0);
				return;
			}
			Label label = new Label
			{
				Name = "lable_" + InitName,
				Text = text
			};
			label.Margin = new Padding(3, LabelTitleTop, 3, 0);
			label.Height = LabelTitleHeight;
			label.Left = 10;
			label.TextAlign = ContentAlignment.MiddleRight;
			label.AutoSize = false;
			label.Width = LabelTitleWidth;
			base.Controls.Add(label);
		}

		private void CreateInstanceRequire()
		{
			if (base.Controls.ContainsKey("lable_" + InitName + "_require"))
			{
				Control control = base.Controls["lable_" + InitName];
				int num = (int)((float)control.Height * 0.32f);
				base.Controls["lable_" + InitName + "_require"].Text = (MRequired ? "*" : "");
				Padding margin = base.Controls["lable_" + InitName + "_require"].Margin;
				margin.Top = LabelTitleTop + num;
				margin.Right = 1;
				base.Controls["lable_" + InitName + "_require"].Margin = margin;
			}
			else
			{
				Label label = new Label
				{
					Name = "lable_" + InitName + "_require",
					Text = (MRequired ? "*" : ""),
					ForeColor = Color.Red
				};
				Control control2 = base.Controls["lable_" + InitName];
				Padding margin2 = label.Margin;
				int num2 = (int)((float)control2.Height * 0.32f);
				margin2.Top = LabelTitleTop + num2;
				margin2.Right = 1;
				label.Margin = margin2;
				label.AutoSize = false;
				label.Width = 15;
				base.Controls.Add(label);
			}
		}

		public BaseLineInput()
		{
			CreateInstanceLabelTitle(LabelTitle);
			CreateInstanceRequire();
			base.Width = 200;
			base.Height = 60;
		}

		protected override void OnControlAdded(ControlEventArgs e)
		{
			base.OnControlAdded(e);
		}

		protected override void OnControlRemoved(ControlEventArgs e)
		{
			base.OnControlAdded(e);
		}

		protected override ControlCollection CreateControlsInstance()
		{
			return new HyControlCollection<TControl>(this);
		}
	}
	public class BaseMultControl<TControl> : FlowLayoutPanel where TControl : ButtonBase
	{
		private CommonControlCollection<TControl> _btns;

		[Category("Radios")]
		[Description("配置的单选框")]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
		[Localizable(true)]
		public CommonControlCollection<TControl> Btns
		{
			get
			{
				if (_btns == null)
				{
					_btns = CreateBtnsInstance();
				}
				return _btns;
			}
		}

		[Browsable(false)]
		public bool Checked
		{
			get
			{
				if (Btns == null)
				{
					return false;
				}
				return Btns.Checked();
			}
		}

		public object DisplayText
		{
			get
			{
				return Btns?.GetCheckedDisplayText();
			}
			set
			{
				if (value != null && !(value is DBNull))
				{
					if (!(value is string) && !(value is IList<string>))
					{
						throw new Exception("DisplayText的值必须是string或者IList<string>");
					}
					Btns?.SetValue(value);
				}
			}
		}

		public IEnumerable<TControl> CheckedControls => Btns?.GetCheckedControls();

		protected virtual CommonControlCollection<TControl> CreateBtnsInstance()
		{
			return new CommonControlCollection<TControl>(this);
		}

		public BaseMultControl()
		{
			if (_btns == null)
			{
				_btns = CreateBtnsInstance();
			}
		}
	}
}
namespace WinformsUserControl.Controls.Common
{
	public static class FontUIToolkit
	{
		public static readonly float[] FontSizeList = new float[28]
		{
			5f, 6f, 7f, 8f, 9f, 10f, 10.5f, 11f, 11.5f, 12f,
			12.5f, 14f, 16f, 18f, 20f, 22f, 24f, 26f, 28f, 30f,
			32f, 34f, 38f, 46f, 58f, 64f, 78f, 92f
		};

		private static readonly float FixedDrawFontSize = 14f;

		internal static bool Has(this FontStyle flag, FontStyle check)
		{
			return (flag & check) == check;
		}

		internal static bool HasAny(this FontStyle flag, FontStyle check)
		{
			return (flag & check) > FontStyle.Regular;
		}

		public static string GetFontStyleName(FontStyle style, string regularText, string italicText, string boldText)
		{
			List<string> list = new List<string>();
			if (style.Has(FontStyle.Bold))
			{
				list.Add(boldText);
			}
			if (style.Has(FontStyle.Italic))
			{
				list.Add(italicText);
			}
			if (list.Count == 0)
			{
				return regularText;
			}
			return string.Join(" ", list.ToArray());
		}

		public static FontStyle GetFontStyleByName(string text, string italicText, string boldText)
		{
			string[] array = text.Split(new char[1] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
			FontStyle fontStyle = FontStyle.Regular;
			string[] array2 = array;
			foreach (string strA in array2)
			{
				if (string.Compare(strA, italicText, ignoreCase: true) == 0)
				{
					fontStyle |= FontStyle.Italic;
				}
				else if (string.Compare(strA, boldText, ignoreCase: true) == 0)
				{
					fontStyle |= FontStyle.Bold;
				}
			}
			return fontStyle;
		}

		public static void DrawFontItem(Graphics g, FontFamilyInfo fontFamily, Rectangle rect, bool isSelected)
		{
			using StringFormat stringFormat = new StringFormat
			{
				Alignment = StringAlignment.Near,
				LineAlignment = StringAlignment.Center
			};
			stringFormat.FormatFlags |= StringFormatFlags.NoWrap;
			using FontFamily fontFamily2 = new FontFamily(fontFamily.CultureName);
			Font font = null;
			if (fontFamily2.IsStyleAvailable(FontStyle.Regular))
			{
				font = new Font(fontFamily.CultureName, FixedDrawFontSize, FontStyle.Regular);
			}
			else if (fontFamily2.IsStyleAvailable(FontStyle.Bold))
			{
				font = new Font(fontFamily.CultureName, FixedDrawFontSize, FontStyle.Bold);
			}
			else if (fontFamily2.IsStyleAvailable(FontStyle.Italic))
			{
				font = new Font(fontFamily.CultureName, FixedDrawFontSize, FontStyle.Italic);
			}
			else if (fontFamily2.IsStyleAvailable(FontStyle.Strikeout))
			{
				font = new Font(fontFamily.CultureName, FixedDrawFontSize, FontStyle.Strikeout);
			}
			else if (fontFamily2.IsStyleAvailable(FontStyle.Underline))
			{
				font = new Font(fontFamily.CultureName, FixedDrawFontSize, FontStyle.Underline);
			}
			if (font != null)
			{
				g.DrawString(font.Name, font, isSelected ? SystemBrushes.HighlightText : Brushes.Black, rect, stringFormat);
				font.Dispose();
			}
		}

		public static float GetLargerSize(float size)
		{
			return (size >= FontSizeList.Max()) ? size : ((IEnumerable<float>)FontSizeList).Where((Func<float, bool>)((float fs) => fs > size)).Min();
		}

		public static float GetSmallerSize(float size)
		{
			return (size >= FontSizeList.Max()) ? size : ((IEnumerable<float>)FontSizeList).Where((Func<float, bool>)((float fs) => fs < size)).Max();
		}

		public static Font GetFontIfExisted(string name, float size, FontStyle style)
		{
			Font font = null;
			try
			{
				FontFamily fontFamily = new FontFamily(name);
				if (!fontFamily.IsStyleAvailable(style))
				{
					style = FontStyle.Regular;
				}
				return new Font(fontFamily, size, style);
			}
			catch (Exception ex)
			{
				Debug.WriteLine("info: " + ex.Message, "font toolkit");
				return SystemFonts.DefaultFont;
			}
		}

		public static FontFamily GetFontFamilyIfExisted(string name)
		{
			try
			{
				return new FontFamily(name);
			}
			catch (Exception ex)
			{
				Debug.WriteLine("font family does not exist: " + ex.Message, "font toolkit");
				return null;
			}
		}

		public static bool AreCloseSize(float s1, float s2)
		{
			return Math.Abs(s1 - s2) < 0.3f;
		}
	}
	public class FontFamilyInfo
	{
		private string[] names;

		private string cultureName;

		public string[] Names => names;

		public FontFamily FontFamily { get; private set; }

		public string CultureName => cultureName;

		public FontFamilyInfo(FontFamily fontFamily, params string[] names)
		{
			FontFamily = fontFamily;
			cultureName = fontFamily.Name;
			this.names = names;
		}

		public bool IsFamilyName(string name)
		{
			return names == null || names.Any((string n) => n.StartsWith(name, StringComparison.CurrentCultureIgnoreCase));
		}

		public override string ToString()
		{
			return cultureName;
		}
	}
	public class FontInfo
	{
		private FontFamilyInfo fontFamilyInfo;

		private float size;

		private FontStyle style;

		public FontFamilyInfo FontFamilyInfo
		{
			get
			{
				return fontFamilyInfo;
			}
			set
			{
				fontFamilyInfo = value;
			}
		}

		public string Name => fontFamilyInfo.FontFamily.Name;

		public float Size
		{
			get
			{
				return size;
			}
			set
			{
				size = value;
			}
		}

		public FontStyle Style
		{
			get
			{
				return style;
			}
			set
			{
				style = value;
			}
		}

		public FontInfo(Font font)
			: this(font.Name, font.Size, font.Style)
		{
		}

		public FontInfo(FontInfo proto)
		{
			fontFamilyInfo = proto.fontFamilyInfo;
			size = proto.size;
			style = proto.style;
		}

		public FontInfo(string name, float size, FontStyle style)
			: this(new FontFamilyInfo(new FontFamily(name)), size, style)
		{
		}

		public FontInfo(FontFamilyInfo fontFamily, float size, FontStyle style)
		{
			Debug.Assert(fontFamilyInfo != null);
			fontFamilyInfo = fontFamily;
			this.size = size;
			this.style = style;
		}

		public override string ToString()
		{
			return Name;
		}

		public override bool Equals(object obj)
		{
			if (obj == null || !(obj is FontInfo))
			{
				return false;
			}
			FontInfo fontInfo = (FontInfo)obj;
			return string.Equals(Name, fontInfo.Name, StringComparison.CurrentCultureIgnoreCase) && size == fontInfo.size && style == fontInfo.style;
		}

		public bool Equals(string name, float size, FontStyle fontStyle)
		{
			return string.Equals(Name, name, StringComparison.CurrentCultureIgnoreCase) && this.size == size && style == fontStyle;
		}

		public bool Equals(Font font)
		{
			return font != null && Equals(font.Name, font.Size, font.Style);
		}

		public override int GetHashCode()
		{
			return fontFamilyInfo.GetHashCode() ^ (int)size ^ (int)style;
		}
	}
	internal static class GraphicsToolkit
	{
		public enum TriangleDirection
		{
			Left,
			Up,
			Right,
			Down
		}

		public static bool PointInRect(RectangleF rect, PointF p)
		{
			return rect.Left <= p.X && rect.Top <= p.Y && rect.Right >= p.X && rect.Bottom >= p.Y;
		}

		public static void FillTriangle(Graphics g, int width, Point loc)
		{
			FillTriangle(g, width, loc, TriangleDirection.Down);
		}

		public static void FillTriangle(Graphics g, float size, PointF loc, TriangleDirection dir)
		{
			FillTriangle(g, size, loc, dir, Pens.Black);
		}

		public static void FillTriangle(Graphics g, float size, PointF loc, TriangleDirection dir, Pen p)
		{
			float num = loc.X;
			float num2 = loc.Y;
			switch (dir)
			{
			case TriangleDirection.Up:
				loc.X -= size / 2f;
				for (num = 0f; num < size / 2f; num += 1f)
				{
					g.DrawLine(p, loc.X + num, num2, loc.X + size - num - 1f, num2);
					num2 -= 1f;
				}
				break;
			case TriangleDirection.Down:
				loc.X -= size / 2f - 1f;
				num2 -= 1f;
				for (num = 0f; num < size / 2f; num += 1f)
				{
					g.DrawLine(p, loc.X + num, num2, loc.X + size - num, num2);
					num2 += 1f;
				}
				break;
			case TriangleDirection.Left:
				loc.Y -= size / 2f;
				for (num2 = 0f; num2 < size / 2f; num2 += 1f)
				{
					g.DrawLine(p, num, loc.Y + num2, num, loc.Y + size - num2 - 1f);
					num -= 1f;
				}
				break;
			case TriangleDirection.Right:
				loc.Y -= size / 2f;
				for (num2 = 0f; num2 < size / 2f; num2 += 1f)
				{
					g.DrawLine(p, num, loc.Y + num2, num, loc.Y + size - num2 - 1f);
					num += 1f;
				}
				break;
			}
		}

		public static void DrawTransparentBlock(Graphics g, Rectangle rect)
		{
			g.SetClip(rect);
			int num = 0;
			int num2 = 0;
			for (int i = rect.Top; i < rect.Bottom; i += 5)
			{
				num = num2++;
				for (int j = rect.Left; j < rect.Right; j += 5)
				{
					g.FillRectangle((num++ % 2 == 1) ? Brushes.White : Brushes.Gainsboro, j, i, 5, 5);
				}
			}
			g.ResetClip();
		}

		public static void Draw3DButton(Graphics g, Rectangle rect, bool isPressed)
		{
			Rectangle rect2 = rect;
			rect2.Offset(1, 1);
			g.FillRectangle(isPressed ? Brushes.Black : Brushes.White, rect2);
			g.DrawLine(Pens.Black, rect.X + 1, rect.Y, rect.Right - 1, rect.Y);
			g.DrawLine(Pens.Black, rect.X + 1, rect.Bottom, rect.Right - 1, rect.Bottom);
			g.DrawLine(Pens.Black, rect.X, rect.Y + 1, rect.X, rect.Bottom - 1);
			g.DrawLine(Pens.Black, rect.Right, rect.Y + 1, rect.Right, rect.Bottom - 1);
			Rectangle rect3 = rect;
			rect3.Inflate(-1, -1);
			rect3.Offset(1, 1);
			g.FillRectangle(Brushes.LightGray, rect3);
			g.DrawLines(isPressed ? Pens.White : Pens.DimGray, new Point[3]
			{
				new Point(rect.Left + 1, rect.Bottom - 1),
				new Point(rect.Right - 1, rect.Bottom - 1),
				new Point(rect.Right - 1, rect.Top + 1)
			});
		}

		public static Color ConvertWebColor(string code)
		{
			if (code.StartsWith("#"))
			{
				code = code.Substring(1);
			}
			if (code.Length == 3)
			{
				return Color.FromArgb(Convert.ToInt32(code.Substring(0, 1), 16), Convert.ToInt32(code.Substring(1, 1), 16), Convert.ToInt32(code.Substring(2, 1), 16));
			}
			if (code.Length == 6)
			{
				return Color.FromArgb(Convert.ToInt32(code.Substring(0, 2), 16), Convert.ToInt32(code.Substring(2, 2), 16), Convert.ToInt32(code.Substring(4, 2), 16));
			}
			if (code.Length == 8)
			{
				return Color.FromArgb(Convert.ToInt32(code.Substring(0, 2), 16), Convert.ToInt32(code.Substring(2, 2), 16), Convert.ToInt32(code.Substring(3, 2), 16), Convert.ToInt32(code.Substring(4, 2), 16));
			}
			return Color.Empty;
		}
	}
}
namespace WinformsUserControl.Common
{
	[Editor("WinformsUserControl.Common.TabInfoCollectionEditor", typeof(UITypeEditor))]
	public class TabInfoCollection : Collection<TabInfo>
	{
		private SearchTabControl owner;

		public const string Prefix = "winfTab_";

		private int nextTabIndex = 1;

		private TabInfoCollection()
		{
		}

		public TabInfoCollection(SearchTabControl _owner)
		{
			owner = _owner;
		}

		public TabInfoCollection(IList<TabInfo> list, SearchTabControl _owner)
			: base(list)
		{
			owner = _owner;
			if (list != null && list.Any())
			{
				int result;
				int num = ((IEnumerable<TabInfo>)list).Max((Func<TabInfo, int>)((TabInfo t) => int.TryParse(t.Name?.Split('_').LastOrDefault() ?? "0", out result) ? result : 0));
				nextTabIndex = num + 1;
			}
		}

		public void Add(string text)
		{
			Add(new TabInfo
			{
				Name = string.Format("{0}{1:D3}", "winfTab_", nextTabIndex++),
				Text = text
			});
		}

		protected override void InsertItem(int index, TabInfo item)
		{
			if (string.IsNullOrEmpty(item?.Name))
			{
				item.Name = string.Format("{0}{1:D3}", "winfTab_", nextTabIndex++);
				item.Text = string.Format("{0}{1:D3}", "winfTab_", nextTabIndex);
			}
			nextTabIndex = Math.Max(nextTabIndex, (base.Items?.Count ?? 0) + 2);
			base.InsertItem(index, item);
		}
	}
	public class TabInfo
	{
		public string Name { get; set; }

		public string Text { get; set; }

		public object Tag { get; set; }

		public override string ToString()
		{
			return Name ?? "";
		}
	}
	public class TabInfoCollectionEditor : CollectionEditor
	{
		public TabInfoCollectionEditor(Type type)
			: base(type)
		{
		}

		protected override string GetDisplayText(object value)
		{
			return ((TabInfo)value).ToString();
		}
	}
}
namespace WinformsUserControl.Collections
{
	public class CommonControlCollection<TControl> : BaseCollection, IList, ICollection, IEnumerable where TControl : ButtonBase
	{
		protected BaseMultControl<TControl> _ownerControl;

		private ArrayList items = new ArrayList();

		private CollectionChangeEventHandler onCollectionChanged;

		protected override ArrayList List => items;

		object IList.this[int index]
		{
			get
			{
				return items[index];
			}
			set
			{
				throw new NotSupportedException();
			}
		}

		public override int Count => items.Count;

		public bool IsFixedSize
		{
			get
			{
				throw new NotImplementedException();
			}
		}

		public TControl this[string columnName]
		{
			get
			{
				if (columnName == null)
				{
					throw new ArgumentNullException("columnName");
				}
				int count = items.Count;
				for (int i = 0; i < count; i++)
				{
					TControl val = (TControl)items[i];
					if (string.Equals(val.Name, columnName, StringComparison.OrdinalIgnoreCase))
					{
						return val;
					}
				}
				return null;
			}
		}

		public event CollectionChangeEventHandler CollectionChanged
		{
			add
			{
				onCollectionChanged = (CollectionChangeEventHandler)Delegate.Combine(onCollectionChanged, value);
			}
			remove
			{
				onCollectionChanged = (CollectionChangeEventHandler)Delegate.Remove(onCollectionChanged, value);
			}
		}

		public CommonControlCollection(BaseMultControl<TControl> _baseMultControl)
		{
			_ownerControl = _baseMultControl;
		}

		public CommonControlCollection()
		{
		}

		protected virtual void OnCollectionChanged(CollectionChangeEventArgs e)
		{
			if (onCollectionChanged != null)
			{
				onCollectionChanged(this, e);
			}
		}

		private void OnCollectionChanged(CollectionChangeEventArgs ccea, bool changeIsInsertion, Point newCurrentCell)
		{
			OnCollectionChanged(ccea);
		}

		public int Add(object value)
		{
			if (value is string text)
			{
				return Add(new TControl
				{
					Text = text
				});
			}
			return Add((TControl)value);
		}

		public virtual int Add(TControl TControl)
		{
			TControl.Margin = new Padding(3, 3, 0, 0);
			TControl.AutoSize = true;
			items.Add(TControl);
			_ownerControl.Controls.Add(TControl);
			OnCollectionChanged(new CollectionChangeEventArgs(CollectionChangeAction.Add, _ownerControl), changeIsInsertion: false, new Point(-1, -1));
			return 1;
		}

		private void AddBySelf(TControl value)
		{
			items.Add(value);
		}

		public bool Contains(object value)
		{
			return items.Contains((TControl)value);
		}

		public void Clear()
		{
			items.Clear();
		}

		public int IndexOf(object value)
		{
			return items.IndexOf((TControl)value);
		}

		public void Insert(int index, object value)
		{
			items.Insert(index, (TControl)value);
		}

		public void Remove(object value)
		{
			items.Remove((TControl)value);
		}

		public void RemoveAt(int index)
		{
			items.RemoveAt(index);
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			return items.GetEnumerator();
		}

		public virtual bool Checked()
		{
			for (int i = 0; i < items.Count; i++)
			{
				if (items[i] is RadioButton { Checked: not false })
				{
					return true;
				}
				if (items[i] is CheckBox { Checked: not false })
				{
					return true;
				}
			}
			return false;
		}

		public virtual object GetCheckedDisplayText()
		{
			if (typeof(TControl) == typeof(RadioButton))
			{
				for (int i = 0; i < items.Count; i++)
				{
					if (items[i] is RadioButton { Checked: not false } radioButton)
					{
						return radioButton.Text;
					}
				}
			}
			if (typeof(TControl) == typeof(CheckBox))
			{
				List<string> list = new List<string>();
				for (int j = 0; j < items.Count; j++)
				{
					if (items[j] is CheckBox { Checked: not false } checkBox)
					{
						list.Add(checkBox.Text);
					}
				}
				return list;
			}
			return null;
		}

		internal void SetValue(object value)
		{
			if (value is string y)
			{
				for (int i = 0; i < items.Count; i++)
				{
					if (items[i] is RadioButton radioButton && EqualityComparer<string>.Default.Equals(radioButton.Text, y))
					{
						radioButton.Checked = true;
						break;
					}
				}
			}
			else
			{
				if (!(value is IList<string> list) || list == null)
				{
					return;
				}
				Dictionary<string, string> dictionary = list.ToDictionary<string, string, string>((string p) => p, (string p) => p);
				for (int j = 0; j < items.Count; j++)
				{
					if (items[j] is CheckBox checkBox && dictionary.ContainsKey(checkBox.Text))
					{
						checkBox.Checked = true;
					}
				}
			}
		}

		internal IEnumerable<TControl> GetCheckedControls()
		{
			for (int i = 0; i < items.Count; i++)
			{
				object obj = items[i];
				if (obj is RadioButton rb1 && rb1.Checked)
				{
					yield return (TControl)items[i];
				}
				obj = items[i];
				if (obj is CheckBox cb2 && cb2.Checked)
				{
					yield return (TControl)items[i];
				}
			}
		}

		bool IList.get_IsReadOnly()
		{
			return base.IsReadOnly;
		}
	}
	internal class HyControlCollection<TControl> : Control.ControlCollection where TControl : Control
	{
		public HyControlCollection(Control owner)
			: base(owner)
		{
		}

		public override void Add(Control value)
		{
			if (value != null && base.Owner is BaseLineInput<TControl> baseLineInput)
			{
				if (value.Name == "lable_" + baseLineInput.InitName + "_require" || value.Name == "lable_" + baseLineInput.InitName || value == baseLineInput.InputBox || value == baseLineInput.RemarkPanel)
				{
					base.Add(value);
				}
				if (baseLineInput.InputBox == null && value is TControl)
				{
					base.Add(value);
					baseLineInput.InputBox = (TControl)value;
				}
			}
		}

		public override void AddRange(Control[] controls)
		{
			base.AddRange(controls);
		}

		public override void Remove(Control value)
		{
			base.Remove(value);
			if (value != null && base.Owner is BaseLineInput<TControl> baseLineInput && baseLineInput.InputBox == value)
			{
				baseLineInput.InputBox = null;
			}
		}
	}
}
namespace WinformsUserControl.Args
{
	[Serializable]
	[ComVisible(true)]
	public delegate void TabEventHandler(object sender, TabEventArgs e);
	public class TabEventArgs : EventArgs
	{
		public TabInfo TabInfo { get; internal set; }

		public TabEventArgs(TabInfo tabInfo)
		{
			TabInfo = tabInfo;
		}
	}
}
