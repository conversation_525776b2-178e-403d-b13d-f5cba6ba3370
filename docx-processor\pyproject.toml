[project]
name = "docx-processor"
version = "0.1.0"
description = "Word document processor for table filling and splitting"
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
dependencies = [
    "python-docx>=1.1.0",
    "openpyxl>=3.1.0",
]
requires-python = ">=3.8.1"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
]
