using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Xml;
using Web.Security.Util;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("CommonApi")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("CommonApi")]
[assembly: AssemblyCopyright("Copyright ©  2022")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("b4f3211b-4e07-4375-8b4e-6013c2070c5d")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace CommonApi.Util;

public class ApiConfigHelper
{
	public static string filePath = "apiConfig.xml";

	private static Dictionary<string, string> ApiConfigs = null;

	public const string SM4_KEY = "86C63180C2806ED1F47B859DE501215B";

	public static Dictionary<string, string> GetApiConfigs()
	{
		if (ApiConfigs != null)
		{
			return ApiConfigs;
		}
		string text = ConfigurationManager.AppSettings.Get("profile");
		if (text != null)
		{
			filePath = text;
		}
		else
		{
			if (!File.Exists("bid-anhui.exe.config"))
			{
				return new Dictionary<string, string>();
			}
			ExeConfigurationFileMap exeConfigurationFileMap = new ExeConfigurationFileMap();
			exeConfigurationFileMap.ExeConfigFilename = "bid-anhui.exe.config";
			Configuration configuration = ConfigurationManager.OpenMappedExeConfiguration(exeConfigurationFileMap, ConfigurationUserLevel.None);
			filePath = configuration.AppSettings.Settings["profile"].Value ?? filePath;
		}
		XmlDocument xmlDocument = new XmlDocument();
		xmlDocument.Load(filePath);
		XmlNodeList xmlNodeList = xmlDocument.SelectNodes("//ApiConfigs/Api");
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		foreach (XmlElement item in xmlNodeList)
		{
			XmlAttributeCollection attributes = item.Attributes;
			string key = attributes?[0]?.Value ?? "-nokey-";
			if (!dictionary.ContainsKey(key))
			{
				dictionary.Add(key, attributes?[1]?.Value);
			}
		}
		ApiConfigs = dictionary;
		return dictionary;
	}

	public static string GetValueByKey(string key)
	{
		Dictionary<string, string> apiConfigs = GetApiConfigs();
		if (apiConfigs.ContainsKey(key))
		{
			if (key.Contains("Url") && !apiConfigs[key].StartsWith("http"))
			{
				apiConfigs[key] = SM4Util.DecryptEcb("86C63180C2806ED1F47B859DE501215B", apiConfigs[key]);
			}
			return apiConfigs[key];
		}
		return null;
	}
}
