Metadata-Version: 2.1
Name: mccabe
Version: 0.7.0
Summary: <PERSON><PERSON><PERSON><PERSON> checker, plugin for flake8
Home-page: https://github.com/pycqa/mccabe
Author: <PERSON><PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON> Cordasco
Maintainer-email: g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
License: Expat license
Keywords: flake8 mccabe
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Quality Assurance
Requires-Python: >=3.6
License-File: LICENSE

McCabe complexity checker
=========================

Ned's script to check Mc<PERSON>abe complexity.

This module provides a plugin for ``flake8``, the Python code checker.


Installation
------------

You can install, upgrade, or uninstall ``mccabe`` with these commands::

  $ pip install mccabe
  $ pip install --upgrade mccabe
  $ pip uninstall mccabe


Standalone script
-----------------

The complexity checker can be used directly::

  $ python -m mccabe --min 5 mccabe.py
  ("185:1: 'PathGraphingAstVisitor.visitIf'", 5)
  ("71:1: 'PathGraph.to_dot'", 5)
  ("245:1: 'McCabeChecker.run'", 5)
  ("283:1: 'main'", 7)
  ("203:1: 'PathGraphingAstVisitor.visitTryExcept'", 5)
  ("257:1: 'get_code_complexity'", 5)


Plugin for Flake8
-----------------

When both ``flake8 2+`` and ``mccabe`` are installed, the plugin is
available in ``flake8``::

  $ flake8 --version
  2.0 (pep8: 1.4.2, pyflakes: 0.6.1, mccabe: 0.2)

By default the plugin is disabled.  Use the ``--max-complexity`` switch to
enable it.  It will emit a warning if the McCabe complexity of a function is
higher than the provided value::

    $ flake8 --max-complexity 10 coolproject
    ...
    coolproject/mod.py:1204:1: C901 'CoolFactory.prepare' is too complex (14)

This feature is quite useful for detecting over-complex code.  According to McCabe,
anything that goes beyond 10 is too complex.

Flake8 has many features that mccabe does not provide. Flake8 allows users to
ignore violations reported by plugins with ``# noqa``. Read more about this in
`their documentation
<http://flake8.pycqa.org/en/latest/user/violations.html#in-line-ignoring-errors>`__.
To silence violations reported by ``mccabe``, place your ``# noqa: C901`` on
the function definition line, where the error is reported for (possibly a
decorator).


Links
-----

* Feedback and ideas: http://mail.python.org/mailman/listinfo/code-quality

* Cyclomatic complexity: http://en.wikipedia.org/wiki/Cyclomatic_complexity

* Ned Batchelder's script:
  http://nedbatchelder.com/blog/200803/python_code_complexity_microtool.html

* McCabe complexity: http://en.wikipedia.org/wiki/Cyclomatic_complexity


Changes
-------

0.7.0 - 2021-01-23
``````````````````

* Drop support for all versions of Python lower than 3.6

* Add support for Python 3.8, 3.9, and 3.10

* Fix option declaration for Flake8

0.6.1 - 2017-01-26
``````````````````

* Fix signature for ``PathGraphingAstVisitor.default`` to match the signature
  for ``ASTVisitor``

0.6.0 - 2017-01-23
``````````````````

* Add support for Python 3.6

* Fix handling for missing statement types

0.5.3 - 2016-12-14
``````````````````

* Report actual column number of violation instead of the start of the line

0.5.2 - 2016-07-31
``````````````````

* When opening files ourselves, make sure we always name the file variable

0.5.1 - 2016-07-28
``````````````````

* Set default maximum complexity to -1 on the class itself

0.5.0 - 2016-05-30
``````````````````

* PyCon 2016 PDX release

* Add support for Flake8 3.0

0.4.0 - 2016-01-27
``````````````````

* Stop testing on Python 3.2

* Add support for async/await keywords on Python 3.5 from PEP 0492

0.3.1 - 2015-06-14
``````````````````

* Include ``test_mccabe.py`` in releases.

* Always coerce the ``max_complexity`` value from Flake8's entry-point to an
  integer.

0.3 - 2014-12-17
````````````````

* Computation was wrong: the mccabe complexity starts at 1, not 2.

* The ``max-complexity`` value is now inclusive.  E.g.: if the
  value is 10 and the reported complexity is 10, then it passes.

* Add tests.


0.2.1 - 2013-04-03
``````````````````

* Do not require ``setuptools`` in setup.py.  It works around an issue
  with ``pip`` and Python 3.


0.2 - 2013-02-22
````````````````

* Rename project to ``mccabe``.

* Provide ``flake8.extension`` setuptools entry point.

* Read ``max-complexity`` from the configuration file.

* Rename argument ``min_complexity`` to ``threshold``.


0.1 - 2013-02-11
````````````````
* First release


