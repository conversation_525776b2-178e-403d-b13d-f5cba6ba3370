"""
工具函数模块

提供文档处理相关的辅助功能
"""

import os
import re
import logging
from typing import List, Dict, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


def find_docx_files(directory: str, pattern: str = "办公桌") -> List[str]:
    """
    在指定目录中查找匹配模式的docx文件
    
    Args:
        directory: 搜索目录
        pattern: 文件名模式
        
    Returns:
        匹配的文件路径列表
    """
    docx_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(".docx") and pattern in file:
                # 跳过已处理的文件
                if any(keyword in file for keyword in ["技术参数", "组件材料", "国网安徽省电力有限公司"]):
                    continue
                    
                file_path = os.path.join(root, file)
                docx_files.append(file_path)
    
    return docx_files


def extract_project_folders(base_dir: str) -> List[Dict[str, str]]:
    """
    提取项目文件夹信息
    
    Args:
        base_dir: 基础目录
        
    Returns:
        项目文件夹信息列表
    """
    project_folders = []
    
    for root, dirs, files in os.walk(base_dir):
        folder_name = os.path.basename(root)
        
        # 查找包含项目ID的文件夹
        match = re.search(r'G00E-\d+-\d+', folder_name)
        if match:
            project_id = match.group()
            
            # 查找该文件夹下的docx文件
            docx_files = [f for f in files if f.endswith('.docx')]
            
            if docx_files:
                project_folders.append({
                    'project_id': project_id,
                    'folder_path': root,
                    'docx_files': docx_files
                })
    
    return project_folders


def validate_file_structure(file_path: str) -> Dict[str, bool]:
    """
    验证文件结构
    
    Args:
        file_path: 文件路径
        
    Returns:
        验证结果
    """
    validation_result = {
        'file_exists': False,
        'is_docx': False,
        'folder_has_project_id': False,
        'is_readable': False
    }
    
    try:
        # 检查文件是否存在
        validation_result['file_exists'] = os.path.exists(file_path)
        
        # 检查是否为docx文件
        validation_result['is_docx'] = file_path.endswith('.docx')
        
        # 检查文件夹是否包含项目ID
        folder_path = os.path.dirname(file_path)
        folder_name = os.path.basename(folder_path)
        validation_result['folder_has_project_id'] = bool(re.search(r'G00E-\d+-\d+', folder_name))
        
        # 检查文件是否可读
        if validation_result['file_exists']:
            validation_result['is_readable'] = os.access(file_path, os.R_OK)
            
    except Exception as e:
        logger.error(f"文件验证失败: {file_path}, 错误: {str(e)}")
    
    return validation_result


def create_backup(file_path: str) -> Optional[str]:
    """
    创建文件备份
    
    Args:
        file_path: 原始文件路径
        
    Returns:
        备份文件路径
    """
    try:
        backup_path = f"{file_path}.backup"
        
        # 如果备份已存在，添加时间戳
        if os.path.exists(backup_path):
            import time
            timestamp = int(time.time())
            backup_path = f"{file_path}.backup.{timestamp}"
        
        # 复制文件
        import shutil
        shutil.copy2(file_path, backup_path)
        
        logger.info(f"创建备份: {backup_path}")
        return backup_path
        
    except Exception as e:
        logger.error(f"创建备份失败: {file_path}, 错误: {str(e)}")
        return None


def format_processing_report(results: List[Dict]) -> str:
    """
    格式化处理报告
    
    Args:
        results: 处理结果列表
        
    Returns:
        格式化的报告字符串
    """
    report = []
    report.append("=" * 60)
    report.append("Word文档处理报告")
    report.append("=" * 60)
    
    successful = [r for r in results if "error" not in r]
    failed = [r for r in results if "error" in r]
    
    report.append(f"总处理文件数: {len(results)}")
    report.append(f"成功处理: {len(successful)}")
    report.append(f"处理失败: {len(failed)}")
    report.append("")
    
    if successful:
        report.append("成功处理的文件:")
        report.append("-" * 40)
        for result in successful:
            report.append(f"项目ID: {result['project_id']}")
            report.append(f"  主文档: {result['main_doc']}")
            report.append(f"  技术参数表: {result['tech_params']}")
            report.append(f"  组件配置表: {result['component_config']}")
            report.append(f"  映射项数: {result['mapping_count']}")
            report.append("")
    
    if failed:
        report.append("处理失败的文件:")
        report.append("-" * 40)
        for result in failed:
            report.append(f"错误: {result['error']}")
            report.append("")
    
    report.append("=" * 60)
    
    return "\n".join(report)


def get_test_folder(base_dir: str) -> Optional[str]:
    """
    获取第一个包含项目ID的测试文件夹
    
    Args:
        base_dir: 基础目录
        
    Returns:
        测试文件夹路径
    """
    for root, dirs, files in os.walk(base_dir):
        folder_name = os.path.basename(root)
        
        # 查找包含项目ID的文件夹
        if re.search(r'G00E-\d+-\d+', folder_name):
            # 检查是否包含docx文件
            docx_files = [f for f in files if f.endswith('.docx') and '办公桌' in f]
            if docx_files:
                return root
    
    return None
