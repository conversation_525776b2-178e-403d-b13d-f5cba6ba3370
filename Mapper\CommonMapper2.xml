<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--
		附件信息+  保证金主表信息
	-->
	<select id="insertAttachInfoTemp">
		--保证金主表信息
		insert into db1.OS_ZB_SUPPLIER_BID_ATTACHMENT SELECT * FROM OS_ZB_SUPPLIER_BID_ATTACHMENT WHERE MARK_NO ='{markNo}';
		--保证金主表信息
		insert into db1.OS_ZB_SUPPLIER_DEPOSIT SELECT * FROM OS_ZB_SUPPLIER_DEPOSIT WHERE PROJECT_NO ='{projectNo}';
		--保证金详情
		insert into db1.OS_ZB_SUPPLIER_DEPOSIT_DETAIL SELECT * FROM OS_ZB_SUPPLIER_DEPOSIT_DETAIL WHERE MARK_NO ='{markNo}';
		--授权人信息
		insert into db1.OS_ZB_SUPPLIER_AUTH_PERSON_INFO SELECT * FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO;
		--授权人关联信息
		insert into db1.OS_ZB_AUTH_PERSON_RELATE SELECT * FROM OS_ZB_AUTH_PERSON_RELATE;
		--字典信息
		insert into db1.CM_C_DICTIONARY_DETAILS SELECT * FROM CM_C_DICTIONARY_DETAILS;
		--响应情况信息
		insert into db1.OS_ZB_SUPPLIER_RESPONSE_INFO SELECT * FROM OS_ZB_SUPPLIER_RESPONSE_INFO WHERE MARK_NO ='{markNo}';

		--更新供应商id信息
		--标书信息
		update db1.OS_ZB_SUPPLIER_BID_ATTACHMENT SET SUPPLIER_ID ='{supplierId}';
		--保证金
		update db1.OS_ZB_SUPPLIER_DEPOSIT SET SUPPLIER_ID ='{supplierId}';
		--保证金详情
		update db1.OS_ZB_SUPPLIER_DEPOSIT_DETAIL SET SUPPLIER_ID ='{supplierId}';
		--授权人信息
		update db1.OS_ZB_SUPPLIER_AUTH_PERSON_INFO SET SUPPLIER_ID ='{supplierId}';
		--商务、技术响应信息
		update db1.OS_ZB_SUPPLIER_RESPONSE_INFO SET SUPPLIER_ID ='{supplierId}';
		--股权结构信息
		update db1.OS_ZB_SUPPLIER_BIAO_EQUITY_INFO SET SUPPLIER_ID ='{supplierId}';
		--试验报告
		update db1.OS_ZB_SUPPLIER_BIAO_REPORT_INFO SET SUPPLIER_ID ='{supplierId}';
		--产品信息
		update db1.OS_ZB_SUPPLIER_PRODUCT_INFO SET SUPPLIER_ID ='{supplierId}';
		--产品部件
		update db1.OS_ZB_SUPPLIER_PARTS_INFO SET SUPPLIER_ID ='{supplierId}';
		--供应商信息
		update db1.OS_ZB_SUPPLIER_INFO SET ID ='{supplierId}';
		--项目信息
		update db1.OS_ZB_PURCHASE_PROJECT_INFO SET SUPPLIER_ID ='{supplierId}';
		--资质证书
		update db1.OS_ZB_SUPPLIER_BIAO_CERT_INFO SET SUPPLIER_ID ='{supplierId}';
		--销售信息
		update db1.OS_ZB_SUPPLIER_BIAO_SALES_INFO SET SUPPLIER_ID ='{supplierId}';
		--财务信息
		update db1.OS_ZB_SUPPLIER_BIAO_FINANCE_INFO SET SUPPLIER_ID ='{supplierId}';
		--试验设备
		update db1.OS_ZB_SUPPLIER_BIAO_LABEQP_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产设备
		update db1.OS_ZB_SUPPLIER_BIAO_MEQP_INFO SET SUPPLIER_ID ='{supplierId}';
		--项目信息
		update db1.OS_ZB_PURCHASE_PROJECT_INFO SET SUPPLIER_ID ='{supplierId}';
		--资质业绩核实
		update db1.OS_ZB_SUPPLIER_QUALIFY_INFO SET SUPPLIER_ID ='{supplierId}';
		--信息信用
		update db1.OS_ZB_SUPPLIER_BIAO_CREDIT_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产场地
		update db1.OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE SET SUPPLIER_ID ='{supplierId}';
		--运行评价
		update db1.OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE SET SUPPLIER_ID ='{supplierId}';

		--基础数据更新供应商id
		--财务数据
		update db1.OS_ZB_SUPPLIER_FINANCE_INFO SET SUPPLIER_ID ='{supplierId}';
		--信息信用
		update db1.OS_ZB_SUPPLIER_CREDIT_INFO SET SUPPLIER_ID ='{supplierId}';
		--股权结构
		update db1.OS_ZB_SUPPLIER_EQUITY_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产产地
		update db1.OS_ZB_SUPPLIER_PRODUCT_PLACE SET SUPPLIER_ID ='{supplierId}';

		--更新供应商id信息
		--附件信息
		insert into db1.SYS_FILE_INFO
		--pdf文件/word文件(商务+技术的支持文件)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		INNER JOIN
		OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.ID = SYS_FILE_INFO.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BID_ATTACHMENT.PROJECT_NO = '{projectNo}' AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = '{markNo}'
		union
		--ecp压缩文件+价格压缩文件+上架商品压缩包
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;%'
		union
		--保证金明细附件(按项目)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT_ATTACH' and RELATED_ID like '{projectNo}'
		union
		--授权人信息
		select SYS_FILE_INFO.*
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO inner JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID;
	</select>


	<select id="selectRelateInfo">
		SELECT {mainTable}.*,
		'已上传数('||(select count(id) from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID = {mainTable}.ID and SYS_FILE_INFO.RELATED_KEY = '{relatedKey1}')||')' as file1CountStr, --附件1个数
		'已上传数('||(select count(id) from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID = {mainTable}.ID and SYS_FILE_INFO.RELATED_KEY = '{relatedKey2}')||')' as file2CountStr --附件2个数
		FROM   {mainTable} where {mainTable}.PROJECT_NO ='{projectNo}' and {mainTable}.MARK_NO ='{markNo}'
		and ({mainTable}.PACK_NAME ='{packName}' or {mainTable}.PACK_NAME is null)
	</select>


	<!--技术模块查询-->
	<select id="selectTechModel">
		--销售业绩
		SELECT OS_ZB_SUPPLIER_BIAO_SALES_INFO.*,
		contract_file.ID as  contract_id,
		contract_file.FILE_FORMAT as _file_format,
		contract_file.FILE_DISP_NAME as _file_disp_name,
		contract_file.FILE_PATH as _file_path,
		bill_file.ID as bill_id,
		bill_file.FILE_FORMAT as _file_format1,
		bill_file.FILE_DISP_NAME as _file_disp_name1,
		bill_file.FILE_PATH as _file_path1
		FROM OS_ZB_SUPPLIER_BIAO_SALES_INFO
		left join sys_file_info contract_file on contract_file.RELATED_KEY='contract' and OS_ZB_SUPPLIER_BIAO_SALES_INFO.ID=contract_file.RELATED_ID
		left join sys_file_info bill_file on bill_file.RELATED_KEY='bill' and OS_ZB_SUPPLIER_BIAO_SALES_INFO.ID=bill_file.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_SALES_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_SALES_INFO.CREATE_TIME desc;

		--试验报告
		select OS_ZB_SUPPLIER_BIAO_REPORT_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_REPORT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='sybgImg' and OS_ZB_SUPPLIER_BIAO_REPORT_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_REPORT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_REPORT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_REPORT_INFO.CREATE_TIME desc;

		--资质证书
		select OS_ZB_SUPPLIER_BIAO_CERT_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_CERT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='zzzsImg' 
		<!--and OS_ZB_SUPPLIER_BIAO_CERT_INFO.ID=sys_file_info.RELATED_ID 此处修改为引用数据-->
		and OS_ZB_SUPPLIER_BIAO_CERT_INFO.RELATE_ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_CERT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_CERT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_CERT_INFO.CREATE_TIME desc;

		--试验设备
		select OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_LABEQP_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='shebeifj' and OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.SUPPLIER_ID ='{supplierId}' and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.CREATE_TIME desc;

		--生产设备
		select OS_ZB_SUPPLIER_BIAO_MEQP_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_MEQP_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='zhizaofj' and OS_ZB_SUPPLIER_BIAO_MEQP_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_MEQP_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_MEQP_INFO.SUPPLIER_ID ='{supplierId}'      and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_MEQP_INFO.CREATE_TIME desc;

		--产品信息
		select OS_ZB_SUPPLIER_PRODUCT_INFO.*,(case when AGENT ='NO' then '否' else '是' end) as agent_name,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_PRODUCT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='agentFj' and OS_ZB_SUPPLIER_PRODUCT_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_PRODUCT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_PRODUCT_INFO.CREATE_TIME desc;

		--部件信息(无附件)
		select OS_ZB_SUPPLIER_PARTS_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_PARTS_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='-无附件-' and OS_ZB_SUPPLIER_PARTS_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_PARTS_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_PARTS_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_PARTS_INFO.CREATE_TIME desc;

		--生产场地
		select OS_ZB_SUPPLIER_PRODUCT_PLACE.*,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.PROJECT_NO,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.MARK_NO,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.PACK_NAME,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE
		inner join OS_ZB_SUPPLIER_PRODUCT_PLACE  on  OS_ZB_SUPPLIER_PRODUCT_PLACE.ID = OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.RELATE_ID
		left join sys_file_info  on sys_file_info.RELATED_KEY='zmcl' and OS_ZB_SUPPLIER_PRODUCT_PLACE.ID = sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME)&lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.CREATE_TIME desc;

		--运行评价
		select OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE
		left join sys_file_info  on sys_file_info.RELATED_KEY='attach' and OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE.ID = sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME)&lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE.CREATE_TIME desc;
	</select>


	<!--商务模块查询-->
	<select id="selectBusinessModel">
		--股权信息(高管信息不生成)
		SELECT OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_EQUITY_INFO
		LEFT JOIN
		sys_file_info ON sys_file_info.RELATED_KEY = 'gdyyzzFj' AND
		OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.MARK_NO = '{markNo}' and OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		and OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.PEOPEL_TYPE !='gaoguan'
		ORDER BY OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.CREATE_TIME DESC,sys_file_info.UPLOAD_TIME desc;

		--股权信息证明文件
		SELECT
		sys_file_info.ID,
		sys_file_info.FILE_FORMAT,
		sys_file_info.FILE_DISP_NAME,
		sys_file_info.FILE_PATH
		FROM sys_file_info
		WHERE sys_file_info.RELATED_KEY = 'gqjgFj' AND sys_file_info.RELATED_ID like  '%{markNo}{packName}%'
		ORDER BY sys_file_info.UPLOAD_TIME desc;

		--财务信息
		SELECT OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_FINANCE_INFO
		LEFT JOIN
		sys_file_info ON  sys_file_info.RELATED_KEY = 'cwsjbg' and
		OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.CREATE_TIME DESC,sys_file_info.UPLOAD_TIME desc;

		--信用信息
		SELECT  FILE_DISP_NAME, FILE_PATH, FILE_FORMAT
		FROM OS_ZB_SUPPLIER_BIAO_CREDIT_INFO
		WHERE OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.UPDATE_TIME DESC;

		---资质业绩
		SELECT OS_ZB_SUPPLIER_QUALIFY_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_QUALIFY_INFO
		LEFT JOIN
		sys_file_info ON sys_file_info.RELATED_KEY = 'zzyjhszm'  AND
		OS_ZB_SUPPLIER_QUALIFY_INFO.ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_QUALIFY_INFO.MARK_NO = '{markNo}'   and OS_ZB_SUPPLIER_QUALIFY_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_QUALIFY_INFO.CREATE_TIME DESC,sys_file_info.UPLOAD_TIME desc;

		--供应商基本信息
		SELECT OS_ZB_SUPPLIER_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_INFO
		LEFT JOIN
		sys_file_info ON
		OS_ZB_SUPPLIER_INFO.SZHYZZ_ATTACHID = sys_file_info.ID
		WHERE OS_ZB_SUPPLIER_INFO.ID ='{supplierId}';
	</select>


	<!--校验相关文件上传信息-->
	<select  id="checkFileExport">

		--商务支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务支持文件未导入') as WARN_INO  from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null

		union
		--技术支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null


		union

		---到标的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null


		union

		---到标的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null

		union

		---到标的技术价格文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 价格文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 价格文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术开标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 开标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术开标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 开标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的上架商品
		
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  || ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;%' AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND '{goodsImpBtn}'='YES'

		union
		---到包的上架商品
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || ',' ||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME|| ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;' || OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'PACK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND  '{goodsImpBtn}'='YES'

	</select>



	<!--获取签章模块导入的信息-->
	<select  id="getUploadFiles_old">

		--商务支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||"\支持文件" as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		--技术支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union

		---到标的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\商务\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union

		---到标的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union

		---到标的技术价格文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\价格文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\价格文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO


		union

		---到包的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'


		union

		---到标的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union
		---到包的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\上架商品' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union

		---到标的投标授权人
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\投标授权人信息' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO INNER JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID

		union
		---到标的保证金附件
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金信息' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM SYS_FILE_INFO where RELATED_ID='{projectNo}';
	</select>


	<!--获取签章模块导入的信息-->
	<select  id="getUploadFiles">

		--商务支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		--技术支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union

		---到标的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\商务' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union

		---到标的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union

		---到标的技术价格文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术开标文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\开标' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术开标文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\开标\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO


		union

		---到包的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'


		union

		---到标的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union
		---到标的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID LIKE '%'||OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%'  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union
		---到包的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union

		---到标的投标授权人
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\投标授权人' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO INNER JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID

		union
		---到标的保证金附件
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM SYS_FILE_INFO where RELATED_ID='{projectNo}';
	</select>
	
</mapper>