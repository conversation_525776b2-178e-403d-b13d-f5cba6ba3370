from docx import Document
import os
import shutil

# Function to fill and split tables
def process_docx(input_path, output_base):
    doc = Document(input_path)

    # Process document (dummy implementation, real logic should be here)
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                cell.text = cell.text.replace("(填写)", "已填写")

    # Save modified document
    filled_doc_path = f"{output_base}.docx"
    doc.save(filled_doc_path)

    print(f"Filled and saved: {filled_doc_path}")

    # Split tables into separate documents
    tech_params_doc = Document()
    component_config_doc = Document()

    # Assume the first two tables are tech parameters, the third is component config (dummy logic)
    for i, table in enumerate(doc.tables):
        if i < 2:
            new_table = tech_params_doc.add_table(rows=0, cols=len(table.columns))
            for row in table.rows:
                cells = row.cells
                new_cells = new_table.add_row().cells
                for j in range(len(cells)):
                    new_cells[j].text = cells[j].text
            tech_params_doc.add_paragraph()  # Add a blank line between tables
        else:
            new_table = component_config_doc.add_table(rows=0, cols=len(table.columns))
            for row in table.rows:
                cells = row.cells
                new_cells = new_table.add_row().cells
                for j in range(len(cells)):
                    new_cells[j].text = cells[j].text
            component_config_doc.add_paragraph()

    tech_params_path = f"{output_base}-技术参数特征表.docx"
    component_config_path = f"{output_base}-组件材料配置表.docx"

    tech_params_doc.save(tech_params_path)
    component_config_doc.save(component_config_path)

    print(f"Split and saved: {tech_params_path}, {component_config_path}")

# Directory paths
source_dir = "F:\\2025\\7月\\国网安徽\\国网安徽电力2025年非电网及办公用品物资框架协议公开招标采购_招标文件包\\办公家具\\包1_完整招标文件_7534782003872905\\包1_技术规范书_7534780372181727"

# Process each file
for root, dirs, files in os.walk(source_dir):
    for file in files:
        if file.endswith(".docx") and "办公桌" in file:
            input_file = os.path.join(root, file)
            # Extract project ID from parent folder name (G00E-500024815-00005)
            project_id = root.split('_')[-1][:-1]
            output_base = os.path.join(root, project_id)
            process_docx(input_file, output_base)
