<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--
		响应情况信息
	-->
	<select id="selectDynamicShow">
		select
		sum(case when mainTable.type ='NEED_PRODUCT' then 1 else 0 end) as NEED_PRODUCT,
		sum(case when mainTable.type ='NEED_SALE' then 1 else 0 end) as NEED_SALE,
		sum(case when mainTable.type ='NEED_REPORT' then 1 else 0 end) as NEED_REPORT,
		sum(case when mainTable.type ='NEED_CERT' then 1 else 0 end) as NEED_CERT,
		sum(case when mainTable.type ='NEED_LABEQP' then 1 else 0 end) as NEED_LABEQP,
		sum(case when mainTable.type ='NEED_MEQP' then 1 else 0 end) as NEED_MEQP,
		sum(case when mainTable.type ='NEED_AUTH_PEOPEL' then 1 else 0 end) as NEED_AUTH_PEOPEL,
		sum(case when mainTable.type ='NEED_DEPOSIT' then 1 else 0 end) as NEED_DEPOSIT,
		sum(case when mainTable.type ='NEED_RESPONSE' then 1 else 0 end) as NEED_RESPONSE,
		sum(case when mainTable.type ='NEED_XYXX' then 1 else 0 end) as NEED_XYXX,
		sum(case when mainTable.type ='NEED_ZZYJ' then 1 else 0 end) as NEED_ZZYJ,
		sum(case when mainTable.type ='NEED_CWSJ' then 1 else 0 end) as NEED_CWSJ,
		sum(case when mainTable.type ='NEED_TEC_ZGYS' then 1 else 0 end) as NEED_TEC_ZGYS,
		sum(case when mainTable.type ='NEED_CWSJ' then 1 else 0 end) as NEED_CWSJ,
		sum(case when mainTable.type ='NEED_BUS_ZGYS' then 1 else 0 end) as NEED_BUS_ZGYS
		from

		(select 'NEED_PRODUCT' as type,NEED_ZZYJ as VALUE  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_PRODUCT ='是'

		union

		select 'NEED_SALE' as type,NEED_SALE  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_SALE ='是'

		union

		select 'NEED_REPORT' as type,NEED_REPORT  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_REPORT ='是'

		union

		select 'NEED_CERT' as type,NEED_CERT  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_CERT ='是'

		union

		select 'NEED_LABEQP' as type,NEED_LABEQP  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_LABEQP ='是'

		union

		select 'NEED_MEQP' as type,NEED_MEQP  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_MEQP ='是'

		union

		select 'NEED_AUTH_PEOPEL' as type,NEED_AUTH_PEOPEL  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_AUTH_PEOPEL='是'

		union

		select 'NEED_DEPOSIT' as type,NEED_DEPOSIT  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_DEPOSIT ='是'

		union

		select 'NEED_RESPONSE' as type,NEED_RESPONSE  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_RESPONSE ='是'

		union

		select 'NEED_XYXX' as type,NEED_XYXX  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_XYXX ='是'

		union

		select 'NEED_ZZYJ' as type,NEED_ZZYJ  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_ZZYJ ='是'

		union

		select 'NEED_CWSJ' as type,NEED_CWSJ  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_CWSJ ='是'

		union

		select 'NEED_TEC_ZGYS' as type,NEED_TEC_ZGYS  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_TEC_ZGYS ='是'

		union

		select 'NEED_BUS_ZGYS' as type,NEED_BUS_ZGYS  from OS_ZB_PURCHASE_PROJECT_INFO where project_no ='{projectNO}' and NEED_BUS_ZGYS ='是'
		) mainTable
	</select>



	<!--查询拟分包信息-->
	<select id="selectSubPack">
		select OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.*,count(SYS_FILE_INFO.id) as count from OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK
		left join SYS_FILE_INFO on OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.ID = SYS_FILE_INFO.RELATED_ID
		where
		RELATED_PAGE  ='OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK' and RELATED_KEY  ='worker_commit' and
		OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.MARK_NO ='@markNo' and OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.PACK_NAME ='@packName' and OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.SUB_PACK='@subPack'
		group by OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.id
	</select>


	<!--查询商务响应表（技术+商务）分包信息-->
	<select id="selectSubPackByResponseMark">
		SELECT OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AS keyName,
		OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AS keyValue,
		(CASE WHEN OS_ZB_SUPPLIER_RESPONSE_INFO.id IS NOT NULL THEN 1 ELSE 0 END) AS selected,
		OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY AS extraField
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_RESPONSE_INFO ON OS_ZB_SUPPLIER_RESPONSE_INFO.PROJECT_NO = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO AND
		OS_ZB_SUPPLIER_RESPONSE_INFO.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND OS_ZB_SUPPLIER_RESPONSE_INFO.PACK_NAME LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME || '%'
		AND OS_ZB_SUPPLIER_RESPONSE_INFO.SUPPLIER_ID = '{supplyId}'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE = '是' 
		GROUP BY OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		order by CAST(replace(OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME,'包','') as INTEGER)
	</select>

	<!--查询商务响应表（技术+商务）近三年业绩文件信息-->
	<select id="selectThreeYearsSale">
		select ONE_YA_SALE,TWO_YA_SALE,THREE_YA_SALE from OS_ZB_SUPPLIER_RESPONSE_INFO  where MARK_NO = '{markNo}' and PACK_NAME ='{packName}'
		union
		SELECT (
		SELECT SUM(OS_ZB_SUPPLIER_BIAO_SALES_INFO.SALE_MONEY)
		FROM OS_ZB_SUPPLIER_BIAO_SALES_INFO
		WHERE OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN LIKE '2022%' AND
		('{packName}'  like '%'||OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME||',%' or  '{packName}' =OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME or length(OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME)&lt;=0)
		group by OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN
		)
		as ONE_YA_SALE,
		(
		SELECT SUM(OS_ZB_SUPPLIER_BIAO_SALES_INFO.SALE_MONEY)
		FROM OS_ZB_SUPPLIER_BIAO_SALES_INFO
		WHERE OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN LIKE '2021%' AND
		('{packName}'  like '%'||OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME||',%' or  '{packName}' =OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME or length(OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME)&lt;=0)
		group by OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN
		)
		as TWO_YA_SALE,
		(
		SELECT SUM(OS_ZB_SUPPLIER_BIAO_SALES_INFO.SALE_MONEY)
		FROM OS_ZB_SUPPLIER_BIAO_SALES_INFO
		WHERE OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN LIKE '2020%' AND
		('{packName}'  like '%'||OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME||',%' or  '{packName}' =OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME or length(OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME)&lt;=0)
		group by OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN
		)
		as THREE_YA_SALE

	</select>

	<!--查询类似项目业绩和实施经验-->
	<select id="selectRecordAndImpl">
		select  *  from
		(
		SELECT (CASE WHEN MODEL_FLAG = 'designing_list' THEN TASK_TIME ELSE CONTRACT_SIGN_TIME END) CONTRACT_SIGN_TIME,
		(CASE WHEN MODEL_FLAG = 'finish_list' THEN TASK_TIME ELSE CONTRACT_SIGN_TIME END) TASK_TIME,
		PROJECT_TITLE
		FROM OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD
		where MARK_NO ='{markNo}' <if test="packName!=null" value="and PACK_NAME='{packName}'"></if> 
			union
			SELECT CONTRACT_SIGN_TIME,PRODUCTION_DATE as TASK_TIME,PROJECT_TITLE
			FROM OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE
			where MARK_NO ='{markNo}' <if test="packName!=null" value="and PACK_NAME='{packName}'"></if>
	) as mainTb
	</select>
	
	
</mapper>