using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using Microsoft.Win32;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("CheckWindowsEnv")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("CheckWindowsEnv")]
[assembly: AssemblyCopyright("Copyright ©  2022")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("e049999d-600a-4c46-beb0-d6a1b592f423")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
namespace CheckWindowsEnv.Properties
{
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "*******")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		private static ResourceManager resourceMan;

		private static CultureInfo resourceCulture;

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (resourceMan == null)
				{
					ResourceManager resourceManager = new ResourceManager("CheckWindowsEnv.Properties.Resources", typeof(Resources).Assembly);
					resourceMan = resourceManager;
				}
				return resourceMan;
			}
		}

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return resourceCulture;
			}
			set
			{
				resourceCulture = value;
			}
		}

		internal Resources()
		{
		}
	}
	[CompilerGenerated]
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "1*******")]
	internal sealed class Settings : ApplicationSettingsBase
	{
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());

		public static Settings Default => defaultInstance;
	}
}
namespace checkEnv
{
	internal static class Program
	{
		[STAThread]
		private static void Main()
		{
			Application.EnableVisualStyles();
			Application.SetCompatibleTextRenderingDefault(defaultValue: false);
			CheckEnv();
		}

		private static void CheckEnv()
		{
			try
			{
				if (!StandardEnv())
				{
					string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "NDP462-KB3151800-x86-x64-AllOS-ENU.exe");
					if (!File.Exists(path))
					{
						throw new Exception("缺少NDP462-KB3151800-x86-x64-AllOS-ENU.exe，请联系管理员!");
					}
					if (MessageBox.Show("当前环境的.Net FrameWork版本过低，点击是进行版本升级，\r\n\r\n安装完成后将自动启动软件", "提示", MessageBoxButtons.YesNo) != DialogResult.No)
					{
						Process.Start("NDP462-KB3151800-x86-x64-AllOS-ENU.exe").WaitForExit();
						Process.Start("bid.exe");
					}
				}
				else
				{
					Process.Start("bid-anhui.exe");
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		public static bool StandardEnv()
		{
			bool result = false;
			using (RegistryKey registryKey = RegistryKey.OpenRemoteBaseKey(RegistryHive.LocalMachine, "").OpenSubKey("SOFTWARE\\Microsoft\\NET Framework Setup\\NDP\\"))
			{
				string[] subKeyNames = registryKey.GetSubKeyNames();
				foreach (string text in subKeyNames)
				{
					if (text.StartsWith("v4"))
					{
						return true;
					}
				}
			}
			return result;
		}

		private static bool GetDotNetVersion(string version)
		{
			string text = "0";
			using (RegistryKey registryKey = RegistryKey.OpenRemoteBaseKey(RegistryHive.LocalMachine, "").OpenSubKey("SOFTWARE\\Microsoft\\NET Framework Setup\\NDP\\"))
			{
				string[] subKeyNames = registryKey.GetSubKeyNames();
				foreach (string text2 in subKeyNames)
				{
					if (!text2.StartsWith("v"))
					{
						continue;
					}
					RegistryKey registryKey2 = registryKey.OpenSubKey(text2);
					string text3 = (string)registryKey2.GetValue("Version", "");
					if (string.Compare(text3, text) > 0)
					{
						text = text3;
					}
					if (text3 != "")
					{
						continue;
					}
					string[] subKeyNames2 = registryKey2.GetSubKeyNames();
					foreach (string name in subKeyNames2)
					{
						RegistryKey registryKey3 = registryKey2.OpenSubKey(name);
						text3 = (string)registryKey3.GetValue("Version", "");
						if (string.Compare(text3, text) > 0)
						{
							text = text3;
						}
					}
				}
			}
			return string.Compare(text, version) > 0;
		}
	}
}
