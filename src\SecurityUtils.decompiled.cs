using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using IKVM.Attributes;
using java.lang;
using java.security;
using java.util;
using javax.crypto;
using javax.crypto.spec;
using org.bouncycastle.jce.provider;
using org.bouncycastle.pqc.math.linearalgebra;

[assembly: Debuggable(true, false)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: InternalsVisibleTo("SecurityUtils-ikvm-runtime-injected, PublicKey=00240000048000009400000006020000002400005253413100040000010001009D674F3D63B8D7A4C428BD7388341B025C71AA61C6224CD53A12C21330A3159D300051FE2EED154FE30D70673A079E4529D0FD78113DCA771DA8B0C1EF2F77B73651D55645B0A4294F0AF9BF7078432E13D0F46F951D712C2FCF02EB15552C0FE7817FC0AED58E0984F86661BF64D882F29B619899DD264041E7D4992548EB9E")]
[assembly: AssemblyVersion("0.0.0.0")]
[module: SourceFile(null)]
[module: JavaModule(Jars = new string[] { "SecurityUtils.jar" })]
[module: PackageList("SecurityUtils.jar", new string[] { "com.tlzh.utils" })]
namespace com.tlzh.utils;

public class SM4Util : Object
{
	private const string ENCODING = "UTF-8";

	public const string ALGORITHM_NAME = "SM4";

	public const string ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";

	public const int DEFAULT_KEY_SIZE = 128;

	public const string SM4_KEY = "86C63180C2806ED1F47B859DE501215B";

	[MethodImpl(MethodImplOptions.NoInlining)]
	[SpecialName]
	public static void __<clinit>()
	{
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 13, 112, 108 })]
	public static byte[] generateKey(int keySize)
	{
		KeyGenerator instance = KeyGenerator.getInstance("SM4", "BC");
		instance.init(keySize, new SecureRandom());
		byte[] encoded = instance.generateKey().getEncoded();
		_ = null;
		return encoded;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 52, 109 })]
	public static byte[] encrypt_Ecb_Padding(byte[] key, byte[] data)
	{
		Cipher cipher = generateEcbCipher("SM4/ECB/PKCS5Padding", 1, key);
		byte[] result = cipher.doFinal(data);
		_ = null;
		return result;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 159, 180, 108, 108, 104 })]
	private static Cipher generateEcbCipher(string P_0, int P_1, byte[] P_2)
	{
		Cipher instance = Cipher.getInstance(P_0, "BC");
		SecretKeySpec key = new SecretKeySpec(P_2, "SM4");
		instance.init(P_1, key);
		return instance;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 90, 109 })]
	public static byte[] decrypt_Ecb_Padding(byte[] key, byte[] cipherText)
	{
		Cipher cipher = generateEcbCipher("SM4/ECB/PKCS5Padding", 2, key);
		byte[] result = cipher.doFinal(cipherText);
		_ = null;
		return result;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[LineNumberTable(9)]
	public SM4Util()
	{
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(53)]
	public static byte[] generateKey()
	{
		byte[] result = generateKey(128);
		_ = null;
		return result;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 31, 134, 135, 140, 136, 103 })]
	public static string encryptEcb(string hexKey, string paramStr)
	{
		byte[] key = ByteUtils.fromHexString(hexKey);
		byte[] data = String.instancehelper_getBytes(paramStr, "UTF-8");
		byte[] barr = encrypt_Ecb_Padding(key, data);
		return ByteUtils.toHexString(barr);
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 69, 134, 135, 135, 136, 108 })]
	public static string decryptEcb(string hexKey, string cipherText)
	{
		byte[] key = ByteUtils.fromHexString(hexKey);
		byte[] cipherText2 = ByteUtils.fromHexString(cipherText);
		byte[] bytes = decrypt_Ecb_Padding(key, cipherText2);
		return String.newhelper(bytes, "UTF-8");
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[Throws(new string[] { "java.lang.Exception" })]
	[LineNumberTable(new byte[] { 108, 130, 135, 135, 136, 140, 105 })]
	public static bool verifyEcb(string hexKey, string cipherText, string paramStr)
	{
		_ = 0;
		byte[] key = ByteUtils.fromHexString(hexKey);
		byte[] cipherText2 = ByteUtils.fromHexString(cipherText);
		byte[] a = decrypt_Ecb_Padding(key, cipherText2);
		byte[] a2 = String.instancehelper_getBytes(paramStr, "UTF-8");
		return Arrays.equals(a, a2);
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[LineNumberTable(new byte[] { 160, 67, 102, 107, 104, 103, 247, 61, 230, 69 })]
	public static string strTo16(string s)
	{
		string text = "";
		for (int i = 0; i < String.instancehelper_length(s); i++)
		{
			int i2 = String.instancehelper_charAt(s, i);
			string str = Integer.toHexString(i2);
			text = new StringBuilder().append(text).append(str).toString();
		}
		return text;
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	[LineNumberTable(new byte[]
	{
		160, 79, 107, 104, 130, 103, 100, 103, 105, 136,
		232, 58, 230, 72
	})]
	public static string byteArrayToHexStr(byte[] byteArray)
	{
		StringBuilder stringBuilder = new StringBuilder("");
		if (byteArray == null || (nint)byteArray.LongLength <= 0)
		{
			return null;
		}
		for (int i = 0; i < (nint)byteArray.LongLength; i++)
		{
			int i2 = byteArray[i];
			string text = Integer.toHexString(i2);
			if (String.instancehelper_length(text) < 2)
			{
				stringBuilder.append(0);
			}
			stringBuilder.append(text);
		}
		string result = stringBuilder.toString();
		_ = null;
		return result;
	}

	[LineNumberTable(new byte[] { 159, 154, 107 })]
	static SM4Util()
	{
		Security.addProvider(new BouncyCastleProvider());
	}
}
