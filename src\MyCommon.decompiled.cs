using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SQLite;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Management;
using System.Net;
using System.Net.NetworkInformation;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;
using System.Xml.Serialization;
using System.util.collections;
using Aspose.Cells;
using CommonApi.Util;
using Microsoft.CodeAnalysis;
using Microsoft.Win32;
using MyCommon.Model.Cache;
using MyCommon.Model.attr;
using MyCommon.Model.attr.vo;
using MyCommon.Model.convert;
using MyCommon.Model.dto;
using MyCommon.Model.entity;
using MyCommon.XmlDto;
using MyCommon.api;
using MyCommon.attr;
using MyCommon.condition;
using MyCommon.condition.factory;
using MyCommon.dto;
using MyCommon.entity;
using MyCommon.enums;
using MyCommon.extendmethods;
using MyCommon.message;
using MyCommon.serivce;
using MyCommon.serivce.impl;
using MyCommon.util;
using MyCommon.vo;
using NLog;
using NLog.Config;
using NLog.Targets;
using NLog.Time;
using NetCore_Demo;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp.Extensions;
using UTCAdapt.Driver.UKEY;
using UTCCrypt;
using UTCSeal;
using WinformsUserControl.UserControls;
using com.tlzh.utils;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("MyCommon")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("MyCommon")]
[assembly: AssemblyCopyright("Copyright ©  2021")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("a2e0be6a-72a4-4a6b-b085-b5a3bebc77eb")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace Microsoft.CodeAnalysis
{
	[CompilerGenerated]
	[Microsoft.CodeAnalysis.Embedded]
	internal sealed class EmbeddedAttribute : Attribute
	{
	}
}
namespace System.Runtime.CompilerServices
{
	[CompilerGenerated]
	[Microsoft.CodeAnalysis.Embedded]
	internal sealed class IsReadOnlyAttribute : Attribute
	{
	}
}
namespace WinFormsDateGridViewDemo
{
	public class ImageZipHelper
	{
		public static void CompressImage(Image sourceImage, string savePath, long quality, string mimeType = null)
		{
			mimeType = mimeType ?? "image/jpeg";
			ImageCodecInfo encoderInfo = GetEncoderInfo(mimeType);
			System.Drawing.Imaging.Encoder quality2 = System.Drawing.Imaging.Encoder.Quality;
			EncoderParameter encoderParameter = new EncoderParameter(quality2, quality);
			EncoderParameters encoderParameters = new EncoderParameters(1);
			encoderParameters.Param[0] = encoderParameter;
			sourceImage.Save(savePath, encoderInfo, encoderParameters);
		}

		public static void CompressImage(string sourceImage, string savePath, long quality, string mimeType = null, Action<Image> callAction = null)
		{
			mimeType = mimeType ?? "image/jpeg";
			using Image image = Image.FromFile(sourceImage);
			ImageCodecInfo encoderInfo = GetEncoderInfo(mimeType);
			System.Drawing.Imaging.Encoder quality2 = System.Drawing.Imaging.Encoder.Quality;
			EncoderParameter encoderParameter = new EncoderParameter(quality2, quality);
			EncoderParameters encoderParameters = new EncoderParameters(1);
			encoderParameters.Param[0] = encoderParameter;
			image.Save(savePath, encoderInfo, encoderParameters);
			callAction?.Invoke(image);
		}

		private static ImageCodecInfo GetEncoderInfo(string mimeType)
		{
			ImageCodecInfo[] imageEncoders = ImageCodecInfo.GetImageEncoders();
			for (int i = 0; i < imageEncoders.Length; i++)
			{
				if (imageEncoders[i].MimeType == mimeType)
				{
					return imageEncoders[i];
				}
			}
			return null;
		}
	}
}
namespace NetCore_Demo
{
	public class SqlExpressionVisitor<TEntity> : ExpressionVisitor where TEntity : CommonEntity
	{
		private StringBuilder _sqlBuilder = new StringBuilder();

		private Stack<string> symStack = new Stack<string>();

		private static Dictionary<ExpressionType, string> expressionTypeDict = new Dictionary<ExpressionType, string>
		{
			{
				ExpressionType.And,
				" AND "
			},
			{
				ExpressionType.AndAlso,
				" AND "
			},
			{
				ExpressionType.OrElse,
				" OR "
			},
			{
				ExpressionType.Equal,
				" = "
			},
			{
				ExpressionType.NotEqual,
				" != "
			},
			{
				ExpressionType.GreaterThan,
				" >"
			},
			{
				ExpressionType.GreaterThanOrEqual,
				" >= "
			},
			{
				ExpressionType.LessThan,
				" < "
			},
			{
				ExpressionType.LessThanOrEqual,
				" <= "
			}
		};

		private static Dictionary<Type, string> commonTypeDict = new Dictionary<Type, string>
		{
			{
				typeof(short),
				null
			},
			{
				typeof(int),
				null
			},
			{
				typeof(long),
				null
			},
			{
				typeof(float),
				null
			},
			{
				typeof(double),
				null
			},
			{
				typeof(decimal),
				null
			},
			{
				typeof(string),
				null
			},
			{
				typeof(DateTime),
				null
			},
			{
				typeof(DateTimeOffset),
				null
			}
		};

		private static Dictionary<Type, string> numberTypeDict = new Dictionary<Type, string>
		{
			{
				typeof(short),
				null
			},
			{
				typeof(int),
				null
			},
			{
				typeof(long),
				null
			},
			{
				typeof(float),
				null
			},
			{
				typeof(double),
				null
			},
			{
				typeof(decimal),
				null
			}
		};

		private SqlWrapper<TEntity> shareWrapper = null;

		public string GetSeqmentSql()
		{
			string result = _sqlBuilder.ToString();
			_sqlBuilder.Clear();
			symStack.Clear();
			return result;
		}

		protected override Expression VisitBinary(BinaryExpression node)
		{
			if (expressionTypeDict.ContainsKey(node.NodeType))
			{
				symStack.Push(expressionTypeDict[node.NodeType]);
			}
			return base.VisitBinary(node);
		}

		protected override Expression VisitMethodCall(MethodCallExpression node)
		{
			if (node.Method.DeclaringType == typeof(string) && node.Method.Name == "Contains")
			{
				symStack.Push(" LIKE ");
			}
			else if (node.Method.Name == "Equals")
			{
				symStack.Push(expressionTypeDict[ExpressionType.Equal]);
			}
			return base.VisitMethodCall(node);
		}

		protected override Expression VisitConstant(ConstantExpression node)
		{
			string text = null;
			if (node.Value == null)
			{
				_sqlBuilder.Append("NULL");
			}
			else if (numberTypeDict.ContainsKey(node.Value.GetType()))
			{
				_sqlBuilder.Append(node.Value);
			}
			else if (_sqlBuilder.ToString().EndsWith("LIKE "))
			{
				_sqlBuilder.Append("'%").Append(node.Value).Append("%'");
			}
			else
			{
				_sqlBuilder.Append("'").Append(node.Value).Append("'");
			}
			if (symStack.Any())
			{
				text = symStack.Pop();
				_sqlBuilder.Append(text);
			}
			return base.VisitConstant(node);
		}

		protected override Expression VisitMember(MemberExpression node)
		{
			Type declaringType = node.Member.DeclaringType;
			shareWrapper.TableDict.TryGetValue(declaringType, out var value);
			string key = node.Member.Name?.ToUpper() ?? "";
			string value2 = null;
			value?.FieldColumnDict?.TryGetValue(key, out value2);
			string value3 = value?.Alias + "." + value2;
			if (_sqlBuilder.ToString().EndsWith("LIKE "))
			{
				_sqlBuilder.Append("%").Append(value3).Append("%");
			}
			else
			{
				_sqlBuilder.Append(value3);
			}
			if (symStack.Any())
			{
				string value4 = symStack.Pop();
				_sqlBuilder.Append(value4);
			}
			return node;
		}

		public void SetShareWrapper(SqlWrapper<TEntity> shareWrapper)
		{
			this.shareWrapper = shareWrapper;
		}
	}
}
namespace MyCommon
{
	public enum MsgToken
	{
		All = 0,
		UiUpate = 2,
		DataUpate = 8
	}
	public interface IMessage
	{
		void Register<TValue>(IRecipient<TValue> iRecipient, string token = null) where TValue : SendMsg;

		void UnRegister<TValue>(IRecipient<TValue> iRecipient, string token = null) where TValue : SendMsg;

		void UnRegisterAll<Recipient>(Recipient iRecipient) where Recipient : IBaseRecipient;

		void Send<TValue>(TValue tvalue, string token = null, string flag = null) where TValue : SendMsg;
	}
	public class WeakReferenceMessager : IMessage
	{
		private Dictionary<string, Dictionary<Type, object>> registedDict = new Dictionary<string, Dictionary<Type, object>>();

		private const int MaxMessageCount = 600;

		private const int MaxRegistMessageCount = 1000;

		private static WeakReferenceMessager _default;

		public static WeakReferenceMessager Default
		{
			get
			{
				if (_default == null)
				{
					_default = new WeakReferenceMessager();
				}
				return _default;
			}
		}

		private WeakReferenceMessager()
		{
		}

		public void Register<TValue>(IRecipient<TValue> iRecipient, string token = null) where TValue : SendMsg
		{
			lock (registedDict)
			{
				if (registedDict.Count + 1 > 600)
				{
					throw new ArgumentOutOfRangeException($"超出了最大的消息注册数量限制，最大为{600}条");
				}
				string key = GenericKey(typeof(TValue), token);
				if (!registedDict.ContainsKey(key))
				{
					registedDict.Add(key, new Dictionary<Type, object> { 
					{
						iRecipient.GetType(),
						iRecipient
					} });
				}
				else
				{
					if (registedDict[key].Count + 1 > 1000)
					{
						throw new ArgumentOutOfRangeException($"超出了最大的此类消息订阅数量限制，最大为{1000}条");
					}
					registedDict[key].AddIfNotAbsent(iRecipient.GetType(), iRecipient);
				}
				if (iRecipient is Form form)
				{
					form.FormClosing -= Frm_FormClosing;
					form.FormClosing += Frm_FormClosing;
				}
			}
		}

		public void Send<TValue>(TValue message, string token = null, string flag = null) where TValue : SendMsg
		{
			Type typeFromHandle = typeof(TValue);
			string key = GenericKey(typeFromHandle, token);
			if (!registedDict.ContainsKey(key))
			{
				return;
			}
			Dictionary<Type, object> dictionary = registedDict[key];
			foreach (KeyValuePair<Type, object> item in dictionary)
			{
				if (item.Value is IRecipient<TValue> recipient)
				{
					recipient.Receive(message, flag);
				}
			}
		}

		public void UnRegister<TValue>(IRecipient<TValue> iRecipient, string token = null) where TValue : SendMsg
		{
			lock (registedDict)
			{
				string key = GenericKey(typeof(TValue), token);
				if (registedDict.TryGetValue(key, out var value))
				{
					return;
				}
				foreach (KeyValuePair<Type, object> item in value)
				{
					if (item.Key != iRecipient.GetType())
					{
						value.Remove(item.Key);
					}
				}
			}
		}

		public void RegisterAll<Recipient>(Recipient iRecipient) where Recipient : IBaseRecipient
		{
			Type type = iRecipient.GetType();
			List<Type> list = ((IEnumerable<Type>)type.GetInterfaces()).Where((Func<Type, bool>)((Type p) => p.FullName.Contains("MyCommon.message.IRecipient"))).ToList();
			if (registedDict.Count + 1 > 600)
			{
				throw new ArgumentOutOfRangeException($"超出了最大的消息注册数量限制，最大为{600}条");
			}
			for (int i = 0; i < list.Count; i++)
			{
				lock (registedDict)
				{
					Type stype = list[i].GetGenericArguments()[0];
					string[] array = new string[3]
					{
						GenericKey(stype),
						GenericKey(stype, "UpdateList"),
						GenericKey(stype, "RefData")
					};
					string[] array2 = array;
					foreach (string key in array2)
					{
						if (!registedDict.ContainsKey(key))
						{
							registedDict.Add(key, new Dictionary<Type, object> { 
							{
								iRecipient.GetType(),
								iRecipient
							} });
							continue;
						}
						if (registedDict[key].Count + 1 > 1000)
						{
							throw new ArgumentOutOfRangeException($"超出了最大的此类消息订阅数量限制，最大为{1000}条");
						}
						registedDict[key].AddIfNotAbsent(iRecipient.GetType(), iRecipient);
					}
				}
			}
			lock (registedDict)
			{
				if (iRecipient is Form form)
				{
					form.FormClosing -= Frm_FormClosing;
					form.FormClosing += Frm_FormClosing;
				}
			}
		}

		public void UnRegisterAll<Recipient>(Recipient iRecipient) where Recipient : IBaseRecipient
		{
			Type type = iRecipient.GetType();
			List<Type> list = ((IEnumerable<Type>)type.GetInterfaces()).Where((Func<Type, bool>)((Type p) => p.FullName.Contains("MyCommon.message.IRecipient"))).ToList();
			for (int i = 0; i < list.Count; i++)
			{
				lock (registedDict)
				{
					Type stype = list[i].GetGenericArguments()[0];
					string[] array = new string[3]
					{
						GenericKey(stype),
						GenericKey(stype, "UpdateList"),
						GenericKey(stype, "RefData")
					};
					string[] array2 = array;
					foreach (string key in array2)
					{
						if (registedDict.ContainsKey(key))
						{
							bool flag = registedDict[key].Remove(type);
						}
					}
				}
			}
		}

		private void Frm_VisibleChanged(object sender, EventArgs e)
		{
			if (sender is IBaseRecipient iRecipient)
			{
				UnRegisterAll(iRecipient);
			}
		}

		private void Frm_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (sender is IBaseRecipient iRecipient)
			{
				UnRegisterAll(iRecipient);
			}
		}

		private string GenericKey(Type stype, string token = null)
		{
			return stype.FullName + "&&" + token;
		}
	}
}
namespace MyCommon.XmlDto
{
	[XmlRoot("CloudUpload", Namespace = null, IsNullable = true)]
	public class CloudUpload
	{
		private string xml_tables;

		[XmlAttribute("type")]
		public string Type { get; set; }

		[XmlAttribute("diff")]
		public string Diff { get; set; }

		[XmlAttribute("condition")]
		public string Condition { get; set; }

		[XmlAttribute("tables")]
		public string Xml_Tables
		{
			get
			{
				return xml_tables;
			}
			set
			{
				xml_tables = value;
				if (!string.IsNullOrEmpty(value))
				{
					string[] array = value.Split(',', '，');
					for (int i = 0; i < array.Length; i++)
					{
						array[i] = array[i]?.Trim();
					}
					Tables = array.ToList();
				}
			}
		}

		[XmlIgnore]
		public List<string> Tables { get; private set; }

		[XmlAttribute("useSql")]
		public bool UseSql { get; set; }

		[XmlAttribute("ingoreTables")]
		public string IngoreTables { get; set; }

		[XmlText]
		[XmlElement("Sql")]
		public string Sql { get; set; }
	}
	[XmlRoot("FormType", Namespace = null, IsNullable = false)]
	public class FormType
	{
		[XmlAttribute("type")]
		public string Type { get; set; }

		[XmlAttribute("form")]
		public string Form { get; set; }

		[XmlAttribute("hideTables")]
		public string HideTables { get; set; }

		[XmlAttribute("tableTitles")]
		public string TableTitles { get; set; }

		[XmlArray("MarkTypes")]
		public List<MarkType> MarkTypes { get; set; }

		[XmlAttribute("personnelResume")]
		[DefaultValue("tabCoreResumeFile")]
		public string PersonnelResume { get; set; }
	}
	[XmlRoot("ModelConfig", Namespace = null, IsNullable = false)]
	public class ModelConfig
	{
		[XmlAttribute("type")]
		public string Type { get; set; }

		[XmlAttribute("flag")]
		public string Flag { get; set; }

		[XmlAttribute("name")]
		public string Name { get; set; }

		[XmlAttribute("form")]
		public string Form { get; set; }

		[XmlAttribute("modelTitles")]
		public string ModelTitles { get; set; }

		[XmlAttribute("modelTypes")]
		public string MODEL_TYPES { get; set; }
	}
	[XmlRoot("ResponseTable", Namespace = null, IsNullable = true)]
	public class ResponseTable
	{
		private string form;

		private Dictionary<string, Expression<Func<OsZbPurchaseProjectInfo, bool>>> _ExpressionDict = null;

		[XmlArray("ModelConfigs")]
		public List<ModelConfig> ModelConfigs { get; set; }

		[XmlAttribute("type")]
		public string Type { get; set; }

		[XmlAttribute("info")]
		public string Info { get; set; }

		[XmlAttribute("condition")]
		public string Condition { get; set; }

		[XmlAttribute("form")]
		public string Form
		{
			get
			{
				return form;
			}
			set
			{
				form = value;
			}
		}

		private Dictionary<string, Expression<Func<OsZbPurchaseProjectInfo, bool>>> GetExpressionDict()
		{
			if (_ExpressionDict == null)
			{
				_ExpressionDict = new Dictionary<string, Expression<Func<OsZbPurchaseProjectInfo, bool>>>();
				_ExpressionDict.Add("IsServiceFrame==\"是\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => "是".Equals(p.IsServiceFrame)));
				_ExpressionDict.Add("IsServiceFrame!=\"是\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => !"是".Equals(p.IsServiceFrame)));
				_ExpressionDict.Add("PurchaseType==\"物资招标\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => "物资招标".Equals(p.PurchaseType)));
				_ExpressionDict.Add("PurchaseType!=\"物资招标\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => !"物资招标".Equals(p.PurchaseType)));
				_ExpressionDict.Add("PurchaseType==\"服务招标\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => "服务招标".Equals(p.PurchaseType)));
				_ExpressionDict.Add("PurchaseType!=\"服务招标\"", (Expression<Func<OsZbPurchaseProjectInfo, bool>>)((OsZbPurchaseProjectInfo p) => !"服务招标".Equals(p.PurchaseType)));
			}
			return _ExpressionDict;
		}

		public bool IsMatch(OsZbPurchaseProjectInfo info)
		{
			if (string.IsNullOrWhiteSpace(Condition))
			{
				return true;
			}
			_ExpressionDict = GetExpressionDict();
			string[] array = Regex.Split(Condition, "and");
			string[] array2 = Regex.Split(Condition, "or");
			int num = 0;
			string[] array3 = array;
			for (int i = 0; i < array3.Length; i++)
			{
				string key = array3[i]?.Trim();
				if (_ExpressionDict.ContainsKey(key) && _ExpressionDict[key].Compile().Invoke(info))
				{
					num++;
				}
			}
			int? num2 = array2?.Length;
			string[] array4 = array2;
			for (int j = 0; j < array4.Length; j++)
			{
				string key = array4[j]?.Trim();
				if (_ExpressionDict.ContainsKey(key) && _ExpressionDict[key].Compile().Invoke(info))
				{
					num2--;
				}
			}
			if (array2 != null && array2.Length > 1)
			{
				return (array2 != null) ? (array2.Length != num2) : num2.HasValue;
			}
			return num >= array.Length;
		}
	}
	[XmlRoot("configuration", Namespace = null, IsNullable = false)]
	public class XmlConfiguration
	{
		[XmlArray("ResponseTables")]
		public List<ResponseTable> ResponseTables { get; set; }
	}
}
namespace MyCommon.util
{
	public class AsposeExcelOption<TEntity> where TEntity : CommonEntity
	{
		internal Dictionary<string, Func<TEntity, object>> HeadColumnMapping = new Dictionary<string, Func<TEntity, object>>();

		private Dictionary<string, ExportColumnInfo<TEntity>> _exportFuncDict = null;

		public IList<TEntity> DataSources { get; set; }

		public string TitleName { get; set; } = "标题栏";


		public string SavePath { get; set; }

		public string Template { get; set; }

		public int SheetIndex { get; set; } = 0;


		public int FirstWriteRow { get; set; } = 0;


		public Dictionary<string, ExportColumnInfo<TEntity>> ExportFuncDict
		{
			get
			{
				return _exportFuncDict;
			}
			set
			{
				_exportFuncDict = value;
				if (value != null && value.Any())
				{
					HeadColumnMapping = value.ToDictionary<KeyValuePair<string, ExportColumnInfo<TEntity>>, string, Func<TEntity, object>>((Func<KeyValuePair<string, ExportColumnInfo<TEntity>>, string>)(object)(Func<KeyValuePair<string, ExportColumnInfo<KeyValuePair<string, ExportColumnInfo<TEntity>>>>, string>)((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Key), (Func<KeyValuePair<string, ExportColumnInfo<TEntity>>, Func<TEntity, object>>)(object)(Func<KeyValuePair<string, ExportColumnInfo<KeyValuePair<string, ExportColumnInfo<TEntity>>>>, Func<KeyValuePair<string, ExportColumnInfo<TEntity>>, object>>)((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value.FuncObj));
				}
			}
		}

		public AsposeExcelOption(IList<TEntity> dataSources, string titleName, Dictionary<string, Func<TEntity, object>> headColumnMapping, string savePath)
		{
			DataSources = dataSources;
			TitleName = titleName;
			HeadColumnMapping = headColumnMapping;
			SavePath = savePath;
		}

		public AsposeExcelOption(IList<TEntity> dataSources, string titleName, Dictionary<string, ExportColumnInfo<TEntity>> exportFuncDict, string savePath)
		{
			DataSources = dataSources;
			TitleName = titleName;
			ExportFuncDict = exportFuncDict;
			SavePath = savePath;
		}
	}
	public class AsposeExcel
	{
		public static Workbook CreateMyWorkBook(string fileName = null)
		{
			Workbook workbook = ((fileName == null) ? new Workbook() : new Workbook(fileName));
			Worksheet worksheet = workbook.Worksheets[0];
			Cells cells = worksheet.Cells;
			worksheet.Protection.AllowSelectingLockedCell = false;
			worksheet.Protection.AllowFormattingColumn = true;
			worksheet.Protection.AllowFormattingRow = true;
			return workbook;
		}

		public static int WorkbookCreateExcel<T>(Workbook workbook, int sheetIndex, List<T> list, string titleName, string[] exportHeads, string[] exportFields, int firstRow = 0) where T : CommonEntity
		{
			Cells cells = workbook.Worksheets[sheetIndex].Cells;
			if (exportHeads == null || exportFields == null)
			{
				throw new Exception("导出指定参数不能为空");
			}
			if (exportHeads.Length != exportFields.Length)
			{
				throw new Exception("exportHeads长度和exportFields长度不一致");
			}
			Type typeFromHandle = typeof(T);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			for (int i = 0; i < properties.Length; i++)
			{
				dictionary.Add(properties[i].Name, properties[i]);
			}
			Style style = workbook.CreateStyle();
			style.HorizontalAlignment = TextAlignmentType.Center;
			style.VerticalAlignment = TextAlignmentType.Center;
			style.Font.Name = "宋体";
			style.Font.Size = 14;
			style.Font.IsBold = true;
			Style style2 = workbook.CreateStyle();
			style2.HorizontalAlignment = TextAlignmentType.Center;
			style2.VerticalAlignment = TextAlignmentType.Center;
			style2.ForegroundColor = Color.SkyBlue;
			style2.Pattern = BackgroundType.Solid;
			style2.Font.IsBold = true;
			style2.Font.Name = "宋体";
			style2.Font.Size = 13;
			style2.Font.IsBold = true;
			style2.IsTextWrapped = true;
			style2.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			Style style3 = workbook.CreateStyle();
			style3.HorizontalAlignment = TextAlignmentType.Center;
			style3.VerticalAlignment = TextAlignmentType.Center;
			style3.Font.Name = "宋体";
			style3.Font.Size = 12;
			style3.IsTextWrapped = true;
			style3.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			int num = exportHeads.Length;
			int count = list.Count;
			cells.Merge(firstRow, 0, 1, num);
			cells[firstRow, 0].PutValue(titleName);
			cells[firstRow, 0].SetStyle(style);
			cells.SetRowHeight(firstRow, 38.0);
			for (int j = 0; j < num; j++)
			{
				cells[1 + firstRow, j].PutValue(exportHeads[j]);
				cells[1 + firstRow, j].SetStyle(style2);
				cells.SetColumnWidth(j, 10 + exportHeads[j].Length * 5);
			}
			cells.SetRowHeight(1 + firstRow, 38.0);
			PropertyInfo propertyInfo = null;
			string text = null;
			for (int k = 0; k < count; k++)
			{
				for (int l = 0; l < num; l++)
				{
					text = FirstUpCase(exportFields[l]);
					if (dictionary.ContainsKey(text))
					{
						propertyInfo = dictionary[text];
						cells[firstRow + 2 + k, l].PutValue(propertyInfo.GetValue(list[k]));
						cells[firstRow + 2 + k, l].SetStyle(style3);
					}
				}
			}
			return firstRow + count + 2;
		}

		public static void ExportTemplateToExcel<TEntity>(string fileName, string titleContent, IList<TEntity> sourceData, string businessId, Action<Worksheet> callBack = null, bool open = true) where TEntity : CommonEntity
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = fileName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName2 = saveFileDialog.FileName;
				Workbook workbook = CreateMyWorkBook();
				WriteToSheet(titleContent, sourceData, businessId, callBack, workbook);
				workbook.Save(fileName2);
				if (open)
				{
					Process.Start(fileName2);
				}
			}
		}

		public static void ExportTemplateToExcel<TEntity1, TEntity2>(string fileName, string titleContent, IList<TEntity1> sourceData1, string businessId1, IList<TEntity2> sourceData2, string businessId2, Action<Worksheet> callBack = null) where TEntity1 : CommonEntity where TEntity2 : CommonEntity
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = fileName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName2 = saveFileDialog.FileName;
				Workbook workbook = CreateMyWorkBook();
				WriteToSheet(titleContent, sourceData1, businessId1, callBack, workbook);
				WriteToSheet(titleContent, sourceData2, businessId2, callBack, workbook, 1);
				workbook.Save(fileName2);
				Process.Start(fileName2);
			}
		}

		public static void ExportTemplateToExcel<TEntity1, TEntity2>(string fileName, AsposeExcelOption<TEntity1> excelOption1, AsposeExcelOption<TEntity2> excelOption2, Action<Worksheet> callBack = null) where TEntity1 : CommonEntity where TEntity2 : CommonEntity
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = fileName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName2 = saveFileDialog.FileName;
				Workbook workbook = CreateMyWorkBook();
				WriteToSheet(excelOption1, callBack, workbook);
				if (excelOption2 != null)
				{
					WriteToSheet(excelOption2, callBack, workbook);
				}
				workbook.Save(fileName2);
				Process.Start(fileName2);
			}
		}

		public static void ExportTemplateToExcel<TEntity1, TEntity2, TEntity3>(string fileName, string titleContent, IList<TEntity1> sourceData1, string businessId1, IList<TEntity2> sourceData2, string businessId2, IList<TEntity3> sourceData3 = null, string businessId3 = null, Action<Worksheet> callBack = null) where TEntity1 : CommonEntity where TEntity2 : CommonEntity where TEntity3 : CommonEntity
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = fileName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName2 = saveFileDialog.FileName;
				Workbook workbook = CreateMyWorkBook();
				WriteToSheet(titleContent, sourceData1, businessId1, callBack, workbook);
				WriteToSheet(titleContent, sourceData2, businessId2, callBack, workbook, 1);
				WriteToSheet(titleContent, sourceData3, businessId3, callBack, workbook, 2);
				workbook.Save(fileName2);
				Process.Start(fileName2);
			}
		}

		private static void WriteToSheet<TEntity>(string titleContent, IList<TEntity> sourceData, string businessId, Action<Worksheet> callBack, Workbook workbook, int sheetIndex = 0) where TEntity : CommonEntity
		{
			Dictionary<string, ExportColumnInfo<TEntity>> exportFuncDict = ExportColumnHelper.GetExportPropColumns<TEntity>(businessId);
			Dictionary<string, PropertyInfo> propDict = ReflectUtil.GetPropDict(typeof(TEntity));
			Dictionary<string, Func<TEntity, object>> headColumnMapping = exportFuncDict.ToDictionary<KeyValuePair<string, ExportColumnInfo<TEntity>>, string, Func<TEntity, object>>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Key, delegate(KeyValuePair<string, ExportColumnInfo<TEntity>> p)
			{
				try
				{
					if (exportFuncDict.TryGetValue(p.Key, out var value))
					{
						if (value.ActualPropName != null && propDict.TryGetValue(value.ActualPropName, out var curProp))
						{
							return (TEntity o) => curProp.GetValue(o, null) ?? ((p.Value.FuncObj.Invoke(o) != null) ? p.Value.ComboBoxItemDict[p.Value.FuncObj.Invoke(o).ToString()] : null);
						}
						Dictionary<string, string> comboBoxItemDict = value.ComboBoxItemDict;
						if (comboBoxItemDict != null && comboBoxItemDict.Count > 0)
						{
							return (TEntity o) => (p.Value.FuncObj.Invoke(o) != null) ? p.Value.ComboBoxItemDict[p.Value.FuncObj.Invoke(o).ToString()] : null;
						}
					}
				}
				catch (Exception ex)
				{
					LogsHelper.AddErrorLog(ex);
					throw;
				}
				return p.Value.FuncObj;
			});
			AsposeExcelOption<TEntity> asposeExcelOption = new AsposeExcelOption<TEntity>(sourceData, titleContent, headColumnMapping, null);
			asposeExcelOption.SheetIndex = sheetIndex;
			int num = (string.IsNullOrEmpty(titleContent) ? 1 : 2);
			if (workbook.Worksheets.Count - 1 < asposeExcelOption.SheetIndex)
			{
				workbook.Worksheets.Add();
			}
			WorkbookCreateExcel(workbook, asposeExcelOption);
			int num2 = 0;
			foreach (KeyValuePair<string, ExportColumnInfo<TEntity>> item in exportFuncDict)
			{
				if (item.Value.ComboBoxItemDict != null && item.Value.ComboBoxItemDict.Any())
				{
					Worksheet worksheet = workbook.Worksheets[0];
					IEnumerable<string> items = ((IEnumerable<KeyValuePair<string, string>>)item.Value.ComboBoxItemDict).Select<KeyValuePair<string, string>, string>((Func<KeyValuePair<string, string>, string>)((KeyValuePair<string, string> p) => p.Value));
					int curColumn = num2;
					int startRow = num;
					SetComboBox(worksheet, items, curColumn, 300, null, startRow);
				}
				num2++;
			}
			callBack?.Invoke(workbook.Worksheets[sheetIndex]);
		}

		private static void WriteToSheet<TEntity>(AsposeExcelOption<TEntity> option, Action<Worksheet> callBack, Workbook workbook) where TEntity : CommonEntity
		{
			if (workbook.Worksheets.Count - 1 < option.SheetIndex)
			{
				workbook.Worksheets.Add();
			}
			WorkbookCreateExcel(workbook, option);
			int num = 0;
			Dictionary<string, ExportColumnInfo<TEntity>> exportFuncDict = option.ExportFuncDict;
			foreach (KeyValuePair<string, ExportColumnInfo<TEntity>> item in exportFuncDict)
			{
				if (item.Value.ComboBoxItemDict != null && item.Value.ComboBoxItemDict.Any())
				{
					SetComboBox(workbook.Worksheets[option.SheetIndex], ((IEnumerable<KeyValuePair<string, string>>)item.Value.ComboBoxItemDict).Select<KeyValuePair<string, string>, string>((Func<KeyValuePair<string, string>, string>)((KeyValuePair<string, string> p) => p.Value)), num);
				}
				num++;
			}
			callBack?.Invoke(workbook.Worksheets[option.SheetIndex]);
		}

		public static void ExportToExcel<T>(List<T> list, string titleName, string path, string[] exportHeads, string[] exportFields) where T : CommonEntity
		{
			Workbook workbook = CreateMyWorkBook();
			WorkbookCreateExcel(workbook, 0, list, titleName, exportHeads, exportFields);
			workbook.Save(path);
		}

		public static void ExportToExcel<T>(string template, List<T> list, string titleName, string path, string[] exportHeads, string[] exportFields, Action<Worksheet> _callBack = null) where T : CommonEntity
		{
			template = template ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "templates\\common.xlsx");
			Workbook workbook = CreateMyWorkBook(template);
			WorkbookCreateExcel(workbook, 0, list, titleName, exportHeads, exportFields);
			_callBack?.Invoke(workbook.Worksheets[0]);
			workbook.Save(path);
		}

		public static void ExportToExcel<T>(AsposeExcelOption<T> option, Action<Worksheet> _callBack = null) where T : CommonEntity
		{
			Workbook workbook = CreateMyWorkBook(option?.Template);
			WorkbookCreateExcel(workbook, option);
			_callBack?.Invoke(workbook.Worksheets[0]);
			workbook.Save(option.SavePath);
		}

		private static string FirstUpCase(string value)
		{
			if (value == null || "".Equals(value))
			{
				return value;
			}
			char[] array = value.ToCharArray();
			return char.ToUpper(array[0]) + value.Substring(1);
		}

		public static void TemplateToExcel(string templatePath, DataSet dst, Dictionary<string, object> dataSourceKV, string savePath, Func<WorkbookDesigner, DataSet, Dictionary<string, object>, bool> funcSetStyle = null, Action<Workbook> lastFunc = null)
		{
			try
			{
				WorkbookDesigner workbookDesigner = new WorkbookDesigner();
				Workbook workbook = new Workbook(templatePath);
				lastFunc?.Invoke(workbook);
				workbookDesigner.Workbook = workbook;
				workbookDesigner.SetDataSource(dst);
				workbookDesigner.CalculateFormula = true;
				foreach (KeyValuePair<string, object> item in dataSourceKV)
				{
					workbookDesigner.SetDataSource(item.Key, item.Value);
				}
				workbookDesigner.Process(isPreserved: true);
				if (funcSetStyle != null)
				{
					bool flag = funcSetStyle.Invoke(workbookDesigner, dst, dataSourceKV);
				}
				if (File.Exists(savePath))
				{
					File.Delete(savePath);
				}
				workbookDesigner.Workbook.Save(savePath);
				workbookDesigner.ClearDataSource();
			}
			catch (Exception)
			{
				throw;
			}
		}

		public static void TemplateToExcel(string templatePath, DataTable dt, Dictionary<string, object> dataSourceKV, string savePath, Func<WorkbookDesigner, DataSet, Dictionary<string, object>, bool> funcSetStyle = null)
		{
			DataSet dataSet = new DataSet();
			dt?.DataSet?.Tables.Clear();
			dataSet.Tables.Add(dt);
			TemplateToExcel(templatePath, dataSet, dataSourceKV, savePath, funcSetStyle);
		}

		public static void WorkbookCreateExcel<T>(Workbook workbook, AsposeExcelOption<T> option) where T : CommonEntity
		{
			Worksheet worksheet = workbook.Worksheets[option.SheetIndex];
			Cells cells = worksheet.Cells;
			Dictionary<string, Func<T, object>> dictionary = option?.HeadColumnMapping;
			IList<T> list = option?.DataSources;
			if (dictionary == null)
			{
				throw new Exception("AsposeExcelOption中参数HeadColumnMapping不能为空");
			}
			if (list == null)
			{
				throw new Exception("AsposeExcelOption中参数dataSources不能为空");
			}
			Style style = workbook.CreateStyle();
			style.HorizontalAlignment = TextAlignmentType.Center;
			style.VerticalAlignment = TextAlignmentType.Center;
			style.Font.Name = "宋体";
			style.Font.Size = 14;
			style.Font.IsBold = true;
			Style style2 = workbook.CreateStyle();
			style2.HorizontalAlignment = TextAlignmentType.Center;
			style2.VerticalAlignment = TextAlignmentType.Center;
			style2.ForegroundColor = Color.SkyBlue;
			style2.Pattern = BackgroundType.Solid;
			style2.Font.IsBold = true;
			style2.Font.Name = "宋体";
			style2.Font.Size = 13;
			style2.Font.IsBold = true;
			style2.IsTextWrapped = true;
			style2.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			Style style3 = workbook.CreateStyle();
			style3.HorizontalAlignment = TextAlignmentType.Center;
			style3.VerticalAlignment = TextAlignmentType.Center;
			style3.Font.Name = "宋体";
			style3.Font.Size = 12;
			style3.IsTextWrapped = true;
			style3.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			int count = dictionary.Count;
			int count2 = list.Count;
			int num = 0;
			if (!string.IsNullOrEmpty(option.TitleName))
			{
				cells.Merge(option.FirstWriteRow, 0, 1, count);
				cells[option.FirstWriteRow, 0].PutValue(option.TitleName);
				cells[option.FirstWriteRow, 0].SetStyle(style);
				cells.SetRowHeight(option.FirstWriteRow, 38.0);
				num++;
			}
			int num2 = 0;
			foreach (KeyValuePair<string, Func<T, object>> item in dictionary)
			{
				cells[num + option.FirstWriteRow, num2].PutValue(item.Key);
				cells[num + option.FirstWriteRow, num2].SetStyle(style2);
				cells.SetColumnWidth(num2, 10 + item.Key.Length * 5);
				num2++;
			}
			cells.SetRowHeight(1 + option.FirstWriteRow, 38.0);
			num++;
			for (int i = 0; i < count2; i++)
			{
				num2 = 0;
				foreach (KeyValuePair<string, Func<T, object>> item2 in dictionary)
				{
					object objectValue = item2.Value?.Invoke(list[i]);
					cells[option.FirstWriteRow + num + i, num2].PutValue(objectValue);
					cells[option.FirstWriteRow + num + i, num2].SetStyle(style3);
					num2++;
				}
			}
			int firstWriteRow = option.FirstWriteRow + count2 + num;
			option.FirstWriteRow = firstWriteRow;
			option.DataSources?.Clear();
			workbook.Worksheets[option.SheetIndex].AutoFitColumns();
		}

		public static void ExportToExcel(string template, DataTable dataTable, string titleName, string path, string[] exportHeads, string[] exportFields, Action<Worksheet, string[]> _callBack = null)
		{
			template = template ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "templates\\common.xlsx");
			Workbook workbook = CreateMyWorkBook(template);
			WorkbookCreateExcel(workbook, 0, dataTable, titleName, exportHeads, exportFields);
			_callBack?.Invoke(workbook.Worksheets[0], exportFields);
			workbook.Save(path);
		}

		public static int WorkbookCreateExcel(Workbook workbook, int sheetIndex, DataTable dataTable, string titleName, string[] exportHeads, string[] exportFields, int firstRow = 0)
		{
			Cells cells = workbook.Worksheets[sheetIndex].Cells;
			if (exportHeads == null || exportFields == null)
			{
				throw new Exception("导出指定参数不能为空");
			}
			if (exportHeads.Length != exportFields.Length)
			{
				throw new Exception("exportHeads长度和exportFields长度不一致");
			}
			Style style = workbook.CreateStyle();
			style.HorizontalAlignment = TextAlignmentType.Center;
			style.VerticalAlignment = TextAlignmentType.Center;
			style.Font.Name = "宋体";
			style.Font.Size = 14;
			style.Font.IsBold = true;
			Style style2 = workbook.CreateStyle();
			style2.HorizontalAlignment = TextAlignmentType.Center;
			style2.VerticalAlignment = TextAlignmentType.Center;
			style2.ForegroundColor = Color.SkyBlue;
			style2.Pattern = BackgroundType.Solid;
			style2.Font.IsBold = true;
			style2.Font.Name = "宋体";
			style2.Font.Size = 13;
			style2.Font.IsBold = true;
			style2.IsTextWrapped = true;
			style2.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style2.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			Style style3 = workbook.CreateStyle();
			style3.HorizontalAlignment = TextAlignmentType.Center;
			style3.VerticalAlignment = TextAlignmentType.Center;
			style3.Font.Name = "宋体";
			style3.Font.Size = 12;
			style3.IsTextWrapped = true;
			style3.Borders[BorderType.LeftBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.RightBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.TopBorder].LineStyle = CellBorderType.Thin;
			style3.Borders[BorderType.BottomBorder].LineStyle = CellBorderType.Thin;
			int num = exportHeads.Length;
			DataRowCollection rows = dataTable.Rows;
			int count = rows.Count;
			cells.Merge(firstRow, 0, 1, num);
			cells[firstRow, 0].PutValue(titleName);
			cells[firstRow, 0].SetStyle(style);
			cells.SetRowHeight(firstRow, 38.0);
			for (int i = 0; i < num; i++)
			{
				cells[1 + firstRow, i].PutValue(exportHeads[i]);
				cells[1 + firstRow, i].SetStyle(style2);
				cells.SetColumnWidth(i, 10 + exportHeads[i].Length * 5);
			}
			cells.SetRowHeight(1 + firstRow, 38.0);
			for (int j = 0; j < count; j++)
			{
				for (int k = 0; k < num; k++)
				{
					cells[firstRow + 2 + j, k].PutValue(rows[j][exportFields[k]]);
					cells[firstRow + 2 + j, k].SetStyle(style3);
				}
			}
			return firstRow + count + 2;
		}

		public static void SetComboBox(Worksheet worksheet, IEnumerable<string> items, int curColumn, int maxRows = 300, int? endColumn = null, int startRow = 0)
		{
			CellArea ca = CellArea.CreateCellArea(startRow, curColumn, maxRows, endColumn ?? curColumn);
			int index = worksheet.Validations.Add(ca);
			Validation validation = worksheet.Validations[index];
			validation.Type = Aspose.Cells.ValidationType.List;
			validation.Formula1 = string.Join(",", items);
			validation.ShowError = true;
			validation.InCellDropDown = true;
			validation.ShowInput = true;
		}

		public static void SetComboBox(Worksheet worksheet, IEnumerable<string> items, Dictionary<string, string> columnTitleDict, params string[] colunms)
		{
			int num = 0;
			Dictionary<string, string> dictionary = colunms.ToDictionary<string, string, string>((string p) => p, (string p) => p);
			foreach (KeyValuePair<string, string> item in columnTitleDict)
			{
				if (dictionary.ContainsKey(item.Key))
				{
					SetComboBox(worksheet, items, num);
				}
				num++;
			}
		}

		public static void TemplateToExcel(string templatePath, string savePath, Dictionary<string, object> dataSources)
		{
			try
			{
				WorkbookDesigner workbookDesigner = new WorkbookDesigner();
				Workbook workbook = new Workbook(templatePath);
				workbookDesigner.Workbook = workbook;
				if (dataSources != null && dataSources.Any())
				{
					foreach (KeyValuePair<string, object> dataSource in dataSources)
					{
						workbookDesigner.SetDataSource(dataSource.Key, dataSource.Value);
					}
				}
				workbookDesigner.CalculateFormula = true;
				workbookDesigner.Process(isPreserved: true);
				if (File.Exists(savePath))
				{
					File.Delete(savePath);
				}
				workbookDesigner.Workbook.Save(savePath);
				workbookDesigner.ClearDataSource();
			}
			catch (Exception)
			{
				throw;
			}
		}
	}
	public class CdeHelper
	{
		public static void Signal(CountdownEvent cde)
		{
			if (cde != null && !cde.IsSet)
			{
				cde.Signal();
			}
		}
	}
	public class CmdHelper
	{
		public static string ExcuteCmd(string c, string workDirectory = null)
		{
			Process process = new Process();
			process.StartInfo.FileName = "cmd.exe";
			process.StartInfo.WorkingDirectory = workDirectory;
			process.StartInfo.UseShellExecute = false;
			process.StartInfo.CreateNoWindow = true;
			process.StartInfo.RedirectStandardOutput = true;
			process.StartInfo.RedirectStandardInput = true;
			process.Start();
			process.StandardInput.WriteLine(c);
			process.StandardInput.AutoFlush = true;
			process.StandardInput.WriteLine("exit");
			StreamReader standardOutput = process.StandardOutput;
			string text = standardOutput.ReadLine();
			while (!standardOutput.EndOfStream)
			{
				text += standardOutput.ReadLine();
			}
			process.WaitForExit();
			return text;
		}
	}
	public class ComputeHelper
	{
		public static string GetMacByNetworkInterface()
		{
			try
			{
				NetworkInterface[] allNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
				NetworkInterface[] array = allNetworkInterfaces;
				foreach (NetworkInterface networkInterface in array)
				{
					if (networkInterface.Description.Contains("Wi-Fi Direct Virtual Adapter") || networkInterface.Name.Contains("以太网"))
					{
						return BitConverter.ToString(networkInterface.GetPhysicalAddress().GetAddressBytes());
					}
				}
			}
			catch (Exception)
			{
			}
			return "00-00-00-00-00-00";
		}
	}
	public enum PurchaseType
	{
		None = 0,
		Services = 1,
		Materials = 2,
		LilteFrame = 0x10,
		Protocol_Store = 0x20,
		Frame_Protocol = 0x40
	}
	public class ConfigHelper
	{
		private static string URL_REG = "http[s]?://\\w{2,8}\\.\\w{2,8}.\\w{2,8}(.\\w{2,8})?(:\\d{4,5})?";

		private static bool useAppConfig = false;

		public static PurchaseType PurchaseType { get; set; }

		public static string SqliteDBName { get; } = GetValueByKey("sqliteDBName");


		public static string SqliteLocation { get; } = GetValueByKey("sqliteDBLocation");


		public static string GetValueByKey(string key)
		{
			if (useAppConfig)
			{
				return ConfigurationManager.AppSettings.Get(key) ?? "";
			}
			string text = ApiConfigHelper.GetValueByKey(key) ?? "";
			if ((text == null || !text.StartsWith("http")) && key.StartsWith("dataApiPrefixUrl", StringComparison.CurrentCultureIgnoreCase))
			{
				text = SM4Util.decryptEcb(ContantUtils.SM4_KEY, text);
			}
			return text;
		}

		public static void SetPurchaseType()
		{
			string selectSql = "SELECT PROJECT_NO,PROJECT_NAME,MARK_NO,PURCHASE_TYPE FROM OS_ZB_PURCHASE_PROJECT_INFO LIMIT 0,1";
			DataTable dataTable = SQLiteLibrary.SelectBySql(selectSql);
			if (dataTable != null && dataTable.Rows?.Count > 0)
			{
				PurchaseType = ("服务招标".Equals(dataTable.Rows[0]["PURCHASE_TYPE"]?.ToString()) ? PurchaseType.Services : PurchaseType.Materials);
			}
		}

		public static void SetUseAppConfig(bool bl)
		{
			useAppConfig = bl;
		}
	}
	public class ContantUtils
	{
		public static string SM4_KEY = "86C63180C2806ED1F47B859DE501215B";

		public static string PUBLIC_LOGIN_URL = "/public/login";

		public static string PUBLIC_UPDATE_URL = "/public/update";

		public static string PUBLIC_REL_INSERT_URL = "/public/saveList";

		public static string PUBLIC_REL_DELETE_URL = "/public/delete";

		public static string PUBLIC_FILE_BACK_URL = "/public/backSupplierBidFileInfo";

		public static string PUBLIC_CHECK_VERSION = "/public/getLasterVerInfo";

		public static string PUBLIC_SELECT_PROJECT = "/public/selectCurProject";

		public static string PUBLIC_SYN_PROJECT_LIST = "/public/synProjectList";
	}
	public class DataGridColumnHelper
	{
		public static DataGridViewTextBoxColumn GetDefaultTextBoxColumn(string title, string name, string propName = null, int width = 0)
		{
			DataGridViewTextBoxColumn dataGridViewTextBoxColumn = new DataGridViewTextBoxColumn
			{
				Name = name,
				HeaderText = title,
				DataPropertyName = (propName ?? name),
				AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet
			};
			if (width > 0)
			{
				dataGridViewTextBoxColumn.Width = width;
				dataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
			}
			return dataGridViewTextBoxColumn;
		}

		public static DataGridViewButtonColumn GetDefaultButtonColumn(string title, string name, string propName = null, string tag = null, int width = 0)
		{
			DataGridViewButtonColumn dataGridViewButtonColumn = new DataGridViewButtonColumn
			{
				Name = name,
				HeaderText = title,
				DataPropertyName = (propName ?? name),
				Tag = (tag ?? "uploadAttach"),
				AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet,
				UseColumnTextForButtonValue = false
			};
			if (width > 0)
			{
				dataGridViewButtonColumn.Width = width;
				dataGridViewButtonColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
			}
			return dataGridViewButtonColumn;
		}

		public static DataGridViewLinkColumn GetDefaultLinkColumn(string title, string name, string propName = null, int width = 0)
		{
			DataGridViewLinkColumn dataGridViewLinkColumn = new DataGridViewLinkColumn
			{
				Name = name,
				HeaderText = title,
				ActiveLinkColor = Color.Blue,
				LinkColor = Color.Blue,
				VisitedLinkColor = Color.Blue,
				UseColumnTextForLinkValue = true,
				AutoSizeMode = DataGridViewAutoSizeColumnMode.NotSet
			};
			if (width > 0)
			{
				dataGridViewLinkColumn.Width = width;
				dataGridViewLinkColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
			}
			return dataGridViewLinkColumn;
		}
	}
	public enum DgvColumnType
	{
		Link,
		TextBox,
		ComboBox,
		CheckBox,
		Button
	}
	public class DataGridViewHelper
	{
		public static DataGridViewColumn CreateDataGridViewColumn(string name, string headerText, string propName = null, string nullValue = null, DgvColumnType dgvColumnType = DgvColumnType.TextBox)
		{
			DataGridViewColumn dataGridViewColumn = dgvColumnType switch
			{
				DgvColumnType.Link => new DataGridViewLinkColumn
				{
					DefaultCellStyle = new DataGridViewCellStyle
					{
						NullValue = nullValue
					}
				}, 
				DgvColumnType.TextBox => new DataGridViewTextBoxColumn
				{
					DefaultCellStyle = new DataGridViewCellStyle
					{
						NullValue = nullValue
					}
				}, 
				DgvColumnType.ComboBox => new DataGridViewComboBoxColumn
				{
					DefaultCellStyle = new DataGridViewCellStyle
					{
						NullValue = nullValue
					}
				}, 
				DgvColumnType.CheckBox => new DataGridViewCheckBoxColumn
				{
					DefaultCellStyle = new DataGridViewCellStyle
					{
						NullValue = nullValue
					}
				}, 
				DgvColumnType.Button => new DataGridViewButtonColumn
				{
					DefaultCellStyle = new DataGridViewCellStyle
					{
						NullValue = (nullValue ?? "已上传数(0)"),
						Alignment = DataGridViewContentAlignment.MiddleCenter
					}
				}, 
				_ => new DataGridViewTextBoxColumn(), 
			};
			dataGridViewColumn.Name = name;
			dataGridViewColumn.DataPropertyName = propName ?? name;
			dataGridViewColumn.HeaderText = headerText;
			return dataGridViewColumn;
		}
	}
	public static class DataTableUtils
	{
		public static List<T> ToDataList<T>(this DataTable dt)
		{
			List<T> list = new List<T>();
			List<PropertyInfo> list2 = new List<PropertyInfo>(typeof(T).GetProperties());
			foreach (DataRow row in dt.Rows)
			{
				T val = Activator.CreateInstance<T>();
				int i;
				for (i = 0; i < dt.Columns.Count; i++)
				{
					PropertyInfo propertyInfo = list2.Find((PropertyInfo p) => humpToUnderline(p.Name) == dt.Columns[i].ColumnName.ToUpper());
					if (!(propertyInfo != (PropertyInfo)null))
					{
						continue;
					}
					try
					{
						if (!Convert.IsDBNull(row[i]))
						{
							object obj = null;
							propertyInfo.SetValue(value: (!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(row[i], propertyInfo.PropertyType) : Convert.ChangeType(row[i], Nullable.GetUnderlyingType(propertyInfo.PropertyType)), obj: val, index: null);
						}
					}
					catch (FormatException innerException)
					{
						throw new FormatException($"字段/属性：{propertyInfo.Name}格式转换异常,值:{row?[i]},类型:{propertyInfo.PropertyType}", innerException);
					}
					catch (Exception ex)
					{
						throw new Exception("字段[" + propertyInfo.Name + "]转换出错," + ex.Message);
					}
				}
				list.Add(val);
			}
			return list;
		}

		public static T ToDataDto<T>(this DataTable dt)
		{
			T val = Activator.CreateInstance<T>();
			if (dt == null || dt.Rows.Count == 0)
			{
				return val;
			}
			List<PropertyInfo> list = new List<PropertyInfo>(typeof(T).GetProperties());
			int i;
			for (i = 0; i < dt.Columns.Count; i++)
			{
				PropertyInfo propertyInfo = list.Find((PropertyInfo p) => humpToUnderline(p.Name) == dt.Columns[i].ColumnName.ToUpper());
				if (!(propertyInfo != (PropertyInfo)null))
				{
					continue;
				}
				try
				{
					if (!Convert.IsDBNull(dt.Rows[0][i]))
					{
						object obj = null;
						propertyInfo.SetValue(value: (!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(dt.Rows[0][i], propertyInfo.PropertyType) : Convert.ChangeType(dt.Rows[0][i], Nullable.GetUnderlyingType(propertyInfo.PropertyType)), obj: val, index: null);
					}
				}
				catch (FormatException innerException)
				{
					throw new FormatException($"字段/属性：{propertyInfo.Name}格式转换异常,值:{dt.Rows?[0][i]},类型:{propertyInfo.PropertyType}", innerException);
				}
				catch (Exception ex)
				{
					throw new Exception("字段[" + propertyInfo.Name + "]转换出错," + ex.Message);
				}
			}
			return val;
		}

		public static DataTable ToDataTable<T>(List<T> entities)
		{
			DataTable dataTable = CreateTable<T>();
			FillData(dataTable, entities);
			return dataTable;
		}

		private static DataTable CreateTable<T>()
		{
			DataTable dataTable = new DataTable();
			Type typeFromHandle = typeof(T);
			PropertyInfo[] properties = typeFromHandle.GetProperties(BindingFlags.Instance | BindingFlags.Public);
			foreach (PropertyInfo propertyInfo in properties)
			{
				Type type = propertyInfo.PropertyType;
				if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
				{
					type = type.GetGenericArguments()[0];
				}
				dataTable.Columns.Add(propertyInfo.Name, type);
			}
			return dataTable;
		}

		private static void FillData<T>(DataTable dt, IEnumerable<T> entities)
		{
			foreach (T entity in entities)
			{
				dt.Rows.Add(CreateRow(dt, entity));
			}
		}

		private static DataRow CreateRow<T>(DataTable dt, T entity)
		{
			DataRow dataRow = dt.NewRow();
			Type typeFromHandle = typeof(T);
			PropertyInfo[] properties = typeFromHandle.GetProperties(BindingFlags.Instance | BindingFlags.Public);
			foreach (PropertyInfo propertyInfo in properties)
			{
				dataRow[propertyInfo.Name] = propertyInfo.GetValue(entity) ?? DBNull.Value;
			}
			return dataRow;
		}

		public static string humpToUnderline(string strItem)
		{
			if (string.IsNullOrEmpty(strItem))
			{
				return "";
			}
			string text = "";
			for (int i = 0; i < strItem.Length; i++)
			{
				string text2 = strItem[i].ToString();
				if (Regex.IsMatch(text2, "[A-Z]") && i != 0)
				{
					text2 = "_" + text2.ToLower();
				}
				text += text2;
			}
			return text.ToUpper();
		}

		public static List<T> ToList<T>(DataTable dt)
		{
			List<T> list = new List<T>();
			if (dt == null)
			{
				return list;
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			Type typeFromHandle = typeof(T);
			TableFieldAttribute tableFieldAttribute = null;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			for (int i = 0; i < fields.Length; i++)
			{
				tableFieldAttribute = fields[i].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.ColumName != null)
				{
					dictionary.Add(tableFieldAttribute.ColumName, typeFromHandle.GetProperty(SQLiteSqlUtils.UpperCaseFirst(fields[i].Name)));
				}
			}
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			for (int j = 0; j < properties.Length; j++)
			{
				tableFieldAttribute = properties[j].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.ColumName != null)
				{
					dictionary.Add(tableFieldAttribute.ColumName, properties[j]);
				}
			}
			PropertyInfo propertyInfo = null;
			foreach (DataRow row in dt.Rows)
			{
				T val = Activator.CreateInstance<T>();
				for (int k = 0; k < dt.Columns.Count; k++)
				{
					if (dictionary.ContainsKey(dt.Columns[k].ColumnName))
					{
						propertyInfo = dictionary[dt.Columns[k].ColumnName];
						object obj = null;
						propertyInfo.SetValue(value: (!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(row[k], propertyInfo.PropertyType) : Convert.ChangeType(row[k], Nullable.GetUnderlyingType(propertyInfo.PropertyType)), obj: val, index: null);
					}
				}
				list.Add(val);
			}
			return list;
		}

		public static List<object> ToList(Type type, DataTable dt)
		{
			List<object> list = new List<object>();
			if (dt == null)
			{
				return list;
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			Assembly assembly = type.Assembly;
			Type baseType = type.BaseType;
			TableFieldAttribute tableFieldAttribute = null;
			FieldInfo[] fields = type.GetFields(bindingAttr);
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			for (int i = 0; i < fields.Length; i++)
			{
				tableFieldAttribute = fields[i].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.ColumName != null)
				{
					dictionary.Add(tableFieldAttribute.ColumName, type.GetProperty(SQLiteSqlUtils.UpperCaseFirst(fields[i].Name)));
				}
			}
			PropertyInfo propertyInfo = null;
			foreach (DataRow row in dt.Rows)
			{
				object obj = type.Assembly.CreateInstance(type.FullName);
				for (int j = 0; j < dt.Columns.Count; j++)
				{
					if (dictionary.ContainsKey(dt.Columns[j].ColumnName))
					{
						propertyInfo = dictionary[dt.Columns[j].ColumnName];
						object obj2 = null;
						obj2 = ((!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(row[j], propertyInfo.PropertyType) : Convert.ChangeType(row[j], Nullable.GetUnderlyingType(propertyInfo.PropertyType)));
						propertyInfo.SetValue(obj, obj2, null);
					}
				}
				list.Add(obj);
			}
			return list;
		}

		public static T ToDataDto<T>(this DataRow dr)
		{
			T val = Activator.CreateInstance<T>();
			if (dr == null)
			{
				return val;
			}
			Type typeFromHandle = typeof(T);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			PropertyInfo propertyInfo = null;
			FieldInfo[] array = fields;
			foreach (FieldInfo fieldInfo in array)
			{
				TableFieldAttribute customAttribute = fieldInfo.GetCustomAttribute<TableFieldAttribute>();
				if ((propertyInfo = typeFromHandle.GetProperty(fieldInfo.Name.UpperCaseFirst())) != (PropertyInfo)null)
				{
					dictionary.AddIfAbsent(customAttribute?.ColumName ?? humpToUnderline(fieldInfo.Name), propertyInfo);
					dictionary.AddIfAbsent(fieldInfo.Name.ToUpper(), propertyInfo);
				}
			}
			PropertyInfo[] array2 = properties;
			foreach (PropertyInfo propertyInfo2 in array2)
			{
				dictionary.AddIfAbsent(propertyInfo2.GetCustomAttribute<TableFieldAttribute>()?.ColumName ?? humpToUnderline(propertyInfo2.Name), propertyInfo2);
				dictionary.AddIfAbsent(propertyInfo2.Name.ToUpper(), propertyInfo2);
			}
			DataColumnCollection columns = dr.Table.Columns;
			for (int k = 0; k < columns.Count; k++)
			{
				PropertyInfo propertyInfo3 = ((!dictionary.ContainsKey(columns[k].ColumnName.ToUpper())) ? null : dictionary[columns[k].ColumnName.ToUpper()]);
				if (!(propertyInfo3 != (PropertyInfo)null))
				{
					continue;
				}
				try
				{
					if (!Convert.IsDBNull(dr[k]))
					{
						object obj = null;
						propertyInfo3.SetValue(value: (!propertyInfo3.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(dr[k], propertyInfo3.PropertyType) : Convert.ChangeType(dr[k], Nullable.GetUnderlyingType(propertyInfo3.PropertyType)), obj: val, index: null);
					}
				}
				catch (FormatException innerException)
				{
					throw new FormatException($"字段/属性：{propertyInfo3.Name}格式转换异常,值:{dr?[k]},类型:{propertyInfo3.PropertyType}", innerException);
				}
				catch (Exception ex)
				{
					throw new Exception("字段[" + propertyInfo3.Name + "]转换出错," + ex.Message);
				}
			}
			return val;
		}

		public static Dictionary<IKey, IValue> ToDictionary<IKey, IValue>(this DataTable dt, Action<Dictionary<IKey, IValue>, DataRow> action)
		{
			if (dt == null || dt.Rows.Count == 0)
			{
				return null;
			}
			Dictionary<IKey, IValue> dictionary = new Dictionary<IKey, IValue>();
			DataRowCollection rows = dt.Rows;
			for (int i = 0; i < rows.Count; i++)
			{
				action.Invoke(dictionary, rows[i]);
			}
			return dictionary;
		}

		public static Dictionary<IKey, IValue> ToDictionary<IKey, IValue>(this DataTable dt, Func<DataRow, IKey> actionKey, Func<DataRow, IValue> actionValue)
		{
			if (dt == null || dt.Rows.Count == 0)
			{
				return null;
			}
			Dictionary<IKey, IValue> dictionary = new Dictionary<IKey, IValue>();
			DataRowCollection rows = dt.Rows;
			for (int i = 0; i < rows.Count; i++)
			{
				dictionary.Add(actionKey.Invoke(rows[i]), actionValue.Invoke(rows[i]));
			}
			return dictionary;
		}
	}
	public class DateUtil
	{
		public static string yyyyMMdd = "yyyyMMdd";

		public static string yyyy_MM_dd = "yyyy-MM-dd";

		public static string yyyy_MM_ddhhmmss = "yyyy-MM-dd hh:mm:ss";

		public static string GetTimeStamp(DateTime time)
		{
			return ConvertDateTimeToInt(time).ToString();
		}

		public static long ConvertDateTimeToInt(DateTime time)
		{
			DateTime dateTime = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1, 0, 0, 0, 0));
			return (time.Ticks - dateTime.Ticks) / 10000;
		}

		public static DateTime StrToDateTime(string source)
		{
			return Convert.ToDateTime(source);
		}

		public static DateTime StrToDateTime(string source, string pattern)
		{
			if (pattern == null || "".Equals(pattern))
			{
				pattern = "yyyy/MM/dd";
			}
			return DateTime.ParseExact(source, pattern, CultureInfo.CurrentCulture);
		}

		public static void SetDatePick(ref DateTimePicker timePicker, string customFormat, bool allowDrop, Action<object, EventArgs> changeCall)
		{
			customFormat = ((customFormat == null) ? "yyyy-MM-dd" : customFormat);
			timePicker.CustomFormat = "   ";
			timePicker.Format = DateTimePickerFormat.Custom;
			timePicker.AllowDrop = allowDrop;
			if (timePicker.Text != null && !"".Equals(timePicker.Text))
			{
				timePicker.CustomFormat = customFormat;
				timePicker.ShowCheckBox = true;
			}
			else
			{
				timePicker.ShowCheckBox = false;
			}
			if (changeCall != null)
			{
				timePicker.ValueChanged += changeCall.Invoke;
			}
		}

		public static void SetDatePick(ref DateTimePicker timePicker, string customFormat, bool allowDrop)
		{
			SetDatePick(ref timePicker, customFormat, allowDrop, null);
		}

		public static void SetDatePick(ref DateTimePicker timePicker, string customFormat)
		{
			SetDatePick(ref timePicker, customFormat, allowDrop: true, null);
		}

		public static DateTime GetDateTime(string timeStamp)
		{
			long ticks = new DateTime(1970, 1, 1).Ticks;
			long ticks2 = ticks + long.Parse(timeStamp) * 10000;
			return new DateTime(ticks2);
		}
	}
	public class DictComboboxHelper
	{
		public static void SetComboboxWithValue(ComboBox comboBox, Dictionary<string, List<CmCDictionaryDetails>> dictionaries, string dictKey, string selectValue)
		{
			if (dictionaries == null)
			{
				return;
			}
			List<CmCDictionaryDetails> list = dictionaries[dictKey];
			list.Insert(0, new CmCDictionaryDetails
			{
				DictionaryDispName = "请选择",
				DictionaryValue = ""
			});
			string text = "";
			int selectedIndex = 0;
			for (int i = 0; i < list.Count; i++)
			{
				if (list[i].DictionaryValue == selectValue)
				{
					list[i].Selected = true;
					text = list[i].DictionaryDispName;
					selectedIndex = i;
				}
			}
			comboBox.DataSource = list;
			comboBox.DisplayMember = "DictionaryDispName";
			comboBox.ValueMember = "DictionaryValue";
			comboBox.SelectedIndex = selectedIndex;
			comboBox.Text = text ?? "请选择";
		}

		public static void SetEntityWithDictName<T>(T t, Dictionary<string, List<CmCDictionaryDetails>> dictionaries, string dictKey, string sourceField, string setFieldName)
		{
			if (dictionaries == null)
			{
				return;
			}
			Type typeFromHandle = typeof(T);
			PropertyInfo property = typeFromHandle.GetProperty(sourceField);
			PropertyInfo property2 = typeFromHandle.GetProperty(setFieldName);
			if (property == (PropertyInfo)null || property2 == (PropertyInfo)null)
			{
				return;
			}
			List<CmCDictionaryDetails> list = dictionaries[dictKey];
			for (int i = 0; i < list.Count; i++)
			{
				if (list[i].DictionaryValue == property.GetValue(t)?.ToString())
				{
					property2.SetValue(t, list[i].DictionaryDispName);
				}
			}
		}

		public static void SetEntityWithDictName<T>(List<T> list, Dictionary<string, List<CmCDictionaryDetails>> dictionaries, string dictKey, string sourceField, string setFieldName)
		{
			if (list != null)
			{
				for (int i = 0; i < list.Count; i++)
				{
					SetEntityWithDictName(list[i], dictionaries, dictKey, sourceField, setFieldName);
				}
			}
		}

		public static void SetComboboxWithValue(DataGridViewComboBoxColumn comboBoxColumn, Dictionary<string, List<CmCDictionaryDetails>> dictionaries, string dictKey, string selectValue)
		{
			if (dictionaries == null)
			{
				return;
			}
			List<CmCDictionaryDetails> list = dictionaries[dictKey];
			list.Insert(0, new CmCDictionaryDetails
			{
				DictionaryDispName = "请选择",
				DictionaryValue = ""
			});
			string text = "";
			int num = 0;
			for (int i = 0; i < list.Count; i++)
			{
				if (list[i].DictionaryValue == selectValue)
				{
					list[i].Selected = true;
					text = list[i].DictionaryDispName;
					num = i;
				}
			}
			comboBoxColumn.DataSource = list;
			comboBoxColumn.DisplayMember = "DictionaryDispName";
			comboBoxColumn.ValueMember = "DictionaryValue";
		}
	}
	public class DownLoadHelper
	{
		public static void DownFileAsy(string url, string fullName, AsyncCompletedEventHandler DownloadFileCompleted = null, DownloadProgressChangedEventHandler DownloadProgressChanged = null)
		{
			using WebClient webClient = new WebClient();
			try
			{
				if (DownloadFileCompleted != null)
				{
					webClient.DownloadFileCompleted += DownloadFileCompleted.Invoke;
				}
				if (DownloadProgressChanged != null)
				{
					webClient.DownloadProgressChanged += DownloadProgressChanged.Invoke;
				}
				webClient.DownloadFileAsync(new Uri(url), fullName);
			}
			catch (WebException ex)
			{
				MessageHelper.ShowError(ex);
			}
		}

		public static bool DownFile(string url, string fullName, IDictionary<string, object> dict)
		{
			bool result = false;
			using (WebClient webClient = new WebClient())
			{
				try
				{
					url = url + "?rnd=" + DateTime.Now.ToFileTimeUtc();
					if (dict != null)
					{
						url = url + "&_encData=" + SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(dict));
					}
					webClient.DownloadFile(new Uri(url), fullName);
					result = File.Exists(fullName) && new FileInfo(fullName).Length > 3072;
				}
				catch (WebException ex)
				{
					string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm") + " :  下载异常,错误信息:/r/n";
					text = text + ex.Message + "\r\n";
					text = text + "文件名:" + fullName;
					result = false;
				}
				finally
				{
					webClient.Dispose();
				}
			}
			return result;
		}

		private void Client_DownloadProgressChanged(object sender, DownloadProgressChangedEventArgs e)
		{
		}

		private void Client_DownloadFileCompleted(object sender, AsyncCompletedEventArgs e)
		{
			if (e.Error != null)
			{
				MessageHelper.ShowError(e.Error.Message);
			}
		}

		public static R DownFileByHttp(string url, Dictionary<string, object> dict, string filePath, int timeout = 30000, Action<decimal> fackBack = null)
		{
			url = url + "?rnd=" + DateTime.Now.ToFileTimeUtc();
			if (dict != null)
			{
				url = url + "&_encData=" + SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(dict));
			}
			return HttpPostDown(url, filePath, timeout, fackBack);
		}

		private static R HttpPostDown(string url, string filePath, int Timeout = 100000, Action<decimal> fallBack = null)
		{
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
			httpWebRequest.Method = "POST";
			httpWebRequest.ReadWriteTimeout = 5000;
			httpWebRequest.Timeout = Timeout;
			httpWebRequest.KeepAlive = true;
			httpWebRequest.ContentType = "application/json";
			try
			{
				LogsHelper.InsertLog("下载地址:" + url, null, "Logs/operate.log");
				decimal d = 0m;
				using (httpWebRequest.GetRequestStream())
				{
					using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
					using Stream stream = httpWebResponse.GetResponseStream();
					decimal d2 = 1024m;
					string responseHeader = httpWebResponse.GetResponseHeader("fileLength");
					if (!string.IsNullOrWhiteSpace(responseHeader))
					{
						d2 = Convert.ToDecimal(responseHeader);
					}
					using FileStream fileStream = File.Open(filePath, FileMode.OpenOrCreate);
					byte[] array = new byte[1024];
					int num = stream.Read(array, 0, array.Length);
					while (num > 0)
					{
						fileStream.Write(array, 0, num);
						num = stream.Read(array, 0, array.Length);
						d += (decimal)num;
						if (fallBack != null)
						{
							decimal obj = decimal.Divide(d, d2);
							fallBack(obj);
						}
					}
				}
				return new R
				{
					Code = 200,
					Successful = true,
					ResultHint = "操作成功"
				};
			}
			catch (Exception ex)
			{
				return new R
				{
					Code = 502,
					Successful = false,
					ResultHint = ex.Message
				};
			}
		}
	}
	public class ExcelReadUtil
	{
		private const string DATE_FORMART = "yyyy-MM-dd";

		private const int ROW_COLUMN_SPACE_COUNT = 2;

		private const int MAX_LINE = 120000;

		[Obsolete("推荐使用ReadExcel")]
		private static MessageInfo<T> ReadExcel<T>(int firstRow, bool readAllSheet, int startSheet, int titleRow)
		{
			Stopwatch stopwatch = new Stopwatch();
			stopwatch.Start();
			MessageInfo<T> result = new MessageInfo<T>();
			try
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "文件|*.xlsx;*.xlsx";
				openFileDialog.FileName = "";
				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					Workbook workbook = new Workbook(openFileDialog.FileName);
					WorkbookSettings settings = workbook.Settings;
					settings.MemorySetting = MemorySetting.MemoryPreference;
					if (readAllSheet)
					{
						result = GetmessageInfo<T>(workbook.Worksheets, startSheet, firstRow, titleRow, readAllSheet);
					}
					else
					{
						for (int i = 0; i < workbook.Worksheets.Count; i++)
						{
							if (startSheet != i)
							{
								workbook.Worksheets.RemoveAt(i);
							}
						}
						result = GetmessageInfo<T>(workbook.Worksheets, startSheet, firstRow, titleRow);
					}
					stopwatch.Stop();
					Console.WriteLine("excel解析时长: {0}ms", stopwatch.ElapsedMilliseconds);
					return result;
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				MessageBox.Show("导入文件错误信息：" + ex.Message);
			}
			return result;
		}

		[Obsolete("推荐使用GetmessageInfoV2")]
		public static MessageInfo<T> GetmessageInfo<T>(WorksheetCollection worksheets, int startSheet, int firstRow, int titleRow, bool readAll = false)
		{
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			int count = worksheets.Count;
			bool existError = false;
			int num = 0;
			StringBuilder stringBuilder = new StringBuilder();
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			List<T> list = new List<T>();
			Type typeFromHandle = typeof(T);
			ColumnInfoAttribute columnInfoAttribute = null;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			Dictionary<int, PropertyInfo> dictionary = new Dictionary<int, PropertyInfo>();
			Dictionary<int, ColumnInfoAttribute> dictionary2 = new Dictionary<int, ColumnInfoAttribute>();
			Dictionary<int, string> dictionary3 = new Dictionary<int, string>();
			PropertyInfo property = typeFromHandle.GetProperty("CurExcelIndex");
			PropertyInfo propertyInfo = null;
			PropertyInfo propertyInfo2 = null;
			bool flag = false;
			for (int i = 0; i < fields.Length; i++)
			{
				columnInfoAttribute = fields[i].GetCustomAttribute<ColumnInfoAttribute>();
				if (columnInfoAttribute != null)
				{
					try
					{
						dictionary.Add(columnInfoAttribute.Index, typeFromHandle.GetProperty(UpperCaseFirst(fields[i].Name)));
						dictionary2.Add(columnInfoAttribute.Index, columnInfoAttribute);
					}
					catch (Exception ex)
					{
						Console.WriteLine(ex.Message);
					}
				}
			}
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			for (int j = 0; j < properties.Length; j++)
			{
				columnInfoAttribute = properties[j].GetCustomAttribute<ColumnInfoAttribute>();
				if (columnInfoAttribute != null)
				{
					try
					{
						dictionary.Add(columnInfoAttribute.Index, properties[j]);
						dictionary2.Add(columnInfoAttribute.Index, columnInfoAttribute);
					}
					catch (Exception ex2)
					{
						Console.WriteLine(ex2.Message);
					}
				}
			}
			string format = (readAll ? "sheetName为{0}," : "");
			for (int k = 0; k < count; k++)
			{
				dictionary3.Clear();
				if (k < startSheet)
				{
					continue;
				}
				int num2 = 0;
				Cells cells = worksheets[k].Cells;
				RowCollection rows = cells.Rows;
				int num3 = 0;
				for (int l = 0; l < rows.Count; l++)
				{
					num2++;
					T val = Activator.CreateInstance<T>();
					Cell firstCell = rows[l].FirstCell;
					Cell lastCell = rows[l].LastCell;
					if (l == titleRow)
					{
						num3 = lastCell.Column;
						for (int m = firstCell.Column; m <= lastCell.Column; m++)
						{
							dictionary3.Add(m, cells[l, m].Value.ToString());
						}
					}
					if (l < firstRow)
					{
						continue;
					}
					if (firstCell == null && lastCell == null)
					{
						break;
					}
					Cell cell = cells[l, (lastCell.Column + firstCell.Column) / 2];
					if (ObjectUtil.IsEmptyObj(firstCell.Value) && ObjectUtil.IsEmptyObj(lastCell.Value) && lastCell.Column - firstCell.Column <= 2 && ObjectUtil.IsEmptyObj(cell.Value))
					{
						break;
					}
					num3 = Math.Max(num3, lastCell.Column);
					for (int n = firstCell.Column; n <= num3; n++)
					{
						if (!dictionary.ContainsKey(n))
						{
							continue;
						}
						propertyInfo = dictionary[n];
						columnInfoAttribute = dictionary2[n];
						object obj;
						if (cells[l, n].Value == null)
						{
							obj = null;
						}
						else if (cells[l, n].Type == CellValueType.IsDateTime)
						{
							obj = cells[l, n].DateTimeValue.ToString("yyyy-MM-dd");
							obj = Convert.ChangeType(obj, propertyInfo.PropertyType);
						}
						else
						{
							obj = Convert.ChangeType(cells[l, n].Value, propertyInfo.PropertyType);
						}
						if (!"Id".Equals(propertyInfo.Name) && n == lastCell.Column - 1)
						{
							propertyInfo2 = typeFromHandle.GetProperty("Id", bindingAttr);
							if (propertyInfo2 != (PropertyInfo)null)
							{
								propertyInfo2.SetValue(val, Guid.NewGuid().ToString("N"), null);
							}
							if (property != (PropertyInfo)null)
							{
								property.SetValue(val, num2, null);
							}
						}
						if (!columnInfoAttribute.Nullable && (obj == null || obj.ToString().Trim().Length <= 0))
						{
							existError = true;
							stringBuilder.Append(string.Format(format, worksheets[k].CodeName) + "第" + num2 + "行，" + dictionary3[n] + " 数据不得为空\n");
							flag = true;
						}
						if (obj != null && columnInfoAttribute.MaxLength < obj.ToString().Length)
						{
							existError = true;
							stringBuilder.Append(string.Format(format, worksheets[k].CodeName) + "第" + num2 + "行，" + dictionary3[n] + " 数据长度不得超过" + columnInfoAttribute.MaxLength + "个字符\n");
							flag = true;
						}
						if (obj != null && !string.IsNullOrEmpty(columnInfoAttribute.Pattern) && !Regex.IsMatch(obj.ToString().Trim(), columnInfoAttribute.Pattern))
						{
							existError = true;
							stringBuilder.Append(string.Format(format, worksheets[k].CodeName) + "第" + num2 + "行，" + dictionary3[n] + " " + (columnInfoAttribute.ErrorInfo ?? "数据格式不正确\n"));
							flag = true;
						}
						propertyInfo.SetValue(val, obj, null);
					}
					if (flag)
					{
						flag = false;
						num++;
					}
					list.Add(val);
					if (list != null && list.Count > 120000)
					{
						throw new Exception($"当前excel中某个sheet数据条数超过{120000}万行，如有需要请分批导入");
					}
				}
			}
			messageInfo.Record = list;
			messageInfo.ErrorInfo = stringBuilder.ToString();
			messageInfo.TotalCount = list.Count;
			messageInfo.ErrorCount = num;
			messageInfo.SuccessCount = messageInfo.TotalCount - num;
			messageInfo.ExistError = existError;
			if (list.Count <= 0)
			{
				messageInfo.ExistError = true;
				messageInfo.ErrorInfo = "未填写数据，至少填写一条数据";
				messageInfo.NoData = true;
			}
			return messageInfo;
		}

		public static string UpperCaseFirst(string str)
		{
			if (string.IsNullOrEmpty(str))
			{
				return string.Empty;
			}
			char[] array = str.ToCharArray();
			char c = array[0];
			if ('a' <= c && c <= 'z')
			{
				c = (char)(c & 0xFFFFFFDFu);
			}
			array[0] = c;
			return new string(array);
		}

		[Obsolete("推荐使用ReadExcel")]
		public static MessageInfo<T> ReadExcel<T>(int firstRow, int startSheet = 0, int titleRow = 0)
		{
			return ReadExcel<T>(firstRow, readAllSheet: false, startSheet, titleRow);
		}

		private static bool CheckRowWhiteSpace(DataRow dataRow)
		{
			object[] itemArray = dataRow.ItemArray;
			if (itemArray == null)
			{
				return true;
			}
			int num = 0;
			for (int i = 0; i < itemArray.Length; i++)
			{
				if (string.IsNullOrWhiteSpace((itemArray[i] ?? "").ToString()))
				{
					num++;
				}
			}
			return num >= itemArray.Length - 1;
		}

		public static MessageInfo<T> GetSheetEntityInfo<T>(int startSheet, int firstRow, Func<int, object, T, string> VerifyCellc, bool readAll = false)
		{
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "文件|*.xls;*.xlsx";
			openFileDialog.FileName = "";
			if (openFileDialog.ShowDialog() != DialogResult.OK)
			{
				return messageInfo;
			}
			Workbook workbook = new Workbook(openFileDialog.FileName);
			WorksheetCollection worksheets = workbook.Worksheets;
			int count = worksheets.Count;
			bool existError = false;
			int num = 0;
			StringBuilder stringBuilder = new StringBuilder();
			List<T> list = new List<T>();
			bool flag = false;
			string format = (readAll ? "sheetName为{0}," : "");
			for (int i = 0; i < count; i++)
			{
				if (i < startSheet)
				{
					continue;
				}
				int num2 = 0;
				Cells cells = worksheets[i].Cells;
				RowCollection rows = cells.Rows;
				for (int j = 0; j < rows.Count; j++)
				{
					num2++;
					T val = Activator.CreateInstance<T>();
					Cell firstCell = rows[j].FirstCell;
					Cell lastCell = rows[j].LastCell;
					if (j < firstRow)
					{
						continue;
					}
					if (firstCell == null && lastCell == null)
					{
						break;
					}
					Cell cell = cells[j, (lastCell.Column + firstCell.Column) / 2];
					if (ObjectUtil.IsEmptyObj(firstCell.Value) && ObjectUtil.IsEmptyObj(lastCell.Value) && lastCell.Column - firstCell.Column <= 2 && ObjectUtil.IsEmptyObj(cell.Value))
					{
						break;
					}
					for (int k = firstCell.Column; k <= lastCell.Column; k++)
					{
						object obj = ((cells[j, k].Value != null) ? cells[j, k].Value?.ToString() : null);
						string text = VerifyCellc.Invoke(k, obj, val);
						if (!string.IsNullOrEmpty(text))
						{
							existError = true;
							stringBuilder.Append(string.Format(format, worksheets[i].CodeName) + "第" + num2 + "行，" + text + "\r\n");
							flag = true;
						}
					}
					if (flag)
					{
						flag = false;
						num++;
					}
					list.Add(val);
					if (list != null && list.Count > 120000)
					{
						throw new Exception($"当前excel中某个sheet数据条数超过{120000}万行，如有需要请分批导入");
					}
				}
			}
			messageInfo.Record = list;
			messageInfo.ErrorInfo = stringBuilder.ToString();
			messageInfo.TotalCount = list.Count;
			messageInfo.ErrorCount = num;
			messageInfo.SuccessCount = messageInfo.TotalCount - num;
			messageInfo.ExistError = existError;
			if (list.Count <= 0)
			{
				messageInfo.ExistError = true;
				messageInfo.ErrorInfo = "未填写数据，至少填写一条数据";
			}
			return messageInfo;
		}

		public static DataSet ReadExcel(int startSheet, int? firstRow, int firstColumn, int? endColumn, int titleRow, Dictionary<string, string> titleMapper, Func<DataRow, bool> CheckAction = null, bool readAll = false, in StringBuilder sbf = null)
		{
			Dictionary<string, DataColumn> dictionary = new Dictionary<string, DataColumn>();
			foreach (KeyValuePair<string, string> item in titleMapper)
			{
				dictionary.Add(item.Key, new DataColumn(item.Value));
			}
			return ReadExcel(startSheet, firstColumn, titleRow, dictionary, CheckAction, readAll, in sbf);
		}

		public static DataSet ReadExcel(int startSheet, int firstColumn, int titleRow, Dictionary<string, DataColumn> titleMapper, Func<DataRow, bool> CheckAction = null, bool readAll = false, in StringBuilder sbf = null)
		{
			return ReadExcel(startSheet, firstColumn, titleRow, new Dictionary<int, Dictionary<string, DataColumn>> { { 0, titleMapper } }, CheckAction, readAll, in sbf);
		}

		public static DataSet ReadExcel(int startSheet, int firstColumn, int titleRow, Dictionary<int, Dictionary<string, DataColumn>> sheetTitleMapper, Func<DataRow, bool> CheckAction = null, bool readAll = false, in StringBuilder sbf = null)
		{
			DataSet dataSet = new DataSet();
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "文件|*.xls;*.xlsx";
			openFileDialog.FileName = "";
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				Workbook workbook = new Workbook(openFileDialog.FileName);
				WorksheetCollection worksheets = workbook.Worksheets;
				for (int i = 0; i < worksheets.Count; i++)
				{
					if (i != startSheet && !readAll)
					{
						continue;
					}
					int key = ((i > sheetTitleMapper.Count - 1) ? (sheetTitleMapper.Count - 1) : i);
					Dictionary<string, DataColumn> dictionary = sheetTitleMapper[key];
					Dictionary<string, string> dictionary2 = dictionary.ToDictionaryByLocal<KeyValuePair<string, DataColumn>, string, string>((KeyValuePair<string, DataColumn> p) => p.Value.ColumnName, (KeyValuePair<string, DataColumn> p) => p.Key);
					int num = 0;
					Cells cells = worksheets[i].Cells;
					RowCollection rows = cells.Rows;
					DataTable dataTable = new DataTable();
					DataColumnCollection columns = dataTable.Columns;
					Dictionary<int, DataColumn> dictionary3 = new Dictionary<int, DataColumn>();
					object obj = null;
					for (int j = 0; j < rows.Count; j++)
					{
						Cell firstCell = rows[j].FirstCell;
						Cell lastCell = rows[j].LastCell;
						if (j < titleRow)
						{
							continue;
						}
						if (firstCell == null && lastCell == null)
						{
							break;
						}
						int column = (lastCell.Column + firstCell.Column) / 2;
						Cell cellOrNull = rows[j].GetCellOrNull(column);
						Cell cell = ((cellOrNull == null) ? null : rows[j].GetCellOrNull((cellOrNull.Column + firstCell.Column) / 2));
						string text = string.Concat(str3: (((cellOrNull == null) ? null : rows[j].GetCellOrNull((cellOrNull.Column + lastCell.Column) / 2))?.Value)?.ToString(), str0: firstCell?.Value?.ToString(), str1: (cellOrNull?.Value)?.ToString(), str2: (cell?.Value)?.ToString());
						if (j == titleRow)
						{
							firstColumn = ((firstColumn > 0) ? firstColumn : firstCell.Column);
							for (int k = firstColumn; k <= lastCell.Column; k++)
							{
								obj = null;
								int num2 = 2;
								int num3 = j;
								while (num2 > 0 && num3 >= 0 && obj == null)
								{
									num2--;
									GetCellValue(rows, num3--, k, out obj);
								}
								if (obj != null)
								{
									obj = obj?.ToString();
								}
								string key2 = (obj?.ToString() ?? "").Trim();
								if (dictionary.ContainsKey(key2))
								{
									columns.Add(dictionary[key2]);
									dictionary3.Add(k, dictionary[key2]);
								}
							}
							foreach (KeyValuePair<string, DataColumn> item in dictionary)
							{
								if (!dictionary3.ContainsValue(item.Value))
								{
									columns.Add(item.Value);
								}
							}
							continue;
						}
						if (!columns.Contains("CurExcelIndex"))
						{
							columns.Add("CurExcelIndex");
						}
						if (ObjectUtil.IsEmptyObj(text?.Trim()))
						{
							break;
						}
						DataRow dataRow = dataTable.NewRow();
						foreach (KeyValuePair<int, DataColumn> item2 in dictionary3)
						{
							obj = null;
							if ((obj = rows[j].GetCellOrNull(item2.Key)?.Value) != null)
							{
								if (obj is DateTime dateTime)
								{
									dataRow[item2.Value] = dateTime.ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "");
									continue;
								}
								try
								{
									dataRow[item2.Value.ColumnName] = Convert.ChangeType(obj, item2.Value.DataType);
								}
								catch (Exception ex)
								{
									sbf.AppendLineLimit($"第{j + 1}行 {dictionary2[item2.Value.ColumnName]}{ex.Message}");
								}
							}
							else if (item2.Value.AllowDBNull)
							{
								dataRow[item2.Value] = obj;
							}
							if (!item2.Value.AllowDBNull && string.IsNullOrWhiteSpace(dataRow[item2.Value.ColumnName]?.ToString()))
							{
								sbf.AppendLineLimit($"第{j + 1}行 {dictionary2[item2.Value.ColumnName]}不能为空");
							}
						}
						dataRow["CurExcelIndex"] = j + 1;
						bool? flag = CheckAction?.Invoke(dataRow);
						if (!flag.HasValue || flag.Value)
						{
							try
							{
								dataTable.Rows.Add(dataRow);
							}
							catch (NoNullAllowedException)
							{
								Console.WriteLine();
							}
							catch (Exception ex3)
							{
								sbf.AppendLineLimit($"第{j + 1}行 {ex3.Message}");
							}
						}
						num++;
					}
					dataSet.Tables.Add(dataTable);
					if (dataTable != null && dataTable.Rows.Count > 120000)
					{
						throw new Exception($"当前excel中某个sheet数据条数超过{120000}万行，如有需要请分批导入");
					}
				}
			}
			return dataSet;
		}

		private static void GetCellValue(RowCollection rows, int r, int c, out object curValue)
		{
			curValue = rows[r].GetCellOrNull(c)?.Value;
		}

		public static MessageInfo<T> ReadExcel<T>(int startSheet, int titleRow, string businessId = null, Func<T, int, string> CheckAction = null, bool readAll = true)
		{
			Stopwatch stopwatch = new Stopwatch();
			stopwatch.Start();
			MessageInfo<T> result = new MessageInfo<T>();
			try
			{
				OpenFileDialog openFileDialog = new OpenFileDialog();
				openFileDialog.Filter = "文件|*.xlsx;*.xlsx";
				openFileDialog.FileName = "";
				if (openFileDialog.ShowDialog() == DialogResult.OK)
				{
					Workbook workbook = new Workbook(openFileDialog.FileName);
					WorkbookSettings settings = workbook.Settings;
					settings.MemorySetting = MemorySetting.MemoryPreference;
					result = GetmessageInfoV2<T>(workbook.Worksheets, startSheet, titleRow, businessId, readAll, CheckAction);
					stopwatch.Stop();
					Console.WriteLine("excel解析时长: {0}ms", stopwatch.ElapsedMilliseconds);
					return result;
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				MessageBox.Show("导入文件错误信息：" + ex.Message);
			}
			return result;
		}

		public static MessageInfo<T> GetmessageInfoV2<T>(WorksheetCollection worksheets, int startSheet, int titleRow, string businessId, bool readAll, Func<T, int, string> CheckAction = null)
		{
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			int count = worksheets.Count;
			bool existError = false;
			int num = 0;
			ExportExcelWayAttribute exportExcelWayAttribute = (ExportExcelWayAttribute)(typeof(T).GetCustomAttributes(typeof(ExportExcelWayAttribute), inherit: true)?.Where((Func<object, bool>)((object p) => ((ExportExcelWayAttribute)p).BusinessDiff == businessId)).FirstOrDefault());
			exportExcelWayAttribute = exportExcelWayAttribute ?? new ExportExcelWayAttribute();
			bool flag = exportExcelWayAttribute.ExportExcelWayEnum == ExportExcelWayEnum.ByIndex;
			StringBuilder stringBuilder = new StringBuilder();
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			List<T> list = new List<T>();
			Type typeFromHandle = typeof(T);
			MemberInfo[] allMembers = SQLiteSqlUtils.GetAllMembers(typeFromHandle);
			Dictionary<string, PropRuleVo> dictionary = new Dictionary<string, PropRuleVo>();
			Dictionary<int, PropRuleVo> dictionary2 = new Dictionary<int, PropRuleVo>();
			MemberInfo[] array = allMembers;
			foreach (MemberInfo memberInfo in array)
			{
				ExportColumnAttribute[] array2 = (ExportColumnAttribute[])memberInfo.GetCustomAttributes(typeof(ExportColumnAttribute), inherit: true);
				if (array2 != null && array2.Length != 0)
				{
					ExportColumnAttribute[] array3 = array2;
					foreach (ExportColumnAttribute exportColumnAttribute in array3)
					{
						if (exportColumnAttribute.BusinessDiff == null || businessId == null || (exportColumnAttribute.BusinessDiff.Contains(businessId) && exportColumnAttribute.Title != null))
						{
							if (!flag)
							{
								dictionary[exportColumnAttribute.Title.Trim()] = new PropRuleVo(exportColumnAttribute, typeFromHandle.GetProperty(UpperCaseFirst(memberInfo.Name)), memberInfo.GetAttribute<FormatAttribute>());
							}
							else
							{
								dictionary2[exportColumnAttribute.Index] = new PropRuleVo(exportColumnAttribute, typeFromHandle.GetProperty(UpperCaseFirst(memberInfo.Name)), memberInfo.GetAttribute<FormatAttribute>());
							}
						}
					}
				}
				SubExportColumnAttribute[] array4 = (SubExportColumnAttribute[])memberInfo.GetCustomAttributes(typeof(SubExportColumnAttribute), inherit: true);
				CellValueConvertAttribute customAttribute = memberInfo.GetCustomAttribute<CellValueConvertAttribute>();
				if (array4 == null || array4.Length == 0)
				{
					continue;
				}
				CellValueConvert cellValueConvert = null;
				if (customAttribute != null)
				{
					cellValueConvert = (CellValueConvert)Activator.CreateInstance(customAttribute?.Convert);
				}
				SubExportColumnAttribute[] array5 = array4;
				foreach (SubExportColumnAttribute subExportColumnAttribute in array5)
				{
					string[] titles = subExportColumnAttribute.Titles;
					string[] array6 = titles;
					foreach (string text in array6)
					{
						dictionary[text] = new PropRuleVo(new ExportColumnAttribute(text)
						{
							Nullable = subExportColumnAttribute.Nullable,
							CellValueConvert = cellValueConvert
						}, typeFromHandle.GetProperty(UpperCaseFirst(memberInfo.Name)), memberInfo.GetAttribute<FormatAttribute>());
					}
				}
			}
			bool flag2 = false;
			for (int m = 0; m < count; m++)
			{
				if (!flag)
				{
					dictionary2.Clear();
				}
				if (m != startSheet)
				{
					continue;
				}
				if (m != startSheet && !readAll)
				{
					break;
				}
				int num2 = 0;
				Cells cells = worksheets[m].Cells;
				RowCollection rows = cells.Rows;
				int num3 = 0;
				for (int n = 0; n < rows.Count; n++)
				{
					num2++;
					T val = Activator.CreateInstance<T>();
					Cell firstCell = rows[n].FirstCell;
					Cell lastCell = rows[n].LastCell;
					if (n == titleRow)
					{
						num3 = lastCell.Column;
						for (int num4 = firstCell.Column; num4 <= lastCell.Column; num4++)
						{
							if (dictionary.ContainsKey(cells[n, num4].Value?.ToString().Trim() ?? "#@#"))
							{
								dictionary2[num4] = dictionary[cells[n, num4].Value?.ToString().Trim()];
							}
						}
					}
					if (n <= titleRow)
					{
						continue;
					}
					if (firstCell == null && lastCell == null)
					{
						break;
					}
					Cell cell = cells[n, (lastCell.Column + firstCell.Column) / 2];
					if (ObjectUtil.IsEmptyObj(firstCell.Value) && ObjectUtil.IsEmptyObj(lastCell.Value) && lastCell.Column - firstCell.Column <= 2 && ObjectUtil.IsEmptyObj(cell.Value))
					{
						break;
					}
					num3 = Math.Max(num3, lastCell.Column);
					for (int num5 = firstCell.Column; num5 <= num3; num5++)
					{
						if (!dictionary2.ContainsKey(num5))
						{
							continue;
						}
						PropRuleVo propRuleVo = dictionary2[num5];
						Cell cell2 = cells[n, num5];
						object obj = null;
						if (cell2.Type == CellValueType.IsDateTime && propRuleVo.FormatAttribute?.Fmt != null)
						{
							cells[n, num5].Value = cells[n, num5].DateTimeValue.ToString(propRuleVo.FormatAttribute.Fmt);
						}
						try
						{
							if (cells[n, num5].Value == null)
							{
								obj = null;
							}
							else if (propRuleVo.PropertyInfo.ToString().Contains("System.Nullable"))
							{
								obj = Convert.ChangeType(cells[n, num5].Value, Nullable.GetUnderlyingType(propRuleVo.PropertyInfo.PropertyType));
							}
							else
							{
								if (propRuleVo?.ExportColumnAttribute?.CellValueConvert != null)
								{
									propRuleVo.ExportColumnAttribute.CellValueConvert.Convert(val, propRuleVo.ExportColumnAttribute.Title, cells[n, num5].IsErrorValue ? null : cells[n, num5].Value);
									continue;
								}
								obj = Convert.ChangeType(cells[n, num5].Value, propRuleVo.PropertyInfo.PropertyType);
							}
						}
						catch (Exception)
						{
							existError = true;
							stringBuilder.Append($"第{num2}行，{dictionary2[num5].ExportColumnAttribute.Title} 数据格式不正确\n");
						}
						if (!"Id".Equals(propRuleVo.PropertyInfo.Name) && num5 == lastCell.Column - 1)
						{
							typeFromHandle.GetProperty("Id", bindingAttr)?.SetValue(val, Guid.NewGuid().ToString("N"), null);
						}
						if (!propRuleVo.ExportColumnAttribute.Nullable && (obj == null || obj.ToString().Trim().Length <= 0))
						{
							existError = true;
							stringBuilder.Append($"第{num2}行，{dictionary2[num5].ExportColumnAttribute.Title}数据不得为空\n");
							flag2 = true;
						}
						if (obj != null && propRuleVo.ExportColumnAttribute.MaxLength < obj.ToString().Length)
						{
							existError = true;
							stringBuilder.Append($"第{num2}行，{dictionary2[num5].ExportColumnAttribute.Title} 数据长度不得超过{propRuleVo.ExportColumnAttribute.MaxLength}个字符\n");
							flag2 = true;
						}
						if (obj != null && !string.IsNullOrEmpty(propRuleVo.ExportColumnAttribute.Pattern) && !Regex.IsMatch(obj.ToString().Trim(), propRuleVo.ExportColumnAttribute.Pattern))
						{
							existError = true;
							stringBuilder.Append($"第{num2}行，{dictionary2[num5].ExportColumnAttribute.Title} 数据格式不正确\n");
							flag2 = true;
						}
						propRuleVo.PropertyInfo.SetValue(val, obj, null);
					}
					if (flag2)
					{
						flag2 = false;
						num++;
					}
					string value;
					if (!string.IsNullOrEmpty(value = CheckAction?.Invoke(val, num2)))
					{
						stringBuilder.AppendLine(value);
					}
					list.Add(val);
					if (list != null && list.Count > 120000)
					{
						throw new Exception($"当前excel中某个sheet数据条数超过{120000}万行，如有需要请分批导入");
					}
				}
			}
			messageInfo.Record = list;
			messageInfo.ErrorInfo = stringBuilder.ToString();
			messageInfo.TotalCount = list.Count;
			messageInfo.ErrorCount = num;
			messageInfo.SuccessCount = messageInfo.TotalCount - num;
			messageInfo.ExistError = existError;
			if (list.Count <= 0)
			{
				messageInfo.ExistError = true;
				messageInfo.ErrorInfo = "未填写数据，至少填写一条数据";
				messageInfo.NoData = true;
			}
			return messageInfo;
		}
	}
	public class ExportColumnInfo<TEntity>
	{
		public float OrderValue { get; set; }

		public Func<TEntity, object> FuncObj { get; set; }

		public string ColumnName { get; set; }

		public Dictionary<string, string> ComboBoxItemDict { get; set; }

		public string ActualPropName { get; set; }
	}
	public class ExportColumnHelper
	{
		public static Dictionary<string, string> GetExportColumns<TEntity>(string businessId)
		{
			Dictionary<string, ExportColumnInfo<TEntity>> dictionary = new Dictionary<string, ExportColumnInfo<TEntity>>();
			Type typeFromHandle = typeof(TEntity);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] members = typeFromHandle.GetMembers(bindingAttr);
			MemberInfo[] array = members;
			foreach (MemberInfo memberInfo in array)
			{
				object[] customAttributes = memberInfo.GetCustomAttributes(typeof(ExportColumnAttribute), inherit: true);
				object[] array2 = customAttributes;
				for (int j = 0; j < array2.Length; j++)
				{
					Attribute attribute = (Attribute)array2[j];
					if (attribute is ExportColumnAttribute { Visible: not false } exportColumnAttribute && PatternContent(businessId, exportColumnAttribute.BusinessDiff))
					{
						dictionary[exportColumnAttribute.Title] = new ExportColumnInfo<TEntity>
						{
							OrderValue = exportColumnAttribute.OrderValue,
							ColumnName = memberInfo.GetAttribute<TableFieldAttribute>().ColumName
						};
					}
				}
			}
			return dictionary.OrderBy<KeyValuePair<string, ExportColumnInfo<TEntity>>, float>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value.OrderValue).ToDictionary<KeyValuePair<string, ExportColumnInfo<TEntity>>, string, string>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Key, (KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value.ColumnName);
		}

		public static Dictionary<string, ExportColumnInfo<TEntity>> GetExportPropColumns<TEntity>(string businessId)
		{
			Dictionary<string, ExportColumnInfo<TEntity>> dictionary = new Dictionary<string, ExportColumnInfo<TEntity>>();
			Type typeFromHandle = typeof(TEntity);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] members = typeFromHandle.GetMembers(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			Dictionary<string, PropertyInfo> propDict = new Dictionary<string, PropertyInfo>();
			PropertyInfo[] array = properties;
			foreach (PropertyInfo propertyInfo in array)
			{
				propDict[propertyInfo.Name.ToLower()] = propertyInfo;
			}
			MemberInfo[] array2 = members;
			foreach (MemberInfo memberInfo in array2)
			{
				object[] customAttributes = memberInfo.GetCustomAttributes(typeof(ExportColumnAttribute), inherit: true);
				object[] array3 = customAttributes;
				for (int k = 0; k < array3.Length; k++)
				{
					Attribute attribute = (Attribute)array3[k];
					if (!(attribute is ExportColumnAttribute { Visible: not false } exportColumnAttribute) || !PatternContent(businessId, exportColumnAttribute.BusinessDiff))
					{
						continue;
					}
					string lowerPropName = memberInfo.Name.ToLower();
					Func<TEntity, object> val = (TEntity obj) => propDict.ContainsKey(lowerPropName) ? propDict[lowerPropName].GetValue(obj, null) : null;
					if (val != null)
					{
						if (propDict[lowerPropName].PropertyType == typeof(DateTime))
						{
							val = (TEntity obj) => ((DateTime)propDict[lowerPropName].GetValue(obj, null)).ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "");
						}
						else if (propDict[lowerPropName].PropertyType == typeof(DateTime?))
						{
							val = (TEntity obj) => ((DateTime?)propDict[lowerPropName].GetValue(obj, null))?.ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "");
						}
					}
					dictionary[exportColumnAttribute.Title] = new ExportColumnInfo<TEntity>
					{
						FuncObj = val,
						ComboBoxItemDict = exportColumnAttribute.ComboBoxItemDict,
						OrderValue = exportColumnAttribute.OrderValue,
						ActualPropName = exportColumnAttribute.ActualPropName
					};
				}
			}
			return dictionary.OrderBy<KeyValuePair<string, ExportColumnInfo<TEntity>>, float>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value.OrderValue).ToDictionary<KeyValuePair<string, ExportColumnInfo<TEntity>>, string, ExportColumnInfo<TEntity>>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Key, (KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value);
		}

		public static Dictionary<string, ExportColumnInfo<TEntity>> GetExportPropColumns<TEntity>(TEntity tEntity, string businessId)
		{
			Dictionary<string, ExportColumnInfo<TEntity>> dictionary = new Dictionary<string, ExportColumnInfo<TEntity>>();
			Type type = tEntity.GetType();
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] members = type.GetMembers(bindingAttr);
			PropertyInfo[] properties = type.GetProperties();
			Dictionary<string, PropertyInfo> propDict = new Dictionary<string, PropertyInfo>();
			PropertyInfo[] array = properties;
			foreach (PropertyInfo propertyInfo in array)
			{
				propDict[propertyInfo.Name.ToLower()] = propertyInfo;
			}
			MemberInfo[] array2 = members;
			foreach (MemberInfo memberInfo in array2)
			{
				object[] customAttributes = memberInfo.GetCustomAttributes(typeof(ExportColumnAttribute), inherit: true);
				object[] array3 = customAttributes;
				for (int k = 0; k < array3.Length; k++)
				{
					Attribute attribute = (Attribute)array3[k];
					if (!(attribute is ExportColumnAttribute { Visible: not false } exportColumnAttribute) || !PatternContent(businessId, exportColumnAttribute.BusinessDiff))
					{
						continue;
					}
					string lowerPropName = memberInfo.Name.ToLower();
					Func<TEntity, object> val = (TEntity obj) => propDict.ContainsKey(lowerPropName) ? propDict[lowerPropName].GetValue(obj, null) : null;
					if (val != null)
					{
						if (propDict[lowerPropName].PropertyType == typeof(DateTime))
						{
							val = (TEntity obj) => ((DateTime)propDict[lowerPropName].GetValue(obj, null)).ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "");
						}
						else if (propDict[lowerPropName].PropertyType == typeof(DateTime?))
						{
							val = (TEntity obj) => ((DateTime?)propDict[lowerPropName].GetValue(obj, null))?.ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "");
						}
					}
					dictionary[exportColumnAttribute.Title] = new ExportColumnInfo<TEntity>
					{
						FuncObj = val,
						ComboBoxItemDict = exportColumnAttribute.ComboBoxItemDict,
						OrderValue = exportColumnAttribute.OrderValue
					};
				}
			}
			return dictionary.OrderBy<KeyValuePair<string, ExportColumnInfo<TEntity>>, float>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value.OrderValue).ToDictionary<KeyValuePair<string, ExportColumnInfo<TEntity>>, string, ExportColumnInfo<TEntity>>((KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Key, (KeyValuePair<string, ExportColumnInfo<TEntity>> p) => p.Value);
		}

		private static bool PatternContent(string value, string[] businessIDs)
		{
			if (businessIDs == null || businessIDs.Length == 0)
			{
				return true;
			}
			bool flag = false;
			foreach (string text in businessIDs)
			{
				flag = (text.EndsWith("%") ? RegexHelper.IsMath(value, text.Replace("%", "")) : (text == value));
				if (flag)
				{
					return flag;
				}
			}
			return flag;
		}
	}
	public static class ExpressionHelper
	{
		public static BinaryExpression CreateCompareExpression<Element>(string propName, object equalPropValue, ref ParameterExpression parameterExpression, Func<Expression, Expression, BinaryExpression> compareExpressionCall = null)
		{
			parameterExpression = parameterExpression ?? Expression.Parameter(typeof(Element), "v");
			MemberExpression memberExpression = Expression.Property(parameterExpression, propName);
			ConstantExpression constantExpression = Expression.Constant(equalPropValue);
			return (compareExpressionCall == null) ? Expression.Equal(memberExpression, constantExpression) : compareExpressionCall.Invoke((Expression)memberExpression, (Expression)constantExpression);
		}
	}
	public class FileUtils
	{
		public static void UploadFile(string fileNamePath, string uriString, bool isAutoRename)
		{
			UploadFileMethod(fileNamePath, uriString, isAutoRename);
		}

		private static void UploadFileMethod(string fileNamePath, string uriString, bool isAutoRename)
		{
			string fileName = Path.GetFileName(fileNamePath);
			string text = fileName;
			if (isAutoRename)
			{
				text = DateTime.Now.ToString("yyyyMMddHHmmss") + fileName;
			}
			if (!uriString.EndsWith("/"))
			{
				uriString += "/";
			}
			string path = uriString.Substring(0, uriString.LastIndexOf("/"));
			DirectoryInfo directoryInfo = new DirectoryInfo(path);
			if (!directoryInfo.Exists)
			{
				directoryInfo.Create();
			}
			uriString += text;
			WebClient webClient = new WebClient();
			webClient.Credentials = CredentialCache.DefaultCredentials;
			FileStream fileStream = new FileStream(fileNamePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
			BinaryReader binaryReader = new BinaryReader(fileStream);
			byte[] array = binaryReader.ReadBytes((int)fileStream.Length);
			Stream stream = webClient.OpenWrite(uriString, "PUT");
			try
			{
				if (stream.CanWrite)
				{
					stream.Write(array, 0, array.Length);
					stream.Close();
					fileStream.Dispose();
					Console.WriteLine("----文件上传成功----");
				}
				else
				{
					stream.Close();
					fileStream.Dispose();
					Console.WriteLine("----失败了----");
				}
			}
			catch (Exception ex)
			{
				stream.Close();
				fileStream.Dispose();
				Console.WriteLine("----文件上传失败-----");
				throw ex;
			}
			finally
			{
				stream.Close();
				fileStream.Dispose();
			}
		}

		public static void FileDownLoad(string filePath, string fileName, string preMessage)
		{
			if (preMessage == null || preMessage.Equals(""))
			{
				preMessage = "文件下载";
			}
			try
			{
				WebRequest webRequest = WebRequest.Create(filePath);
				WebResponse response = webRequest.GetResponse();
				Stream responseStream = response.GetResponseStream();
				FileStream fileStream = new FileStream(fileName, FileMode.OpenOrCreate);
				byte[] array = new byte[1024];
				int num;
				do
				{
					num = responseStream.Read(array, 0, array.Length);
					if (num > 0)
					{
						fileStream.Write(array, 0, num);
					}
				}
				while (num > 0);
				fileStream.Flush();
				fileStream.Close();
				responseStream.Flush();
				responseStream.Close();
				MessageBox.Show(preMessage + "成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
			catch (Exception ex)
			{
				MessageBox.Show(preMessage + "异常：" + ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
			}
		}

		public static string RemoveIllegalCharacter(string originalPath, string replacement = "_")
		{
			if (string.IsNullOrEmpty(originalPath))
			{
				return originalPath;
			}
			string pattern = "[\\\\/:*?\"<>|]";
			return Regex.Replace(originalPath, pattern, replacement);
		}
	}
	public static class GlobalConfig
	{
		public static XmlConfiguration _XmlConfiguration;

		public static XmlConfiguration GetXmlConfiguration()
		{
			if (_XmlConfiguration == null)
			{
				XmlSerializer xmlSerializer = new XmlSerializer(typeof(XmlConfiguration));
				using FileStream stream = new FileStream("myconfig.xml", FileMode.Open);
				_XmlConfiguration = xmlSerializer.Deserialize(stream) as XmlConfiguration;
			}
			return _XmlConfiguration;
		}
	}
	public class HashHelper
	{
		public static string GetHash(string path)
		{
			SHA1 sHA = SHA1.Create();
			FileStream fileStream = new FileStream(path, FileMode.Open);
			byte[] array = sHA.ComputeHash(fileStream);
			fileStream.Close();
			return BitConverter.ToString(array).Replace("-", "");
		}

		public static string GetHash(byte[] streamArray)
		{
			SHA1 sHA = SHA1.Create();
			byte[] array = sHA.ComputeHash(streamArray);
			return BitConverter.ToString(array).Replace("-", "");
		}
	}
	public class IconHelper
	{
		private static readonly string IconRelatePath = "images\\ui\\logo64.ico";

		private static Icon icon = null;

		public static Icon GetIcon()
		{
			return GetIcon(IconRelatePath);
		}

		public static Icon GetIcon(string iconRelatePath)
		{
			if (icon != null)
			{
				return icon;
			}
			string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, iconRelatePath);
			return icon = Icon.ExtractAssociatedIcon(filePath);
		}
	}
	public class Base64Helper
	{
		public static string CovertToFile(string base64File, string imgName, out string fullPath)
		{
			string text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "fujian");
			string text2 = Path.Combine(text, imgName);
			byte[] array = Convert.FromBase64String(base64File);
			using (FileStream output = new FileStream(text2, FileMode.CreateNew, FileAccess.Write))
			{
				using BinaryWriter binaryWriter = new BinaryWriter(output);
				binaryWriter.Write(array, 0, array.Length);
			}
			fullPath = text2;
			return fullPath.Replace(text, "");
		}

		public static string FileToBase64(string file)
		{
			string result = null;
			using (FileStream fileStream = new FileStream(file, FileMode.Open, FileAccess.Read))
			{
				byte[] array = new byte[fileStream.Length];
				fileStream.Read(array, 0, array.Length);
				result = Convert.ToBase64String(array);
			}
			return result;
		}
	}
	public class KenFileUtil
	{
		public static string getFileCode(string filePath)
		{
			string text = "-- file is empty--";
			FileStream fileStream;
			using (fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
			{
				BinaryReader binaryReader;
				using (binaryReader = new BinaryReader(fileStream))
				{
					try
					{
						if (fileStream.Length > 0)
						{
							text = binaryReader.ReadByte().ToString();
							text += binaryReader.ReadByte();
						}
					}
					catch (Exception)
					{
						throw;
					}
				}
			}
			return text;
		}

		public static bool IsPDF(string filePath)
		{
			return "3780" == getFileCode(filePath);
		}

		public static bool IsTxt(string filePath)
		{
			return "210187" == getFileCode(filePath);
		}

		public static bool IsWord(string filePath)
		{
			return "208207" == getFileCode(filePath) || "8075" == getFileCode(filePath);
		}

		public static bool IsExcel(string filePath)
		{
			return "208207" == getFileCode(filePath) || "8075" == getFileCode(filePath);
		}

		public static bool IsImage(string filePath)
		{
			return "255216" == getFileCode(filePath) || "7173" == getFileCode(filePath) || "6677" == getFileCode(filePath) || "13780" == getFileCode(filePath);
		}
	}
	public class LocalFileDownLoad
	{
		private static string sqliteDbName = ConfigHelper.GetValueByKey("sqliteDBName");

		private static string sqliteDbLocation = ConfigHelper.GetValueByKey("sqliteDBLocation");

		public static void downLoadFile(string fileName)
		{
			downLoadFile(fileName, null);
		}

		public static void downLoadFile(string fileName, string tempPath)
		{
			tempPath = ((tempPath == null || tempPath == "") ? (AppDomain.CurrentDomain.BaseDirectory + "templates\\export\\") : tempPath);
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = DateTime.Now.ToString("yyyyMMddhhmmss") + fileName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName2 = saveFileDialog.FileName;
				FileUtils.FileDownLoad(tempPath + fileName, fileName2, "下载");
				Process.Start(fileName2);
			}
		}

		public static void downLoadAttachFile(string attachId, string tempPath)
		{
			string selectSql = "select * from SYS_FILE_INFO where id='" + attachId + "'";
			DataTable dt = SQLiteLibrary.SelectDataBySql(sqliteDbLocation, sqliteDbName, selectSql);
			List<SysFileInfo> list = DataTableUtils.ToList<SysFileInfo>(dt);
			if (list.Count > 0)
			{
				tempPath = ((tempPath == null || tempPath == "") ? (AppDomain.CurrentDomain.BaseDirectory + "fujian\\") : tempPath);
				string filePath = tempPath + list[0].FilePath;
				SaveFileDialog saveFileDialog = new SaveFileDialog();
				saveFileDialog.FileName = DateTime.Now.ToString("yyyyMMddhhmmss") + list[0].FileDispName;
				saveFileDialog.AddExtension = false;
				saveFileDialog.RestoreDirectory = true;
				saveFileDialog.Title = "文件保存路径";
				if (saveFileDialog.ShowDialog() == DialogResult.OK)
				{
					string fileName = saveFileDialog.FileName;
					FileUtils.FileDownLoad(filePath, fileName, "下载");
				}
			}
		}

		public static void downLoadAttach(string fileOldName, string relPath)
		{
			string filePath = AppDomain.CurrentDomain.BaseDirectory + "templates\\export\\" + relPath;
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = DateTime.Now.ToString("yyyyMMddhhmmss") + fileOldName;
			saveFileDialog.AddExtension = false;
			saveFileDialog.RestoreDirectory = true;
			saveFileDialog.Title = "文件保存路径";
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				string fileName = saveFileDialog.FileName;
				FileUtils.FileDownLoad(filePath, fileName, "下载");
			}
		}

		public static void downLoadFile2(string filePath, string fileDispName)
		{
			FileStream fileStream = null;
			try
			{
				SaveFileDialog saveFileDialog = new SaveFileDialog();
				saveFileDialog.FileName = DateTime.Now.ToString("yyyyMMddhhmmss") + fileDispName;
				saveFileDialog.AddExtension = false;
				saveFileDialog.RestoreDirectory = true;
				saveFileDialog.Title = "文件保存路径";
				if (saveFileDialog.ShowDialog() == DialogResult.OK)
				{
					byte[] array = File.ReadAllBytes(filePath);
					fileStream = new FileStream(saveFileDialog.FileName, FileMode.Create);
					fileStream.Write(array, 0, array.Length);
					fileStream.Flush();
					MessageBox.Show("下载成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
					Process.Start(saveFileDialog.FileName);
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
			finally
			{
				fileStream?.Close();
			}
		}
	}
	public class LocalFileUtil
	{
		public static string GetFuJianPath(string realPath)
		{
			string text = AppDomain.CurrentDomain.BaseDirectory + "fujian\\";
			return text + realPath;
		}

		public static string GetTemplateFilePath(string realPath)
		{
			return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "templates", realPath);
		}

		public static string GetBidAttachPath(string realPath)
		{
			return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, realPath);
		}
	}
	public static class LogFileType
	{
		public const string Normal = "Logs/operate.log";

		public const string Operate_Error = "Logs/operate_error.log";
	}
	public class LogsHelper
	{
		public static bool ConsoleWrite;

		public static string rootPath;

		private static Logger logger;

		static LogsHelper()
		{
			ConsoleWrite = Convert.ToBoolean(ConfigHelper.GetValueByKey("ConsoleWrite") ?? "false");
			rootPath = AppDomain.CurrentDomain.BaseDirectory;
			LoggingConfiguration loggingConfiguration = new LoggingConfiguration();
			FileTarget fileTarget = new FileTarget("logfile")
			{
				FileName = "Logs/operate_${shortdate}.log"
			};
			fileTarget.CleanupInitializedFiles(TimeSource.Current.Time.AddDays(-7.0));
			loggingConfiguration.AddRule(LogLevel.Info, LogLevel.Info, fileTarget);
			FileTarget fileTarget2 = new FileTarget("errorfile")
			{
				FileName = "Logs/operate_error_${shortdate}.log"
			};
			fileTarget2.CleanupInitializedFiles(TimeSource.Current.Time.AddDays(-7.0));
			loggingConfiguration.AddRule(LogLevel.Error, LogLevel.Error, fileTarget2);
			FileTarget fileTarget3 = new FileTarget("trackfile")
			{
				FileName = "Logs/operate_error_${shortdate}.log"
			};
			fileTarget3.CleanupInitializedFiles(TimeSource.Current.Time.AddDays(-7.0));
			loggingConfiguration.AddRule(LogLevel.Trace, LogLevel.Trace, fileTarget3);
			ConsoleTarget target = new ConsoleTarget("debugLogfile");
			loggingConfiguration.AddRule(LogLevel.Debug, LogLevel.Debug, target);
			LogManager.Configuration = loggingConfiguration;
			logger = LogManager.GetCurrentClassLogger();
		}

		public static void AddLog(string str, string name = "管理员")
		{
			InsertLog(str, name, "Logs/operate.log");
		}

		public static void AddErrorLog(Exception ex, string extraMsg = null)
		{
			if (extraMsg != null)
			{
				extraMsg = "  备注:" + extraMsg + "\r\n";
			}
			logger.Trace(ex, extraMsg + "  message:" + ex.Message);
		}

		public static void AsynAddErrorLog(string str, string name = "管理员")
		{
			//IL_001c: Unknown result type (might be due to invalid IL or missing references)
			//IL_0022: Expected O, but got Unknown
			Action val = (Action)delegate
			{
				logger.Error("操作人:" + name + ",错误信息:" + str);
			};
			val.BeginInvoke((AsyncCallback)null, (object)null);
		}

		public static void InsertLog(string str, string name, string writeFileName)
		{
			if (ConsoleWrite)
			{
				logger.Debug(str);
			}
			name = (PublicVo.EntName ?? LocalCache.SysUserInfo?.UserName) ?? name;
			logger.Info(" 操作人:{0},错误:{1}", name, str);
		}

		public static void AddErrorLog(string message)
		{
			logger.Error("操作人:" + LocalCache.SysUserInfo?.UserName + ",错误信息:" + message);
		}

		public static void Debug(string message)
		{
			logger.Debug(message);
		}
	}
	public class MacAddressHelper
	{
		public static string GetMacByIpConfig()
		{
			List<string> list = new List<string>();
			string text = ExecuteInCmd("chcp 437&&ipconfig/all");
			foreach (string item in ((IEnumerable<string>)text.Split(Environment.NewLine.ToCharArray(), StringSplitOptions.RemoveEmptyEntries)).Select<string, string>((Func<string, string>)((string l) => l.Trim())))
			{
				if (!string.IsNullOrEmpty(item))
				{
					if (item.StartsWith("Physical Address"))
					{
						list.Add(item.Substring(36));
					}
					else if (item.StartsWith("DNS Servers") && item.Length > 36 && item.Substring(36).Contains("::"))
					{
						list.Clear();
					}
					else if (list.Count > 0 && item.StartsWith("NetBIOS") && item.Contains("Enabled"))
					{
						return list.Last();
					}
				}
			}
			return list.FirstOrDefault();
		}

		public static string GetMacByWmi()
		{
			try
			{
				string text = GetMacByNetworkInterface();
				if (string.IsNullOrWhiteSpace(text))
				{
					ManagementClass managementClass = new ManagementClass("Win32_NetworkAdapterConfiguration");
					ManagementObjectCollection instances = managementClass.GetInstances();
					foreach (ManagementObject item in instances)
					{
						if ((bool)item["IPEnabled"])
						{
							text = item["MacAddress"].ToString();
						}
					}
				}
				return text;
			}
			catch (Exception)
			{
				return "";
			}
		}

		public static string GetMacByNetworkInterface()
		{
			string text = "SYSTEM\\CurrentControlSet\\Control\\Network\\{4D36E972-E325-11CE-BFC1-08002BE10318}\\";
			string text2 = string.Empty;
			try
			{
				NetworkInterface[] allNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
				NetworkInterface[] array = allNetworkInterfaces;
				foreach (NetworkInterface networkInterface in array)
				{
					if (networkInterface.NetworkInterfaceType != NetworkInterfaceType.Ethernet || networkInterface.GetPhysicalAddress().ToString().Length == 0)
					{
						continue;
					}
					string name = text + networkInterface.Id + "\\Connection";
					RegistryKey registryKey = Registry.LocalMachine.OpenSubKey(name, writable: false);
					if (registryKey == null)
					{
						continue;
					}
					string text3 = registryKey.GetValue("PnpInstanceID", "").ToString();
					int num = Convert.ToInt32(registryKey.GetValue("MediaSubType", 0));
					if (text3.Length > 3 && text3.Substring(0, 3) == "PCI")
					{
						text2 = networkInterface.GetPhysicalAddress().ToString();
						for (int j = 1; j < 6; j++)
						{
							text2 = text2.Insert(3 * j - 1, ":");
						}
						break;
					}
				}
			}
			catch (Exception)
			{
			}
			return text2;
		}

		public static string ExecuteInCmd(string cmdline)
		{
			using Process process = new Process();
			process.StartInfo.FileName = "cmd.exe";
			process.StartInfo.UseShellExecute = false;
			process.StartInfo.RedirectStandardInput = true;
			process.StartInfo.RedirectStandardOutput = true;
			process.StartInfo.RedirectStandardError = true;
			process.StartInfo.CreateNoWindow = true;
			process.Start();
			process.StandardInput.AutoFlush = true;
			process.StandardInput.WriteLine(cmdline + "&exit");
			string result = process.StandardOutput.ReadToEnd();
			process.WaitForExit();
			process.Close();
			return result;
		}
	}
	public class Md5Util
	{
		public static string Md5(string value)
		{
			string result = string.Empty;
			if (string.IsNullOrEmpty(value))
			{
				return result;
			}
			using (MD5 md5Hash = MD5.Create())
			{
				result = GetMd5Hash(md5Hash, value);
			}
			return result;
		}

		private static string GetMd5Hash(MD5 md5Hash, string input)
		{
			byte[] array = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(input));
			StringBuilder stringBuilder = new StringBuilder();
			byte[] array2 = array;
			foreach (byte b in array2)
			{
				stringBuilder.Append(b.ToString("x2"));
			}
			return stringBuilder.ToString();
		}

		private static bool VerifyMd5Hash(MD5 md5Hash, string input, string hash)
		{
			string md5Hash2 = GetMd5Hash(md5Hash, input);
			StringComparer ordinalIgnoreCase = StringComparer.OrdinalIgnoreCase;
			return ordinalIgnoreCase.Compare(md5Hash2, hash) == 0;
		}
	}
	public class MessageHelper
	{
		public static DialogResult ShowError(Exception ex, string title = "警告")
		{
			LogsHelper.AddErrorLog(ex);
			return ShowError(ex.Message, title);
		}

		public static DialogResult ShowWarn(string msg, string title = "提示", MessageBoxButtons boxButton = MessageBoxButtons.OK)
		{
			return MessageBox.Show(msg, title, boxButton, MessageBoxIcon.Exclamation);
		}

		public static DialogResult ShowInfo(string msg, string title = "提示", MessageBoxButtons boxButton = MessageBoxButtons.OK)
		{
			return MessageBox.Show(msg, title, boxButton, MessageBoxIcon.Asterisk);
		}

		public static DialogResult ShowError(string msg, string title = "警告")
		{
			return MessageBox.Show(msg, title ?? "警告", MessageBoxButtons.OK, MessageBoxIcon.Hand);
		}

		public static DialogResult ShowQuery(string msg, string title = "提示", MessageBoxButtons boxButton = MessageBoxButtons.YesNo)
		{
			return MessageBox.Show(msg, title, boxButton, MessageBoxIcon.Question);
		}
	}
	public class ObjectHelper
	{
		public static bool IsEmpty<T>(List<T> list)
		{
			return list == null || list.Count <= 0;
		}

		public static void CheckNull(int rowIndex, DataRow dt, Dictionary<string, string> dict, ref StringBuilder sbf, Action<int, string, string, object> rowVerify)
		{
			DataColumnCollection columns = dt.Table.Columns;
			if (sbf == null)
			{
				sbf = new StringBuilder();
			}
			string text = ((rowIndex >= 0) ? $"第{rowIndex + 1}行," : "");
			foreach (KeyValuePair<string, string> item in dict)
			{
				object obj = dt[item.Value];
				if (obj == null || string.IsNullOrWhiteSpace(obj.ToString()))
				{
					sbf.Append(text + item.Key + "不得为空\r\n");
				}
				else
				{
					rowVerify?.Invoke(rowIndex, item.Key, item.Value, obj);
				}
			}
		}
	}
	public class ObjectUtil
	{
		public static void CopyPop<A, B>(B b, ref A a, string[] ignores)
		{
			List<string> list = new List<string>();
			if (ignores != null && ignores.Length != 0)
			{
				list = new List<string>(ignores);
			}
			try
			{
				Type type = b.GetType();
				Type typeFromHandle = typeof(A);
				Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
				object obj = null;
				PropertyInfo[] properties = type.GetProperties();
				foreach (PropertyInfo propertyInfo in properties)
				{
					if (!dictionary.ContainsKey(propertyInfo.Name))
					{
						dictionary.Add(propertyInfo.Name, propertyInfo);
					}
				}
				PropertyInfo[] properties2 = typeFromHandle.GetProperties();
				foreach (PropertyInfo propertyInfo2 in properties2)
				{
					if (dictionary.ContainsKey(propertyInfo2.Name) && !list.Contains(FirstToLower(propertyInfo2.Name)))
					{
						obj = dictionary[propertyInfo2.Name].GetValue(b, null);
						if (obj != null)
						{
							propertyInfo2.SetValue(a, dictionary[propertyInfo2.Name].GetValue(b, null), null);
						}
					}
				}
			}
			catch (Exception ex)
			{
				throw ex;
			}
		}

		public static void CopyPop<A, B>(B b, ref A a)
		{
			CopyPop(b, ref a, null);
		}

		public static string FirstToLower(string input)
		{
			if (string.IsNullOrEmpty(input))
			{
				return input;
			}
			return input.Substring(0, 1).ToLower() + input.Substring(1);
		}

		public static bool IsEmpty<T>(List<T> list)
		{
			return list == null || list.Count <= 0;
		}

		public static bool IsEmptyObj(object obj)
		{
			return obj == null || string.IsNullOrWhiteSpace(obj.ToString());
		}

		public static void RequireNotNull(object value, string propName)
		{
			if (value == null)
			{
				throw new ArgumentNullException(propName);
			}
		}
	}
	public class OpenWorkBook
	{
		private readonly Workbook workBook = null;

		public Workbook OpenByDialog()
		{
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "文件|*.xlsx;*.xlsx";
			openFileDialog.FileName = "";
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				return new Workbook(openFileDialog.FileName);
			}
			return null;
		}
	}
	public static class PageComboboxHelper
	{
		public static List<ComboBoxPageVo> DefaultComboBox;

		private static object lockObj;

		public const int DefaultSelectedIndex = 2;

		static PageComboboxHelper()
		{
			DefaultComboBox = null;
			lockObj = new object();
			lock (lockObj)
			{
				if (DefaultComboBox == null)
				{
					DefaultComboBox = new List<ComboBoxPageVo>();
				}
				ComboBoxPageVo item = new ComboBoxPageVo
				{
					DisplayName = "每页50条",
					PageSize = 50,
					Selected = false
				};
				ComboBoxPageVo item2 = new ComboBoxPageVo
				{
					DisplayName = "每页100条",
					PageSize = 100,
					Selected = false
				};
				ComboBoxPageVo item3 = new ComboBoxPageVo
				{
					DisplayName = "每页200条",
					PageSize = 200,
					Selected = true
				};
				ComboBoxPageVo item4 = new ComboBoxPageVo
				{
					DisplayName = "每页500条",
					PageSize = 500,
					Selected = false
				};
				DefaultComboBox.Add(item);
				DefaultComboBox.Add(item2);
				DefaultComboBox.Add(item3);
				DefaultComboBox.Add(item4);
			}
		}

		public static List<ComboBoxPageVo> CreateComboBox(int[] pageSizes)
		{
			List<ComboBoxPageVo> list = new List<ComboBoxPageVo>();
			foreach (int num in pageSizes)
			{
				list.Add(new ComboBoxPageVo
				{
					DisplayName = $"每页{num}条",
					PageSize = num,
					Selected = false
				});
			}
			return list;
		}
	}
	public class PrincipalHelper
	{
		public static void SetSupplier(OsZbSupplierInfo osZbSupplier)
		{
			PublicVo.Token = osZbSupplier.Id;
			PublicVo.SupplyId = osZbSupplier.Id;
			PublicVo.SupplyName = osZbSupplier.EntName;
			PublicVo.Account = osZbSupplier.LoginAccount;
			PublicVo.EntName = osZbSupplier.EntName;
			PublicVo.EntIntroduce = osZbSupplier.EntIntroduce;
			PublicVo.EntRegTime = osZbSupplier.EntRegTime;
			PublicVo.RegCapital = osZbSupplier.RegCapital;
			PublicVo.RegPlace = osZbSupplier.RegPlace;
			PublicVo.FacBuildPlace = osZbSupplier.FacBuildPlace;
			PublicVo.LegalRep = osZbSupplier.LegalRep;
			PublicVo.EntType = osZbSupplier.EntType;
			PublicVo.DeptType = osZbSupplier.DeptType;
			PublicVo.EntNature = osZbSupplier.EntNature;
			PublicVo.BusinessLicRegNo = osZbSupplier.BusinessLicRegNo;
			PublicVo.BusinessLicActiveType = osZbSupplier.BusinessLicActiveType;
			PublicVo.IndusRegNo = osZbSupplier.IndusRegNo;
			PublicVo.OrgNo = osZbSupplier.OrgNo;
			PublicVo.PostCode = osZbSupplier.PostCode;
			PublicVo.MailAddress = osZbSupplier.MailAddress;
			PublicVo.Status = osZbSupplier.Status;
			PublicVo.BusinessScope = osZbSupplier.BusinessScope;
			PublicVo.LoginAccount = osZbSupplier.LoginAccount;
			PublicVo.LoginPwd = osZbSupplier.LoginPwd;
			PublicVo.Id = osZbSupplier.Id;
			PublicVo.ThreeCertOneness = osZbSupplier.ThreeCertOneness;
			PublicVo.LoginPhone = osZbSupplier.LoginPhone;
			PublicVo.BankAccount = osZbSupplier.BankAccount;
			PublicVo.TechnologyLawCompany = osZbSupplier.TechnologyLawCompany;
		}
	}
	public class ReflectUtil
	{
		private static readonly Dictionary<string, string> fixColumnDict = new Dictionary<string, string>();

		public static string GetTableFixColumn(Type type, string columnPrefix, bool userTableName = false)
		{
			string key = type.Namespace + type.Name + "_fixedcolumns";
			TableNameAttribute tableNameAttribute = type.GetCustomAttribute<TableNameAttribute>() ?? new TableNameAttribute(type.Name);
			string value = tableNameAttribute.Value;
			if (!fixColumnDict.ContainsKey(key))
			{
				BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
				MemberInfo[] members = type.GetMembers(bindingAttr);
				StringBuilder stringBuilder = new StringBuilder();
				MemberInfo[] array = members;
				foreach (MemberInfo prop in array)
				{
					TableFieldAttribute attribute = prop.GetAttribute<TableFieldAttribute>();
					if (attribute != null && attribute.IsExist && (columnPrefix == null || attribute.ColumName.StartsWith(columnPrefix)))
					{
						stringBuilder.Append((userTableName ? (value + ".") : null) + attribute.ColumName + ",");
					}
				}
				fixColumnDict.Add(key, stringBuilder.ToString().TrimEnd(','));
			}
			return fixColumnDict[key];
		}

		public static string[] GetComboBoxItems<TEntity>(Expression<Func<TEntity, object>> func, string businessId = null) where TEntity : CommonEntity
		{
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] members = typeof(TEntity).GetMembers(bindingAttr);
			string lastExpression = SQLiteSqlUtils.GetLastExpression(func);
			MemberInfo[] array = members;
			foreach (MemberInfo memberInfo in array)
			{
				ExportColumnAttribute attribute = memberInfo.GetAttribute<ExportColumnAttribute>();
				if (attribute != null && memberInfo.Name.ToUpper() == lastExpression && (businessId == null || (attribute != null && attribute.BusinessDiff.Contains(businessId))))
				{
					return attribute.ComboBoxItems;
				}
			}
			return null;
		}

		public static Dictionary<string, PropertyInfo> GetPropDict(Type type, bool isExist = false)
		{
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] members = type.GetMembers(bindingAttr);
			PropertyInfo[] properties = type.GetProperties();
			Dictionary<string, PropertyInfo> dictionary2 = new Dictionary<string, PropertyInfo>();
			PropertyInfo[] array = properties;
			foreach (PropertyInfo propertyInfo in array)
			{
				dictionary2[propertyInfo.Name.ToLower()] = propertyInfo;
			}
			MemberInfo[] array2 = members;
			foreach (MemberInfo memberInfo in array2)
			{
				TableFieldAttribute attribute = memberInfo.GetAttribute<TableFieldAttribute>();
				if (attribute != null && (!isExist || !attribute.IsExist))
				{
					dictionary[memberInfo.Name.UpperCaseFirst()] = dictionary2[memberInfo.Name.ToLower()];
				}
			}
			return dictionary;
		}

		public static List<ModelTagAttribute> GetModelTagAttributes(string businessId)
		{
			ModelTagAttribute[] source = (ModelTagAttribute[])typeof(SysModelConfig).GetCustomAttributes(typeof(ModelTagAttribute), inherit: true);
			IEnumerable<ModelTagAttribute> enumerable = ((IEnumerable<ModelTagAttribute>)source).Where((Func<ModelTagAttribute, bool>)((ModelTagAttribute v) => businessId == null || v.DiffType == businessId || "*" == v.DiffType));
			return (enumerable != null && enumerable.Any()) ? enumerable.ToList() : new List<ModelTagAttribute>();
		}
	}
	public class RegexHelper
	{
		public const string PhoneAndFixedLine = "(^1[2-9][0-9]{9}$)|(^0[1-9]\\d{9}$)";

		public const string InputFloatNumber = "^\\d{1,16}(\\.)?(\\d{1,4})?$";

		public const string InputFloatNumber2 = "^\\d{1,32}(\\.)?(\\d{1,8})?$";

		public const string YearToMonth = "^[1-9]\\d{2,3}-((1[0-2])|(0?[1-9]))$";

		public const string YearToDate = "^[1-9]\\d{2,3}-((1[0-2])|(0?[1-9]))-(([1-2]\\d)|(3[0-1])|(0?[1-9]))$";

		public const string TimeSlot = "^[1-2]\\d{3}\\-[0-1]\\d至[1-2]\\d{3}\\-[0-1]\\d$";

		public Regex IllegalCharactersPattern = new Regex("[/:\\*\\?<>\\|\\]{1,6}");

		public const string PathIllegalCharacters = "[\\\\/:\\*\\?\"<>\\|]";

		public const string PathIllegalCharactersTxt = "/:*?\"<>|";

		public const string NonNumeric = "[^0-9\\.]";

		public static bool IsMath(string content, string pattern)
		{
			Regex regex = new Regex(pattern);
			return regex.IsMatch(content);
		}

		public static Regex GetRegex(string pattern)
		{
			return new Regex(pattern);
		}

		public static string ReplaceIllegalCharacters(string content)
		{
			return Regex.Replace(content, "[/:\\*\\?<>\\|\\\\]{1,6}", "");
		}
	}
	public class RemoteFileUpload
	{
		public static R UploadSupplierBasicAttachRequest(string filePath, string attachType, string realteId)
		{
			string valueByKey = ConfigHelper.GetValueByKey("uploadSupplierBaseInfoAttach");
			string text = DateTime.Now.Ticks.ToString("x");
			valueByKey = valueByKey + "?token=" + PublicVo.Token + "&rnd=" + DateTime.Now.ToFileTimeUtc();
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			dictionary.Add("supplierId", PublicVo.SupplyId);
			dictionary.Add("attachType", attachType);
			dictionary.Add("relateId", realteId);
			string text2 = null;
			valueByKey = valueByKey + "&_encData=" + text2;
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(valueByKey));
			httpWebRequest.Method = "POST";
			httpWebRequest.AllowWriteStreamBuffering = false;
			httpWebRequest.Timeout = 300000;
			httpWebRequest.ContentType = "multipart/form-data; boundary=" + text;
			FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
			BinaryReader binaryReader = new BinaryReader(fileStream);
			string text3 = "--" + text;
			string format = text3 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
			string s = string.Format(format, "file", Path.GetFileName(filePath));
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text + "--\r\n");
			long contentLength = fileStream.Length + bytes.Length + bytes2.Length;
			httpWebRequest.ContentLength = contentLength;
			try
			{
				int num = 40960;
				byte[] buffer = new byte[num];
				int num2 = 0;
				int num3 = binaryReader.Read(buffer, 0, num);
				DateTime now = DateTime.Now;
				Stream requestStream = httpWebRequest.GetRequestStream();
				requestStream.Write(bytes, 0, bytes.Length);
				while (num3 > 0)
				{
					requestStream.Write(buffer, 0, num3);
					num2 += num3;
					num3 = binaryReader.Read(buffer, 0, num);
				}
				requestStream.Write(bytes2, 0, bytes2.Length);
				requestStream.Close();
				using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
				string value = streamReader.ReadToEnd();
				R r = JsonConvert.DeserializeObject<R>(value);
				Regex regex = new Regex("^[a-zA-z0-9]{25,}$");
				if (r.ResultValue != null && regex.IsMatch(r.ResultValue.ToString()))
				{
					r.ResultValue = r.ResultValue.ToString();
				}
				httpWebResponse.Close();
				streamReader.Close();
				return r;
			}
			catch (Exception ex)
			{
				return new R(999, bl: false, "文件传输异常： " + ex.Message);
			}
			finally
			{
				fileStream.Close();
				binaryReader.Close();
			}
		}

		public static R UploadSupplierBidFileRequest(string filePath, string signKey, string attachType, OsZbSupplierBidAttachment bidInfo)
		{
			string valueByKey = ConfigHelper.GetValueByKey("uploadSupplierBidFile");
			string text = DateTime.Now.Ticks.ToString("x");
			valueByKey = valueByKey + "?token=" + PublicVo.Token + "&rnd=" + DateTime.Now.ToFileTimeUtc();
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			dictionary.Add("supplierId", PublicVo.SupplyId);
			dictionary.Add("attachType", attachType);
			dictionary.Add("projectNo", bidInfo.ProjectNo);
			dictionary.Add("projectName", bidInfo.ProjectName);
			dictionary.Add("markNo", bidInfo.MarkNo);
			dictionary.Add("markName", bidInfo.MarkName);
			dictionary.Add("packNo", bidInfo.PackNo);
			dictionary.Add("packName", bidInfo.PackName);
			if (signKey != null && signKey != "")
			{
				dictionary.Add("signKey", signKey);
			}
			string text2 = null;
			valueByKey = valueByKey + "&_encData=" + text2;
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(valueByKey));
			httpWebRequest.Method = "POST";
			httpWebRequest.AllowWriteStreamBuffering = false;
			httpWebRequest.Timeout = 300000;
			httpWebRequest.ContentType = "multipart/form-data; boundary=" + text;
			FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
			BinaryReader binaryReader = new BinaryReader(fileStream);
			string text3 = "--" + text;
			string format = text3 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
			string s = string.Format(format, "file", Path.GetFileName(filePath));
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text + "--\r\n");
			long contentLength = fileStream.Length + bytes.Length + bytes2.Length;
			httpWebRequest.ContentLength = contentLength;
			try
			{
				int num = 40960;
				byte[] buffer = new byte[num];
				int num2 = 0;
				int num3 = binaryReader.Read(buffer, 0, num);
				DateTime now = DateTime.Now;
				Stream requestStream = httpWebRequest.GetRequestStream();
				requestStream.Write(bytes, 0, bytes.Length);
				while (num3 > 0)
				{
					requestStream.Write(buffer, 0, num3);
					num2 += num3;
					num3 = binaryReader.Read(buffer, 0, num);
				}
				requestStream.Write(bytes2, 0, bytes2.Length);
				requestStream.Close();
				using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
				string value = streamReader.ReadToEnd();
				R r = JsonConvert.DeserializeObject<R>(value);
				Regex regex = new Regex("^[a-zA-z0-9]{25,}$");
				if (r.ResultValue != null && regex.IsMatch(r.ResultValue.ToString()))
				{
					r.ResultValue = null;
				}
				httpWebResponse.Close();
				streamReader.Close();
				return r;
			}
			catch (Exception ex)
			{
				return new R(999, bl: false, "文件传输异常： " + ex.Message);
			}
			finally
			{
				fileStream.Close();
				binaryReader.Close();
			}
		}

		public static R UploadSupplierBidFileRequest(string filePath, string attachType, OsZbSupplierBidAttachment bidInfo)
		{
			return UploadSupplierBidFileRequest(filePath, null, attachType, bidInfo);
		}

		public static R UploadBidZipFileRequest(string filePath, OsZbSupplierBidAttachment bidInfo, string signKey)
		{
			string valueByKey = ConfigHelper.GetValueByKey("uploadSupplierBidFile");
			string text = DateTime.Now.Ticks.ToString("x");
			valueByKey = valueByKey + "?token=" + PublicVo.Token + "&rnd=" + DateTime.Now.ToFileTimeUtc();
			Dictionary<string, string> dictionary = new Dictionary<string, string>
			{
				{
					"supplierId",
					PublicVo.SupplyId
				},
				{ "id", bidInfo.Id },
				{ "attachType", bidInfo.AttachType }
			};
			if (signKey != null && signKey != "")
			{
				dictionary.Add("signKey", signKey);
			}
			string text2 = SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(dictionary));
			valueByKey = valueByKey + "&_encData=" + text2;
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(valueByKey));
			httpWebRequest.Method = "POST";
			httpWebRequest.AllowWriteStreamBuffering = false;
			httpWebRequest.Timeout = 300000;
			httpWebRequest.ContentType = "multipart/form-data; boundary=" + text;
			FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
			BinaryReader binaryReader = new BinaryReader(fileStream);
			string text3 = "--" + text;
			string format = text3 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
			string s = string.Format(format, "file", Path.GetFileName(filePath));
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text + "--\r\n");
			long contentLength = fileStream.Length + bytes.Length + bytes2.Length;
			httpWebRequest.ContentLength = contentLength;
			try
			{
				int num = 40960;
				byte[] buffer = new byte[num];
				int num2 = 0;
				int num3 = binaryReader.Read(buffer, 0, num);
				DateTime now = DateTime.Now;
				Stream requestStream = httpWebRequest.GetRequestStream();
				requestStream.Write(bytes, 0, bytes.Length);
				while (num3 > 0)
				{
					requestStream.Write(buffer, 0, num3);
					num2 += num3;
					num3 = binaryReader.Read(buffer, 0, num);
				}
				requestStream.Write(bytes2, 0, bytes2.Length);
				requestStream.Close();
				using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
				string value = streamReader.ReadToEnd();
				R r = JsonConvert.DeserializeObject<R>(value);
				Regex regex = new Regex("^[a-zA-z0-9]{25,}$");
				if (r.ResultValue == null || regex.IsMatch(r.ResultValue.ToString()))
				{
				}
				httpWebResponse.Close();
				streamReader.Close();
				return r;
			}
			catch (Exception ex)
			{
				return new R(999, bl: false, "文件传输异常： " + ex.Message);
			}
			finally
			{
				fileStream.Close();
				binaryReader.Close();
			}
		}

		public static R UploadBidFile(string api, string filePath, Dictionary<string, object> paramDict)
		{
			string text = BendApi.API_PREFIX + api;
			text = text + "?rnd=" + DateTime.Now.ToFileTimeUtc();
			string text2 = DateTime.Now.Ticks.ToString("x");
			if (paramDict != null && paramDict.Count > 0)
			{
				string text3 = SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(paramDict));
				text = text + "&_encData=" + text3;
			}
			FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
			long length = fileStream.Length;
			int num = (int)((double)length / 10752.0);
			num = 2000000;
			BinaryReader binaryReader = new BinaryReader(fileStream);
			string text4 = "--" + text2;
			string format = text4 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
			string s = string.Format(format, "file", Path.GetFileName(filePath));
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text2 + "--\r\n");
			long contentLength = fileStream.Length + bytes.Length + bytes2.Length;
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(text));
			httpWebRequest.Method = "POST";
			httpWebRequest.Proxy = null;
			httpWebRequest.AllowWriteStreamBuffering = false;
			httpWebRequest.Timeout = num;
			httpWebRequest.ReadWriteTimeout = num;
			httpWebRequest.ContentType = "multipart/form-data; boundary=" + text2;
			httpWebRequest.ContentLength = contentLength;
			try
			{
				int num2 = 1024;
				byte[] buffer = new byte[num2];
				int num3 = 0;
				int num4 = binaryReader.Read(buffer, 0, num2);
				DateTime now = DateTime.Now;
				Stream requestStream = httpWebRequest.GetRequestStream();
				requestStream.Write(bytes, 0, bytes.Length);
				while (num4 > 0)
				{
					requestStream.Write(buffer, 0, num4);
					num3 += num4;
					num4 = binaryReader.Read(buffer, 0, num2);
				}
				requestStream.Write(bytes2, 0, bytes2.Length);
				requestStream.Close();
				using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
				string value = streamReader.ReadToEnd();
				R r = JsonConvert.DeserializeObject<R>(value);
				Regex regex = new Regex("^[a-zA-z0-9]{25,}$");
				if (r.ResultValue != null && regex.IsMatch(r.ResultValue.ToString()))
				{
					r.ResultValue = SM4Util.decryptEcb(ContantUtils.SM4_KEY, r.ResultValue.ToString());
				}
				httpWebResponse.Close();
				streamReader.Close();
				return r;
			}
			catch (Exception ex)
			{
				return new R(999, bl: false, "文件传输出错,服务器异常:" + ex.Message);
			}
			finally
			{
				fileStream.Close();
				binaryReader.Close();
			}
		}

		public static R AsynUpload(string api, string filePath, Dictionary<string, object> paramDict, bool KeepAlive = true)
		{
			R r = new R();
			string text = BendApi.API_PREFIX + api;
			text = text + "?rnd=" + DateTime.Now.ToFileTimeUtc();
			string text2 = DateTime.Now.Ticks.ToString("x");
			if (paramDict != null && paramDict.Count > 0)
			{
				string text3 = SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(paramDict));
				text = text + "&_encData=" + text3;
			}
			HttpState httpState = new HttpState();
			try
			{
				FileInfo fileInfo = new FileInfo(filePath);
				httpState.Size = fileInfo.Length;
				Uri requestUri = new Uri(text);
				HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(requestUri);
				httpWebRequest.Method = "POST";
				httpWebRequest.Proxy = null;
				httpWebRequest.AllowWriteStreamBuffering = false;
				httpWebRequest.KeepAlive = KeepAlive;
				httpWebRequest.ServicePoint.ConnectionLimit = 20;
				httpWebRequest.ContentType = "multipart/form-data; boundary=" + text2;
				string text4 = "--" + text2;
				string format = text4 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
				string s = string.Format(format, "file", Path.GetFileName(filePath));
				byte[] bytes = Encoding.UTF8.GetBytes(s);
				byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text2 + "--\r\n");
				long contentLength = httpState.Size + bytes.Length + bytes2.Length;
				httpState.Request = httpWebRequest;
				httpState.FullName = filePath;
				httpState.PostHeaderBytes = bytes;
				httpState.BoundaryBytes = bytes2;
				httpWebRequest.ContentLength = contentLength;
				ManualResetEvent operationComplete = httpState.OperationComplete;
				httpWebRequest.BeginGetRequestStream(EndGetStreamCallback, httpState);
				operationComplete.WaitOne();
				if (httpState.OperationException != null)
				{
					r.Successful = false;
					r.Code = 999;
					r.ResultHint = httpState.OperationException.Message;
				}
				else
				{
					r.Successful = true;
					r.Code = 200;
					r.ResultValue = httpState.StatusDescription;
				}
			}
			catch (Exception ex)
			{
				r.Successful = false;
				r.Code = 999;
				r.ResultHint = ex.Message;
			}
			return r;
		}

		private static void EndGetStreamCallback(IAsyncResult ar)
		{
			HttpState httpState = (HttpState)ar.AsyncState;
			DateTime now = DateTime.Now;
			Stream stream = null;
			try
			{
				stream = httpState.Request.EndGetRequestStream(ar);
				byte[] buffer = new byte[2048];
				int num = 0;
				int num2 = 0;
				FileStream fileStream = File.OpenRead(httpState.FullName);
				stream.Write(httpState.PostHeaderBytes, 0, httpState.PostHeaderBytes.Length);
				do
				{
					num2 = fileStream.Read(buffer, 0, 2048);
					stream.Write(buffer, 0, num2);
					num += num2;
				}
				while (num2 != 0);
				stream.Write(httpState.BoundaryBytes, 0, httpState.BoundaryBytes.Length);
				httpState.Request.BeginGetResponse(EndGetResponseCallback, httpState);
			}
			catch (Exception operationException)
			{
				Console.WriteLine("Could not get the request stream.");
				httpState.OperationException = operationException;
				httpState.OperationComplete.Set();
			}
			finally
			{
				stream?.Close();
			}
		}

		private static void EndGetResponseCallback(IAsyncResult ar)
		{
			HttpState httpState = (HttpState)ar.AsyncState;
			HttpWebResponse httpWebResponse = null;
			try
			{
				httpWebResponse = (HttpWebResponse)httpState.Request.EndGetResponse(ar);
				httpWebResponse.Close();
				httpState.StatusDescription = httpWebResponse.StatusDescription;
				httpState.OperationComplete.Set();
			}
			catch (Exception operationException)
			{
				Console.WriteLine("Error getting response.");
				httpState.OperationException = operationException;
				httpState.OperationComplete.Set();
			}
		}

		public static R UploadBidFileByFragment(string api, string fileName, byte[] WriteArray, Dictionary<string, object> paramDict)
		{
			string text = BendApi.API_PREFIX + api;
			text = text + "?rnd=" + DateTime.Now.ToFileTimeUtc();
			string text2 = DateTime.Now.Ticks.ToString("x");
			if (paramDict != null && paramDict.Count > 0)
			{
				string text3 = SM4Util.encryptEcb(ContantUtils.SM4_KEY, JsonConvert.SerializeObject(paramDict));
				text = text + "&_encData=" + text3;
			}
			LogsHelper.InsertLog("---当前文件上传请求url:" + text, PublicVo.EntName, "Logs/operate.log");
			int num = 1200000;
			string text4 = "--" + text2;
			string format = text4 + "\r\nContent-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:application/octet-stream\r\n\r\n";
			string s = string.Format(format, "file", SM4Util.encryptEcb(ContantUtils.SM4_KEY, Path.GetFileName(fileName)));
			byte[] bytes = Encoding.UTF8.GetBytes(s);
			byte[] bytes2 = Encoding.ASCII.GetBytes("\r\n--" + text2 + "--\r\n");
			long contentLength = WriteArray.Length + bytes.Length + bytes2.Length;
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(new Uri(text));
			httpWebRequest.Method = "POST";
			httpWebRequest.Proxy = null;
			httpWebRequest.AllowWriteStreamBuffering = false;
			httpWebRequest.Timeout = num;
			httpWebRequest.ReadWriteTimeout = num;
			httpWebRequest.ContentType = "multipart/form-data; boundary=" + text2;
			httpWebRequest.ContentLength = contentLength;
			try
			{
				DateTime now = DateTime.Now;
				Stream requestStream = httpWebRequest.GetRequestStream();
				requestStream.Write(bytes, 0, bytes.Length);
				requestStream.Write(WriteArray, 0, WriteArray.Length);
				requestStream.Write(bytes2, 0, bytes2.Length);
				requestStream.Close();
				using HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8);
				string value = streamReader.ReadToEnd();
				R r = JsonConvert.DeserializeObject<R>(value);
				Regex regex = new Regex("^[a-zA-z0-9]{25,}$");
				if (r.ResultValue != null && regex.IsMatch(r.ResultValue.ToString()))
				{
					r.ResultValue = SM4Util.decryptEcb(ContantUtils.SM4_KEY, r.ResultValue.ToString());
				}
				httpWebResponse.Close();
				streamReader.Close();
				return r;
			}
			catch (Exception ex)
			{
				return new R(999, bl: false, "文件传输出错,服务器异常:" + ex.Message);
			}
		}
	}
	public static class SegmentHelper
	{
		public static void ToDo<TEntity>(ICollection<TEntity> collection, Action<ICollection<TEntity>> continueDo, int pageSize = 5000)
		{
			int count = collection.Count;
			int num = ((count % pageSize == 0) ? (count / pageSize) : (count / pageSize + 1));
			int num2 = 0;
			ICollection<TEntity> collection2 = null;
			while ((collection2 = collection.Skip(num2++ * pageSize).Take(pageSize).ToList()).Any())
			{
				continueDo(collection2);
			}
		}

		public static IEnumerable<TEntity> ToPagination<TEntity>(ICollection<TEntity> collection, out int totalPageNum, int curPage, int pageSize = 5000)
		{
			int count = collection.Count;
			totalPageNum = ((count % pageSize == 0) ? (count / pageSize) : (count / pageSize + 1));
			return collection.Skip(curPage * pageSize).Take(pageSize);
		}
	}
	public static class SQLiteLibrary
	{
		private static string sqliteDbName = ConfigHelper.GetValueByKey("sqliteDBName");

		private static string sqliteDbLocation = ConfigHelper.GetValueByKey("sqliteDBLocation");

		public static void DeleteBySql(string dbLocation, string dbName, string deleteSql, bool delAttach = true, string relatePage = null)
		{
			SQLiteConnection sQLiteConnection = null;
			SQLiteCommand sQLiteCommand = null;
			try
			{
				if (delAttach)
				{
					Match match = Regex.Match(deleteSql.ToLower(), "os_zb_\\w{1,50}\\s{1,5}where");
					Match match2 = Regex.Match(deleteSql.ToLower(), "id\\s{0,5}(=\\s{0,5}\\'\\w{1,40}\\'|in\\s{0,5}\\([\\'\\,\\w]{1,400}\\))");
					string text = null;
					string text2 = null;
					if (match.Success)
					{
						text = match.Groups[0].Value.Replace("where", "").Trim();
					}
					if (match2.Success)
					{
						text2 = Regex.Replace(match2.Groups[0].Value, "(id|in|\\(|\\)|\\=)", "").Trim();
					}
					text = (string.IsNullOrEmpty(relatePage) ? text : relatePage);
					if (!string.IsNullOrWhiteSpace(text) && !string.IsNullOrWhiteSpace(text2))
					{
						string text3 = "delete from SYS_FILE_INFO where RELATED_PAGE in('" + text.ToUpper() + "','" + text + "') and RELATED_ID in(" + text2 + ")";
						deleteSql = deleteSql + ";" + text3;
					}
				}
				string text4 = dbLocation + "\\" + dbName;
				sQLiteConnection = new SQLiteConnection("data source=" + text4);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				sQLiteCommand = new SQLiteCommand();
				sQLiteCommand.Connection = sQLiteConnection;
				sQLiteCommand.CommandText = deleteSql;
				sQLiteCommand.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				LogsHelper.AddErrorLog(ex, "创建表异常,数据库:" + sqliteDbName + ",sql语句:" + deleteSql);
				Console.WriteLine(ex.Message);
				throw ex;
			}
			finally
			{
				sQLiteCommand?.Dispose();
				sQLiteConnection?.Dispose();
			}
		}

		public static void InsertData(string dbLocation, string dbName, string insertSql)
		{
			SQLiteConnection sQLiteConnection = null;
			try
			{
				string text = dbLocation + "\\" + dbName;
				using (sQLiteConnection = new SQLiteConnection("data source=" + text))
				{
					if (sQLiteConnection.State != ConnectionState.Open)
					{
						sQLiteConnection.Open();
					}
					using SQLiteCommand sQLiteCommand = new SQLiteCommand();
					sQLiteCommand.Connection = sQLiteConnection;
					sQLiteCommand.CommandText = insertSql;
					sQLiteCommand.ExecuteNonQuery();
				}
			}
			catch (Exception ex)
			{
				LogsHelper.AddErrorLog(ex, "sql执行异常,数据库:" + dbName + ",sql语句:" + insertSql);
				Console.WriteLine(ex.Message);
				throw ex;
			}
			finally
			{
				SQLiteConnection.ClearAllPools();
			}
		}

		public static T SelectOneBySql<T>(string sql, string dbLocation = null, string dbName = null)
		{
			dbLocation = dbLocation ?? sqliteDbLocation;
			dbName = dbName ?? sqliteDbName;
			IList<T> list = SelectBySql<T>(dbLocation, dbName, sql);
			return (list.Count > 0) ? list[0] : default(T);
		}

		public static DataTable SelectDataBySql(string dbLocation, string dbName, string selectSql)
		{
			string text = dbLocation + "\\" + dbName;
			DataTable dataTable = new DataTable();
			try
			{
				using SQLiteConnection sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				using SQLiteCommand sQLiteCommand = new SQLiteCommand(selectSql, sQLiteConnection);
				sQLiteCommand.CommandTimeout = 120;
				using SQLiteDataReader reader = sQLiteCommand.ExecuteReader();
				LoadDataTable(dataTable, reader);
				sQLiteCommand.Parameters.Clear();
			}
			catch (Exception ex)
			{
				LogsHelper.AddErrorLog(ex, "查询异常,数据库:" + dbName + "，sql:" + selectSql);
				throw ex;
			}
			return dataTable;
		}

		private static void LoadDataTable(DataTable dt, SQLiteDataReader reader)
		{
			Dictionary<Type, Type> dictionary = new Dictionary<Type, Type>();
			dictionary.Add(typeof(byte[]), typeof(byte[]));
			for (int i = 0; i < reader.FieldCount; i++)
			{
				if (dt.Columns.Contains(reader.GetName(i)))
				{
					dt.Columns.Add(new DataColumn
					{
						DataType = reader.GetFieldType(i),
						ColumnName = reader.GetName(i) + "_" + i
					});
				}
				else if (dictionary.ContainsKey(reader.GetFieldType(i)))
				{
					dt.Columns.Add(new DataColumn
					{
						DataType = dictionary[reader.GetFieldType(i)],
						ColumnName = reader.GetName(i)
					});
				}
				else
				{
					dt.Columns.Add(new DataColumn
					{
						DataType = reader.GetFieldType(i),
						ColumnName = reader.GetName(i)
					});
				}
			}
			if (reader == null || !reader.HasRows)
			{
				return;
			}
			int num = 0;
			while (reader.Read())
			{
				object[] array = new object[dt.Columns.Count];
				for (int j = 0; j < reader.FieldCount; j++)
				{
					array[j] = reader.GetValue(j);
				}
				dt.Rows.Add(array);
				num++;
			}
		}

		public static void ExcuteSql(string nonQuerySql, bool compressDb = false, string _sqliteDbLocation = null, string _sqliteDbName = null)
		{
			SQLiteConnection sQLiteConnection = null;
			try
			{
				_sqliteDbLocation = _sqliteDbLocation ?? sqliteDbLocation;
				_sqliteDbName = _sqliteDbName ?? sqliteDbName;
				string text = _sqliteDbLocation + "\\" + _sqliteDbName;
				sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				SQLiteCommand sQLiteCommand = new SQLiteCommand();
				sQLiteCommand.Connection = sQLiteConnection;
				if (compressDb)
				{
					nonQuerySql += ";VACUUM;";
				}
				sQLiteCommand.CommandText = nonQuerySql;
				sQLiteCommand.ExecuteNonQuery();
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				LogsHelper.AddErrorLog(ex, "sql执行异常,数据库:" + _sqliteDbName + ",sql:\r\n" + nonQuerySql);
				throw ex;
			}
			finally
			{
				sQLiteConnection?.Close();
			}
		}

		public static bool ExcuteSql(string dbLocation, string dbName, string sql)
		{
			SQLiteConnection sQLiteConnection = null;
			SQLiteCommand sQLiteCommand = null;
			try
			{
				string text = dbLocation + "\\" + dbName;
				sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				sQLiteCommand = new SQLiteCommand();
				sQLiteCommand.Connection = sQLiteConnection;
				sQLiteCommand.CommandText = sql;
				sQLiteCommand.ExecuteNonQuery();
				return true;
			}
			catch (Exception ex)
			{
				LogsHelper.AddErrorLog(ex, "sql执行异常,数据库:" + dbName + ",sql:\r\n" + sql);
				throw ex;
			}
			finally
			{
				sQLiteCommand?.Dispose();
				sQLiteConnection?.Close();
			}
		}

		public static IList<T> SelectBySql<IL, T>(string dbLocation, string dbName, string selectSql) where IL : IList<T>
		{
			IList<T> list = Activator.CreateInstance<IL>();
			return SelectBySql(dbLocation, dbName, selectSql, ref list);
		}

		public static List<T> SelectBySql<T>(string dbLocation, string dbName, string selectSql)
		{
			IList<T> list = new List<T>();
			return (List<T>)SelectBySql(dbLocation, dbName, selectSql, ref list);
		}

		private static IList<T> SelectBySql<T>(string dbLocation, string dbName, string selectSql, ref IList<T> list)
		{
			object obj;
			if (list != null)
			{
				obj = list;
			}
			else
			{
				IList<T> list2 = new List<T>();
				obj = list2;
			}
			list = (IList<T>)obj;
			SQLiteConnection sQLiteConnection = null;
			SQLiteCommand sQLiteCommand = null;
			SQLiteDataReader sQLiteDataReader = null;
			try
			{
				Type typeFromHandle = typeof(T);
				Dictionary<string, PropertyInfo> fieldDict = GetFieldDict(typeFromHandle);
				dbLocation = dbLocation ?? sqliteDbLocation;
				dbName = dbName ?? sqliteDbName;
				string text = dbLocation + "\\" + dbName;
				sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				sQLiteCommand = new SQLiteCommand(selectSql, sQLiteConnection);
				sQLiteCommand.CommandTimeout = 200;
				sQLiteDataReader = sQLiteCommand.ExecuteReader();
				object obj2 = null;
				string text2 = null;
				object obj3 = null;
				while (sQLiteDataReader.Read())
				{
					T val = Activator.CreateInstance<T>();
					for (int i = 0; i < sQLiteDataReader.FieldCount; i++)
					{
						try
						{
							text2 = sQLiteDataReader.GetName(i);
							obj3 = sQLiteDataReader.GetValue(i);
							if (fieldDict.ContainsKey(text2) && obj3 != DBNull.Value)
							{
								obj2 = ((!fieldDict[text2].PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(obj3, fieldDict[text2].PropertyType) : Convert.ChangeType(obj3, Nullable.GetUnderlyingType(fieldDict[text2].PropertyType)));
								fieldDict[text2].SetValue(val, obj2, null);
							}
						}
						catch (FormatException innerException)
						{
							throw new FormatException($"字段/属性：{text2}格式转换异常,值:{obj3},类型:{fieldDict[text2].PropertyType}", innerException);
						}
						catch (Exception ex)
						{
							throw ex;
						}
					}
					list.Add(val);
				}
				return list;
			}
			catch (Exception ex2)
			{
				LogsHelper.AddErrorLog(ex2, "查询异常,数据库:" + dbName + ",sql:\r\n" + selectSql);
				throw ex2;
			}
			finally
			{
				sQLiteCommand?.Dispose();
				sQLiteDataReader?.Close();
				sQLiteConnection.Close();
			}
		}

		private static Dictionary<string, PropertyInfo> GetFieldDict(Type tbc)
		{
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			if (tbc == (Type)null)
			{
				return dictionary;
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] array = tbc.BaseType?.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] array2 = tbc.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] members = tbc.GetMembers(bindingAttr);
			MemberInfo[] array3 = array;
			foreach (MemberInfo memberInfo in array3)
			{
				TableFieldAttribute customAttribute = memberInfo.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute != null && customAttribute.ColumName != null && !"".Equals(customAttribute.ColumName))
				{
					dictionary[customAttribute.ColumName] = ((memberInfo is PropertyInfo) ? ((PropertyInfo)memberInfo) : tbc.GetProperty(memberInfo.Name.UpperCaseFirst()));
				}
				SubTableAttribute customAttribute2 = memberInfo.GetCustomAttribute<SubTableAttribute>();
				SubTableFieldMapAttribute[] array4 = (SubTableFieldMapAttribute[])memberInfo.GetCustomAttributes(typeof(SubTableFieldMapAttribute), inherit: false);
				if (customAttribute2 != null && array4 != null && array4.Length != 0)
				{
					SubTableFieldMapAttribute[] array5 = array4;
					foreach (SubTableFieldMapAttribute subTableFieldMapAttribute in array5)
					{
						dictionary[subTableFieldMapAttribute.ColumnName] = customAttribute2.Type.GetProperty(subTableFieldMapAttribute.PropName);
					}
				}
			}
			MemberInfo[] array6 = array2;
			foreach (MemberInfo memberInfo2 in array6)
			{
				TableFieldAttribute customAttribute = memberInfo2.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute != null && customAttribute.ColumName != null && !"".Equals(customAttribute.ColumName))
				{
					dictionary[customAttribute.ColumName] = ((memberInfo2 is PropertyInfo) ? ((PropertyInfo)memberInfo2) : tbc.GetProperty(memberInfo2.Name.UpperCaseFirst()));
				}
				SubTableAttribute customAttribute3 = memberInfo2.GetCustomAttribute<SubTableAttribute>();
				SubTableFieldMapAttribute[] array7 = (SubTableFieldMapAttribute[])memberInfo2.GetCustomAttributes(typeof(SubTableFieldMapAttribute), inherit: false);
				if (customAttribute3 != null && array7 != null && array7.Length != 0)
				{
					SubTableFieldMapAttribute[] array8 = array7;
					foreach (SubTableFieldMapAttribute subTableFieldMapAttribute2 in array8)
					{
						dictionary[subTableFieldMapAttribute2.ColumnName] = customAttribute3.Type.GetProperty(subTableFieldMapAttribute2.PropName);
					}
				}
			}
			MemberInfo[] array9 = members;
			foreach (MemberInfo memberInfo3 in array9)
			{
				TableFieldAttribute customAttribute = memberInfo3.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute != null && customAttribute.ColumName != null && !"".Equals(customAttribute.ColumName))
				{
					dictionary[customAttribute.ColumName] = ((memberInfo3 is PropertyInfo) ? ((PropertyInfo)memberInfo3) : tbc.GetProperty(memberInfo3.Name.UpperCaseFirst()));
				}
				SubTableAttribute customAttribute4 = memberInfo3.GetCustomAttribute<SubTableAttribute>();
				SubTableFieldMapAttribute[] array10 = (SubTableFieldMapAttribute[])memberInfo3.GetCustomAttributes(typeof(SubTableFieldMapAttribute), inherit: false);
				if (customAttribute4 != null && array10 != null && array10.Length != 0)
				{
					SubTableFieldMapAttribute[] array11 = array10;
					foreach (SubTableFieldMapAttribute subTableFieldMapAttribute3 in array11)
					{
						dictionary[subTableFieldMapAttribute3.ColumnName] = customAttribute4.Type.GetProperty(subTableFieldMapAttribute3.PropName);
					}
				}
			}
			return dictionary;
		}

		public static DataTable SelectBySql(string selectSql)
		{
			return SelectDataBySql(sqliteDbLocation, sqliteDbName, selectSql);
		}

		public static string UpperCaseFirst(this string str)
		{
			if (string.IsNullOrWhiteSpace(str))
			{
				return string.Empty;
			}
			char[] array = str.ToCharArray();
			char c = array[0];
			if ('a' <= c && c <= 'z')
			{
				c = (char)(c & 0xFFFFFFDFu);
			}
			array[0] = c;
			return new string(array);
		}

		public static bool ExecuteSqlByTransaction(string dbLocation, string dbName, string[] sqlArr)
		{
			bool result = true;
			string connectionString = "data source=" + dbLocation + "\\" + dbName;
			using (SQLiteConnection sQLiteConnection = new SQLiteConnection(connectionString))
			{
				sQLiteConnection.Open();
				using SQLiteTransaction sQLiteTransaction = sQLiteConnection.BeginTransaction();
				using SQLiteCommand sQLiteCommand = new SQLiteCommand();
				try
				{
					foreach (string cmdText in sqlArr)
					{
						PrepareCommand(sQLiteCommand, sQLiteConnection, sQLiteTransaction, cmdText, null);
						int num = sQLiteCommand.ExecuteNonQuery();
						sQLiteCommand.Parameters.Clear();
					}
					sQLiteTransaction.Commit();
				}
				catch
				{
					result = false;
					sQLiteTransaction.Rollback();
					throw;
				}
			}
			return result;
		}

		private static void PrepareCommand(SQLiteCommand cmd, SQLiteConnection conn, SQLiteTransaction trans, string cmdText, SQLiteParameter[] cmdParms)
		{
			if (conn.State != ConnectionState.Open)
			{
				conn.Open();
			}
			cmd.Connection = conn;
			cmd.CommandText = cmdText;
			if (trans != null)
			{
				cmd.Transaction = trans;
			}
			cmd.CommandType = CommandType.Text;
			if (cmdParms != null)
			{
				foreach (SQLiteParameter parameter in cmdParms)
				{
					cmd.Parameters.Add(parameter);
				}
			}
		}

		public static int InsertFileSql(string sql, string filePath, string _sqliteDbLocation = null, string _sqliteDbName = null)
		{
			int result = 0;
			_sqliteDbLocation = _sqliteDbLocation ?? sqliteDbLocation;
			_sqliteDbName = _sqliteDbName ?? sqliteDbName;
			string connectionString = "data source=" + _sqliteDbLocation + "\\" + _sqliteDbName;
			SQLiteParameter[] parameters = null;
			if (filePath != null)
			{
				using FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
				using BinaryReader binaryReader = new BinaryReader(fileStream);
				byte[] value = binaryReader.ReadBytes((int)fileStream.Length);
				parameters = new SQLiteParameter[1]
				{
					new SQLiteParameter("@file", value)
				};
			}
			using (SQLiteConnection sQLiteConnection = new SQLiteConnection(connectionString))
			{
				sQLiteConnection.Open();
				using DbTransaction dbTransaction = sQLiteConnection.BeginTransaction();
				SQLiteCommand sQLiteCommand = ConfigCommand(sQLiteConnection, sql, isProcess: false, parameters);
				result = sQLiteCommand.ExecuteNonQuery();
				sQLiteCommand.Parameters.Clear();
				dbTransaction.Commit();
			}
			return result;
		}

		private static SQLiteCommand ConfigCommand(SQLiteConnection connection, string sql, bool isProcess, SQLiteParameter[] parameters)
		{
			SQLiteCommand sQLiteCommand = new SQLiteCommand(connection);
			sQLiteCommand.CommandText = sql;
			if (isProcess)
			{
				sQLiteCommand.CommandType = CommandType.StoredProcedure;
			}
			else
			{
				sQLiteCommand.CommandType = CommandType.Text;
			}
			if (parameters != null)
			{
				sQLiteCommand.Parameters.AddRange(parameters);
			}
			return sQLiteCommand;
		}

		public static DataSet SelectDataSetBySql(string _dbLocation, string _dbName, string sql, params string[] tableNames)
		{
			_dbLocation = _dbLocation ?? sqliteDbLocation;
			_dbName = _dbName ?? sqliteDbName;
			string connectionString = "data source=" + _dbLocation + "\\" + _dbName;
			using SQLiteConnection connection = new SQLiteConnection(connectionString);
			using SQLiteCommand sQLiteCommand = ConfigCommand(connection, sql, isProcess: false, null);
			try
			{
				DataSet dataSet = new DataSet();
				SQLiteDataAdapter sQLiteDataAdapter = new SQLiteDataAdapter();
				if (tableNames != null && tableNames.Length != 0)
				{
					for (int i = 0; i < tableNames.Length; i++)
					{
						if (i == 0)
						{
							sQLiteDataAdapter.TableMappings.Add("Table", tableNames[i]);
						}
						else
						{
							sQLiteDataAdapter.TableMappings.Add("Table" + i, tableNames[i]);
						}
					}
				}
				sQLiteDataAdapter.SelectCommand = sQLiteCommand;
				sQLiteDataAdapter.Fill(dataSet);
				sQLiteCommand.Parameters.Clear();
				sQLiteDataAdapter.Dispose();
				return dataSet;
			}
			catch (Exception ex)
			{
				throw ex;
			}
		}

		public static int InsertByParams<T>(IEnumerable<T> list, Dictionary<string, string> extraParams = null, string _sqliteDbLocation = null, string _sqliteDbName = null) where T : CommonEntity
		{
			List<SQLiteParameter> paramters;
			string sql = SQLiteSqlUtils.CreateBinaryInsert(list, extraParams, out paramters);
			int result = 0;
			_sqliteDbLocation = _sqliteDbLocation ?? sqliteDbLocation;
			_sqliteDbName = _sqliteDbName ?? sqliteDbName;
			string connectionString = "data source=" + _sqliteDbLocation + "\\" + _sqliteDbName;
			using (SQLiteConnection sQLiteConnection = new SQLiteConnection(connectionString))
			{
				sQLiteConnection.Open();
				using DbTransaction dbTransaction = sQLiteConnection.BeginTransaction();
				SQLiteCommand sQLiteCommand = ConfigCommand(sQLiteConnection, sql, isProcess: false, paramters.ToArray());
				result = sQLiteCommand.ExecuteNonQuery();
				sQLiteCommand.Parameters.Clear();
				dbTransaction.Commit();
			}
			return result;
		}

		public static int InsertBatch<T>(IEnumerable<T> list, string _sqliteDbLocation = null, string _sqliteDbName = null) where T : CommonEntity
		{
			string sql = SQLiteSqlUtils.CreateInsertSql(list.ToList());
			int num = list.Count();
			_sqliteDbLocation = _sqliteDbLocation ?? sqliteDbLocation;
			_sqliteDbName = _sqliteDbName ?? sqliteDbName;
			return ExcuteSql(_sqliteDbLocation, _sqliteDbName, sql) ? num : 0;
		}

		public static TElement SelectFirstValue<TElement>(string dbLocation, string dbName, string selectSql)
		{
			string text = dbLocation + "\\" + dbName;
			try
			{
				using SQLiteConnection sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				using SQLiteCommand sQLiteCommand = new SQLiteCommand(selectSql, sQLiteConnection);
				sQLiteCommand.CommandTimeout = 120;
				object obj = sQLiteCommand.ExecuteScalar();
				if (obj.GetType().ToString().Contains("System.Nullable"))
				{
					return (TElement)Convert.ChangeType(obj, Nullable.GetUnderlyingType(typeof(TElement)));
				}
				return (TElement)Convert.ChangeType(obj, typeof(TElement));
			}
			catch (Exception ex)
			{
				LogsHelper.AddErrorLog(ex, "查询异常,数据库:" + dbName + ",sql:\r\n" + selectSql);
				throw ex;
			}
		}

		public static void CreateTableWithNotExists(string dbSourcePath, string dbTargetPath, Dictionary<string, string> tableMapping)
		{
			StringBuilder stringBuilder = new StringBuilder();
			StringBuilder stringBuilder2 = new StringBuilder();
			int num = 0;
			foreach (KeyValuePair<string, string> item in tableMapping)
			{
				if (num++ != 0)
				{
					stringBuilder2.Append(",");
				}
				stringBuilder2.Append("'" + item.Key + "'");
			}
			string selectSql = $"select replace(sql,name,' IF NOT EXISTS '||name)||';' sql from sqlite_master where type = 'table' and name in({stringBuilder2})  order by name;";
			DataTable dataTable = SelectDataBySql(Path.GetDirectoryName(dbSourcePath), Path.GetFileName(dbSourcePath), selectSql);
			if (dataTable != null && dataTable.Rows?.Count > 0)
			{
				DataRowCollection rows = dataTable.Rows;
				for (int i = 0; i < rows.Count; i++)
				{
					stringBuilder.Append(rows[i]["sql"].ToString().Replace("\"", "") + "\r\n");
				}
				ExcuteSql(Path.GetDirectoryName(dbTargetPath), Path.GetFileName(dbTargetPath), stringBuilder.ToString());
			}
		}

		public static DataSet QuerySetForMigrateData(string dbLocation, string dbName, Dictionary<string, string> tableMapping)
		{
			string connectionString = "data source=" + dbLocation + "\\" + dbName;
			DataSet dataSet = new DataSet();
			StringBuilder stringBuilder = new StringBuilder();
			StringBuilder stringBuilder2 = new StringBuilder();
			int num = 0;
			foreach (KeyValuePair<string, string> item in tableMapping)
			{
				string text = ((item.Value == null) ? null : item.Value?.Replace(";", ""));
				stringBuilder.Append(" select * from " + item.Key + " where 1=1 " + text + ";\r\n");
				if (num++ != 0)
				{
					stringBuilder2.Append("',");
				}
				stringBuilder2.Append("'" + item.Key + "'");
			}
			SQLiteConnection sQLiteConnection = null;
			SQLiteCommand sQLiteCommand = null;
			SQLiteDataAdapter sQLiteDataAdapter = null;
			try
			{
				sQLiteConnection = new SQLiteConnection(connectionString);
				sQLiteConnection.Open();
				sQLiteCommand = ConfigCommand(sQLiteConnection, stringBuilder.ToString(), isProcess: false, null);
				sQLiteDataAdapter = new SQLiteDataAdapter(sQLiteCommand);
				int num2 = 0;
				foreach (KeyValuePair<string, string> item2 in tableMapping)
				{
					string text2 = ((num2 == 0) ? "Table" : ("Table" + Convert.ToString(num2)));
					sQLiteDataAdapter.TableMappings.Add(text2 ?? "", item2.Key);
					num2++;
				}
				sQLiteDataAdapter.Fill(dataSet);
				sQLiteCommand.Parameters.Clear();
			}
			catch (SQLiteException ex)
			{
				sQLiteConnection.Close();
				throw new Exception("数据库查询:" + ex.Message);
			}
			catch (Exception ex2)
			{
				sQLiteConnection.Close();
				throw new Exception("数据库查询:" + ex2.Message);
			}
			finally
			{
				sQLiteDataAdapter?.Dispose();
				sQLiteCommand?.Dispose();
				sQLiteConnection?.Dispose();
			}
			return dataSet;
		}

		public static DataSet QuerySet(string dbLocation, string dbName, string sqls, Dictionary<string, string> tableMapping)
		{
			string connectionString = "data source=" + dbLocation + "\\" + dbName;
			DataSet dataSet = new DataSet();
			SQLiteCommand sQLiteCommand = null;
			SQLiteDataAdapter sQLiteDataAdapter = null;
			using SQLiteConnection sQLiteConnection = new SQLiteConnection(connectionString);
			try
			{
				sQLiteConnection.Open();
				sQLiteCommand = ConfigCommand(sQLiteConnection, sqls, isProcess: false, null);
				sQLiteDataAdapter = new SQLiteDataAdapter(sQLiteCommand);
				int num = 0;
				foreach (KeyValuePair<string, string> item in tableMapping)
				{
					string text = ((num == 0) ? "Table" : ("Table" + Convert.ToString(num)));
					sQLiteDataAdapter.TableMappings.Add(text ?? "", item.Key);
					num++;
				}
				sQLiteDataAdapter.Fill(dataSet);
				sQLiteCommand.Parameters.Clear();
			}
			catch (SQLiteException ex)
			{
				sQLiteConnection.Close();
				throw new Exception("数据库查询:" + ex.Message);
			}
			finally
			{
				sQLiteDataAdapter?.Dispose();
				sQLiteCommand?.Dispose();
				sQLiteConnection?.Dispose();
			}
			return dataSet;
		}

		public static List<T> SelectBySql<T>(T t, Expression<Func<T, object>>[] whereColums, string dbLocation = null, string dbName = null) where T : CommonEntity
		{
			string selectSql = SQLiteSqlUtils.CreateSelectSql2(t, null, null, whereColums);
			dbLocation = dbLocation ?? sqliteDbLocation;
			dbName = dbName ?? sqliteDbName;
			return SelectBySql<T>(dbLocation, dbName, selectSql);
		}

		public static T SelectOneBySql<T>(T t, Expression<Func<T, object>>[] whereColums, string dbLocation = null, string dbName = null) where T : CommonEntity
		{
			string text = SQLiteSqlUtils.CreateSelectSql2(t, null, null, whereColums);
			text += " limit 0,1";
			dbLocation = dbLocation ?? sqliteDbLocation;
			dbName = dbName ?? sqliteDbName;
			List<T> list = SelectBySql<T>(dbLocation, dbName, text);
			return (list != null && list.Any()) ? list.First() : default(T);
		}

		public static int SelectCount<T>(T t, Expression<Func<T, object>>[] whereColums, string dbLocation = null, string dbName = null) where T : CommonEntity
		{
			string selectSql = SQLiteSqlUtils.CreateCountSql(t, whereColums);
			dbLocation = dbLocation ?? sqliteDbLocation;
			dbName = dbName ?? sqliteDbName;
			return SelectFirstValue<int>(dbLocation, dbName, selectSql);
		}

		public static int SelectCount(string selectSql, string dbLocation = null, string dbName = null)
		{
			dbLocation = dbLocation ?? sqliteDbLocation;
			dbName = dbName ?? sqliteDbName;
			return SelectFirstValue<int>(dbLocation, dbName, selectSql);
		}

		public static int UpdateByParams<T>(IEnumerable<T> list, Expression<Func<T, object>>[] updateCols, Expression<Func<T, object>>[] whereCols, string _sqliteDbLocation = null, string _sqliteDbName = null) where T : CommonEntity
		{
			List<SQLiteParameter> paramters;
			string text = SQLiteSqlUtils.CreateBinaryUpdate(list, updateCols, whereCols, out paramters);
			Console.WriteLine("当前sql:" + text);
			int result = 0;
			_sqliteDbLocation = _sqliteDbLocation ?? sqliteDbLocation;
			_sqliteDbName = _sqliteDbName ?? sqliteDbName;
			string connectionString = "data source=" + _sqliteDbLocation + "\\" + _sqliteDbName;
			using (SQLiteConnection sQLiteConnection = new SQLiteConnection(connectionString))
			{
				sQLiteConnection.Open();
				using DbTransaction dbTransaction = sQLiteConnection.BeginTransaction();
				SQLiteCommand sQLiteCommand = ConfigCommand(sQLiteConnection, text, isProcess: false, paramters.ToArray());
				result = sQLiteCommand.ExecuteNonQuery();
				sQLiteCommand.Parameters.Clear();
				dbTransaction.Commit();
			}
			return result;
		}
	}
	public class SqliteSqlHelper
	{
		private static string sqliteDbName = ConfigHelper.GetValueByKey("sqliteDBName");

		private static string sqliteDbLocation = ConfigHelper.GetValueByKey("sqliteDBLocation");

		public static string InsertSqlByFile(SysFileInfo sysFile)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("INSERT INTO SYS_FILE_INFO (");
			stringBuilder.Append("ID,RELATED_PAGE,RELATED_KEY,");
			stringBuilder.Append("FILE_PATH, FILE_FORMAT,FILE_DISP_NAME,");
			stringBuilder.Append(" CREATE_USER,UPLOAD_TIME,SIZE,");
			stringBuilder.Append(" RELATED_ID,SORT_VALUE,BINARY_FILE,HASH_CODE)");
			stringBuilder.Append(" VALUES('{0}', '{1}', '{2}', '{3}','{4}','{5}','{6}',datetime('now','localtime'),'{7}','{8}','{9}',@file,'{10}')");
			string format = stringBuilder.ToString();
			return string.Format(format, sysFile.Id, sysFile.RelatedPage, sysFile.RelatedKey, sysFile.FilePath, sysFile.FileFormat, sysFile.FileDispName, sysFile.CreateUser, sysFile.Size, sysFile.RelatedId, sysFile.SortValue, sysFile.HashCode);
		}

		public static List<OsZbPurchaseProjectInfo> SelectProjectInfoBySupplier()
		{
			string text = " select * from OS_ZB_PURCHASE_PROJECT_INFO ";
			text = text + " where OS_ZB_PURCHASE_PROJECT_INFO.SUPPLIER_ID ='" + PublicVo.Id + "'";
			return SQLiteLibrary.SelectBySql<OsZbPurchaseProjectInfo>(sqliteDbLocation, sqliteDbName, text);
		}

		public static List<OsZbMaterialList> SelectMatrialList(OsZbMaterialList parameter)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("SELECT  OS_ZB_MATERIAL_LIST.* FROM OS_ZB_MATERIAL_LIST where 1=1 ");
			if (!string.IsNullOrEmpty(parameter.MarkNo))
			{
				stringBuilder.Append(" AND OS_ZB_MATERIAL_LIST.MARK_NO ='" + parameter.MarkNo + "' ");
			}
			if (!string.IsNullOrEmpty(parameter.PackName))
			{
				stringBuilder.Append(" AND OS_ZB_MATERIAL_LIST.PACK_NAME ='" + parameter.PackName + "' ");
			}
			if (!string.IsNullOrEmpty(parameter.ProjectNo))
			{
				stringBuilder.Append("  AND OS_ZB_MATERIAL_LIST.PROJECT_NO='" + parameter.ProjectNo + "' ");
			}
			if (!string.IsNullOrEmpty(parameter.ProvinceApplyNo))
			{
				stringBuilder.Append("  AND OS_ZB_MATERIAL_LIST.PROVINCE_APPLY_NO ='" + parameter.ProvinceApplyNo + "' ");
			}
			if (!string.IsNullOrEmpty(parameter.HeadApplyNo))
			{
				stringBuilder.Append("  AND OS_ZB_MATERIAL_LIST.HEAD_APPLY_NO ='" + parameter.HeadApplyNo + "' ");
			}
			if (!string.IsNullOrEmpty(parameter.MaterialNo))
			{
				stringBuilder.Append("  AND OS_ZB_MATERIAL_LIST.MATERIAL_NO ='" + parameter.MaterialNo + "' ");
			}
			return SQLiteLibrary.SelectBySql<OsZbMaterialList>(sqliteDbLocation, sqliteDbName, stringBuilder.ToString());
		}

		public static string CreateReverseDeleteSql<T>(T t, string typeFlag, params Expression<Func<T, object>>[] whereColums)
		{
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			Type typeFromHandle = typeof(T);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic;
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			StringBuilder stringBuilder = new StringBuilder("DELETE FROM " + text + "  WHERE 1=1 AND ID NOT IN (");
			stringBuilder.AppendLine("\tSELECT ID FROM " + text + " WHERE 1=1  ");
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			TableFieldAttribute tableFieldAttribute = null;
			Dictionary<string, object> dictionary2 = new Dictionary<string, object>();
			foreach (Expression<Func<T, object>> expression in whereColums)
			{
				string key = expression.Body.ToString().Split('.')[1].ToUpper();
				dictionary2.Add(key, expression.Compile().Invoke(t));
			}
			for (int j = 0; j < fields.Length; j++)
			{
				tableFieldAttribute = fields[j].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist && dictionary2.TryGetValue(fields[j].Name.ToUpper(), out var value) && !string.IsNullOrWhiteSpace(value?.ToString()))
				{
					stringBuilder.AppendLine(" AND " + tableFieldAttribute.ColumName + " =");
					if (value is int || value is short || value is float || value is long || value is double || value is decimal)
					{
						stringBuilder.AppendLine($" {value} ");
					}
					else if (value is DateTime dateTime)
					{
						stringBuilder.AppendLine(" '" + dateTime.ToString("yyyy-MM-dd HH:mm:ss") + "'");
					}
					else
					{
						stringBuilder.AppendLine($" '{value}'");
					}
				}
			}
			for (int k = 0; k < properties.Length; k++)
			{
				tableFieldAttribute = properties[k].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist && dictionary2.TryGetValue(properties[k].Name.ToUpper(), out var value2) && !string.IsNullOrWhiteSpace(value2?.ToString()))
				{
					stringBuilder.AppendLine(" AND " + tableFieldAttribute.ColumName + " =");
					if (value2 is int || value2 is short || value2 is float || value2 is long || value2 is double || value2 is decimal)
					{
						stringBuilder.AppendLine($" {value2} ");
					}
					else if (value2 is DateTime dateTime2)
					{
						stringBuilder.AppendLine(" '" + dateTime2.ToString("yyyy-MM-dd HH:mm:ss") + "'");
					}
					else
					{
						stringBuilder.AppendLine($" '{value2}'");
					}
				}
			}
			if (!(typeFlag == "business"))
			{
				if (typeFlag == "skill")
				{
					stringBuilder.AppendLine(" AND RELATED_KEY IN('jishuZip','jishuBidOpenZip','jishuPriceZip') OR RELATED_KEY ='JSZCWJ' ");
				}
			}
			else
			{
				stringBuilder.AppendLine(" AND RELATED_KEY IN('shangwuZip') OR RELATED_KEY ='SWZCWJ' ");
			}
			stringBuilder.AppendLine(" ) ");
			return stringBuilder.ToString();
		}

		public static bool CheckMakeBidExists(params string[] ingoreColumnNames)
		{
			string[] array = new string[2] { "NEED_DEPOSIT", "NEED_RESPONSE" };
			if (ingoreColumnNames != null && ingoreColumnNames.Length != 0)
			{
				array = array.Concat(ingoreColumnNames).ToArray();
			}
			return CheckModelExists(array);
		}

		private static bool CheckModelExists(params string[] ingoreColumnNames)
		{
			Type typeFromHandle = typeof(OsZbPurchaseProjectInfo);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			TableFieldAttribute tableFieldAttribute = null;
			HashSet<string> hashSet = new HashSet<string>();
			FieldInfo[] array = fields;
			foreach (FieldInfo field in array)
			{
				TableFieldAttribute tableFieldAttribute2 = (tableFieldAttribute = field.GetCustomAttribute<TableFieldAttribute>());
				if (tableFieldAttribute2 != null && tableFieldAttribute2.IsExist && tableFieldAttribute.ColumName.StartsWith("NEED_") && !(ingoreColumnNames?.Contains(tableFieldAttribute.ColumName) ?? false))
				{
					hashSet.Add(tableFieldAttribute.ColumName + " IN ('是', 'YES','Y')");
				}
			}
			PropertyInfo[] array2 = properties;
			foreach (PropertyInfo field2 in array2)
			{
				TableFieldAttribute tableFieldAttribute3 = (tableFieldAttribute = field2.GetCustomAttribute<TableFieldAttribute>());
				if (tableFieldAttribute3 != null && tableFieldAttribute3.IsExist && tableFieldAttribute.ColumName.StartsWith("NEED_") && !(ingoreColumnNames?.Contains(tableFieldAttribute.ColumName) ?? false))
				{
					hashSet.Add(tableFieldAttribute.ColumName + " IN ('是', 'YES','Y')");
				}
			}
			StringBuilder stringBuilder = new StringBuilder("select count(ID) as count from OS_ZB_PURCHASE_PROJECT_INFO  WHERE SELECTED = '1' AND (\r\n ");
			stringBuilder.AppendLine(string.Join(" OR ", (IEnumerable<string>)hashSet) ?? "");
			stringBuilder.AppendLine(" ) limit 0,1");
			string selectSql = stringBuilder.ToString();
			return SQLiteLibrary.SelectFirstValue<int>(sqliteDbLocation, sqliteDbName, selectSql) > 0;
		}

		public static bool CheckNeedModelExists(params string[] columnNames)
		{
			StringBuilder stringBuilder = new StringBuilder("select count(ID) as count from OS_ZB_PURCHASE_PROJECT_INFO  WHERE SELECTED = '1' AND (\r\n ");
			HashSet<string> hashSet = new HashSet<string>();
			foreach (string text in columnNames)
			{
				hashSet.Add(text + " IN ('是', 'YES','Y')");
			}
			stringBuilder.AppendLine(string.Join(" OR ", (IEnumerable<string>)hashSet) ?? "");
			stringBuilder.AppendLine(" ) limit 0,1");
			string selectSql = stringBuilder.ToString();
			return SQLiteLibrary.SelectFirstValue<bool>(sqliteDbLocation, sqliteDbName, selectSql);
		}

		public static string CreateMergeSql<T>(T t, string typeFlag, string asliaName = "db2", params Expression<Func<T, object>>[] whereColums)
		{
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			Type typeFromHandle = typeof(T);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic;
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			StringBuilder stringBuilder = new StringBuilder("insert into " + asliaName + "." + text + "  ");
			stringBuilder.AppendLine("\tSELECT * FROM " + text + " WHERE 1=1  ");
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			TableFieldAttribute tableFieldAttribute = null;
			Dictionary<string, object> dictionary2 = new Dictionary<string, object>();
			foreach (Expression<Func<T, object>> expression in whereColums)
			{
				string key = expression.Body.ToString().Split('.')[1].ToUpper();
				dictionary2.Add(key, expression.Compile().Invoke(t));
			}
			for (int j = 0; j < fields.Length; j++)
			{
				tableFieldAttribute = fields[j].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist && dictionary2.TryGetValue(fields[j].Name.ToUpper(), out var value) && !string.IsNullOrWhiteSpace(value?.ToString()))
				{
					stringBuilder.AppendLine(" AND " + tableFieldAttribute.ColumName + " =");
					if (value is int || value is short || value is float || value is long || value is double || value is decimal)
					{
						stringBuilder.AppendLine($" {value} ");
					}
					else if (value is DateTime dateTime)
					{
						stringBuilder.AppendLine(" '" + dateTime.ToString("yyyy-MM-dd HH:mm:ss") + "'");
					}
					else
					{
						stringBuilder.AppendLine($" '{value}'");
					}
				}
			}
			for (int k = 0; k < properties.Length; k++)
			{
				tableFieldAttribute = properties[k].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist && dictionary2.TryGetValue(properties[k].Name.ToUpper(), out var value2) && !string.IsNullOrWhiteSpace(value2?.ToString()))
				{
					stringBuilder.AppendLine(" AND " + tableFieldAttribute.ColumName + " =");
					if (value2 is int || value2 is short || value2 is float || value2 is long || value2 is double || value2 is decimal)
					{
						stringBuilder.AppendLine($" {value2} ");
					}
					else if (value2 is DateTime dateTime2)
					{
						stringBuilder.AppendLine(" '" + dateTime2.ToString("yyyy-MM-dd HH:mm:ss") + "'");
					}
					else
					{
						stringBuilder.AppendLine($" '{value2}'");
					}
				}
			}
			if (!(typeFlag == "business"))
			{
				if (typeFlag == "skill")
				{
					stringBuilder.AppendLine(" AND RELATED_KEY IN('jishuZip','jishuBidOpenZip','jishuPriceZip')  ");
				}
			}
			else
			{
				stringBuilder.AppendLine(" AND RELATED_KEY IN('shangwuZip')");
			}
			stringBuilder.AppendLine(" ; ");
			return stringBuilder.ToString();
		}
	}
	public static class SQLiteSqlUtils
	{
		private static readonly Dictionary<SqlFunctionEnum, string> sqlFuncDict = new Dictionary<SqlFunctionEnum, string>
		{
			{
				SqlFunctionEnum.None,
				null
			},
			{
				SqlFunctionEnum.Ave,
				" AVE(#[col]) AVE_0 "
			},
			{
				SqlFunctionEnum.Sum,
				" SUM(#[col]) SUM_0 "
			},
			{
				SqlFunctionEnum.Max,
				" MAX(#[col]) MAX_0 "
			},
			{
				SqlFunctionEnum.Min,
				" MIN(#[col]) MIN_0 "
			},
			{
				SqlFunctionEnum.Count,
				" COUNT(0) COUNT_0 "
			},
			{
				SqlFunctionEnum.PlaceHolder,
				" #[placeHolder] "
			}
		};

		private static readonly Dictionary<Type, DbType> entityDbMap = new Dictionary<Type, DbType>
		{
			{
				typeof(string),
				DbType.String
			},
			{
				typeof(int),
				DbType.Int32
			},
			{
				typeof(long),
				DbType.Int64
			},
			{
				typeof(int?),
				DbType.Int32
			},
			{
				typeof(long?),
				DbType.Int64
			},
			{
				typeof(decimal?),
				DbType.Decimal
			},
			{
				typeof(double?),
				DbType.Double
			},
			{
				typeof(decimal),
				DbType.Decimal
			},
			{
				typeof(double),
				DbType.Double
			},
			{
				typeof(DateTime),
				DbType.DateTime
			},
			{
				typeof(DateTimeOffset),
				DbType.DateTimeOffset
			},
			{
				typeof(byte),
				DbType.Byte
			},
			{
				typeof(byte[]),
				DbType.Binary
			}
		};

		private static readonly Regex removePattern = new Regex("[\\(\\（\\[\\[#\\)\\）]{1,6}");

		public static string CreateSelectSql<T>(T t, string[] whereColums) where T : CommonEntity
		{
			return CreateSelectSql(t, whereColums, null, null);
		}

		public static string CreateSelectSql<T>(T t, string[] whereColums, string joinSql, string joinWhereCon) where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			joinSql = ((joinSql == null) ? "" : joinSql);
			joinWhereCon = ((joinWhereCon == null) ? "" : joinWhereCon);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			string text2 = "SELECT #{colums} FROM " + text + " \n " + joinSql + " WHERE 1=1 #{whereColums} " + joinWhereCon;
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			StringBuilder stringBuilder = new StringBuilder();
			StringBuilder stringBuilder2 = new StringBuilder("");
			int num = 0;
			TableFieldAttribute tableFieldAttribute = null;
			PropertyInfo propertyInfo = null;
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			for (int i = 0; i < allMembers.Length; i++)
			{
				tableFieldAttribute = allMembers[i].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist)
				{
					dictionary[allMembers[i].Name.ToUpper()] = tableFieldAttribute;
					stringBuilder.Append(((num == 0) ? "" : ",") + text + "." + tableFieldAttribute.ColumName);
					num++;
				}
			}
			if (whereColums != null)
			{
				for (int j = 0; j < whereColums.Length; j++)
				{
					propertyInfo = typeFromHandle.GetProperty(UpperCaseFirst(whereColums[j]));
					if (propertyInfo != (PropertyInfo)null)
					{
						TableFieldAttribute tableFieldAttribute2 = dictionary[whereColums[j].ToUpper()];
						stringBuilder2.Append(" and " + ((tableFieldAttribute2 == null) ? "NoField" : (text + "." + tableFieldAttribute2.ColumName)) + "='" + propertyInfo.GetValue(t)?.ToString() + "'");
					}
				}
			}
			return text2.Replace("#{colums}", stringBuilder.ToString()).Replace("#{whereColums}", stringBuilder2.ToString());
		}

		public static string CreateDeleteSql<T>(T t, string[] whereColums) where T : CommonEntity
		{
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			Dictionary<string, object> dictionary2 = new Dictionary<string, object>();
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			string text2 = "DELETE FROM " + text + " WHERE 1=1 #{whereColums}";
			StringBuilder stringBuilder = new StringBuilder("");
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			for (int i = 0; i < allMembers.Length; i++)
			{
				TableFieldAttribute customAttribute2 = allMembers[i].GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute2 != null && customAttribute2.IsExist)
				{
					dictionary2[allMembers[i].Name.ToUpper()] = ((allMembers[i] is FieldInfo) ? ((FieldInfo)allMembers[i]).GetValue(t) : ((PropertyInfo)allMembers[i]).GetValue(t));
					dictionary[allMembers[i].Name.ToUpper()] = customAttribute2;
				}
			}
			if (whereColums != null)
			{
				for (int j = 0; j < whereColums.Length; j++)
				{
					string key = whereColums[j].ToUpper();
					if (dictionary.ContainsKey(key))
					{
						TableFieldAttribute tableFieldAttribute = dictionary[key];
						stringBuilder.Append(" and " + ((tableFieldAttribute == null) ? "NoField" : tableFieldAttribute.ColumName) + "='" + dictionary2[key]?.ToString() + "'");
					}
				}
			}
			return text2.Replace("#{whereColums}", stringBuilder.ToString());
		}

		public static string CreateDeleteSql<T, IIList>(IIList list, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity where IIList : IList<T>
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (T item in list)
			{
				stringBuilder.AppendLine(SQLiteSqlUtils.CreateDeleteSql<T>(item, whereColums) + ";");
			}
			return stringBuilder.ToString().TrimEnd(';', ' ');
		}

		public static string CreateDeleteSql<T>(T t, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity
		{
			StringBuilder stringBuilder = new StringBuilder("");
			ColumService columService = new ColumServiceImpl();
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			Type typeFromHandle = typeof(T);
			MemberInfo[] members = typeFromHandle.GetMembers(bindingAttr);
			Dictionary<string, Expression<Func<T, object>>> dictionary3 = whereColums.ToDictionary<Expression<Func<T, object>>, string, Expression<Func<T, object>>>((Expression<Func<T, object>> p) => GetLastExpression(p), (Expression<Func<T, object>> p) => p);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			StringBuilder stringBuilder2 = new StringBuilder("DELETE  FROM " + customAttribute.Value + " WHERE ");
			Type typeFromHandle2 = typeof(TableFieldAttribute);
			MemberInfo[] array = members;
			foreach (MemberInfo memberInfo in array)
			{
				TableFieldAttribute tableFieldAttribute = (TableFieldAttribute)memberInfo.GetCustomAttributes(typeFromHandle2, inherit: true).FirstOrDefault();
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist)
				{
					string key = memberInfo.Name.ToUpper();
					if (dictionary3.ContainsKey(key))
					{
						stringBuilder2.Append($" {tableFieldAttribute.ColumName} = {FormatValue(dictionary3[key].Compile().Invoke(t))} AND");
					}
				}
			}
			return stringBuilder2.ToString().TrimEnd('A', 'N', 'D', ' ');
		}

		public static string CreateInsertSql<T>(T t) where T : CommonEntity
		{
			List<T> list = new List<T>();
			list.Add(t);
			return CreateInsertSql((IList<T>)list, (ColumService)new ColumServiceImpl());
		}

		public static string CreateInsertSql<T>(List<T> list) where T : CommonEntity
		{
			return CreateInsertSql((IList<T>)list, (ColumService)new ColumServiceImpl());
		}

		public static string CreateInsertSql<T>(BindingList<T> list) where T : CommonEntity
		{
			return CreateInsertSql((IList<T>)list, (ColumService)new ColumServiceImpl());
		}

		public static string CreateInsertSql<T>(T t, ColumService columService) where T : CommonEntity
		{
			List<T> list = new List<T> { t };
			return CreateInsertSql((IList<T>)list, columService);
		}

		public static string CreateInsertSql<T>(IList<T> list, ColumService columService) where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			DateTime now = DateTime.Now;
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			string text2 = "INSERT INTO " + text + "(#{colums}) VALUES\n";
			string text3 = "(#{values})";
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			Dictionary<string, PropertyInfo> dictionary2 = new Dictionary<string, PropertyInfo>();
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			string text4 = "";
			string text5 = "";
			for (int i = 0; i < allMembers.Length; i++)
			{
				dictionary2[allMembers[i].Name] = ((allMembers[i] is PropertyInfo) ? ((PropertyInfo)allMembers[i]) : typeFromHandle.GetProperty(UpperCaseFirst(allMembers[i].Name), bindingAttr));
				TableFieldAttribute customAttribute2 = allMembers[i].GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute2 != null)
				{
					dictionary[allMembers[i].Name] = customAttribute2;
					customAttribute2.JdbcType = allMembers[i].MemberType.ToString();
					if (customAttribute2.IsExist)
					{
						text4 = text4 + " " + customAttribute2.ColumName + " ,";
						text5 = text5 + " #{" + allMembers[i].Name + "} ,";
					}
				}
			}
			text2 = text2.Replace("#{colums}", text4.Substring(0, text4.Length - 1));
			text3 = text3.Replace("#{values}", text5.Substring(0, text5.Length - 1));
			string text6 = "";
			string newValue = "";
			for (int j = 0; j < list.Count(); j++)
			{
				object obj = "";
				string text7 = "";
				string text8 = text3;
				foreach (string key in dictionary.Keys)
				{
					PropertyInfo propertyInfo = dictionary2[key];
					TableFieldAttribute tableFieldAttribute = dictionary[key];
					if (tableFieldAttribute.Value != null)
					{
						obj = tableFieldAttribute.Value;
						text7 = obj.ToString();
						newValue = text7;
					}
					else if (propertyInfo != (PropertyInfo)null)
					{
						obj = dictionary2[key].GetValue(list[j], null);
						text7 = ((obj == null) ? "null" : ((obj is DateTime dateTime) ? ("'" + dateTime.ToString("yyyy-MM-dd HH:mm:ss") + "'") : ("'" + obj.ToString() + "'")));
						newValue = (tableFieldAttribute.DefinedFunc ? columService.DoFormat(key, text7, tableFieldAttribute.PatternStr, j) : text7);
					}
					text8 = text8.Replace("#{" + key + "}", newValue);
				}
				text6 = text6 + text8 + ",";
			}
			Console.WriteLine("insert语句生成耗时{0}ms.", DateTime.Now.Subtract(now).TotalMilliseconds);
			return text2 + text6.Substring(0, text6.Length - 1);
		}

		public static string CreateUpdateSql<T>(T t, string[] whereColums) where T : CommonEntity
		{
			List<T> list = new List<T> { t };
			return CreateUpdateSql(list, whereColums, null, new ColumServiceImpl());
		}

		public static string CreateUpdateSql<T>(T t, string[] selectColumns, string[] whereColums) where T : CommonEntity
		{
			List<T> list = new List<T>();
			list.Add(t);
			return CreateUpdateSql(list, whereColums, selectColumns, null);
		}

		public static string CreateUpdateSql<T>(T t, string[] whereColums, ColumService columService) where T : CommonEntity
		{
			List<T> list = new List<T>();
			list.Add(t);
			return CreateUpdateSql(list, whereColums, null, columService);
		}

		public static string CreateUpdateSql<T>(List<T> list, string[] whereColums) where T : CommonEntity
		{
			return CreateUpdateSql(list, whereColums, null, new ColumServiceImpl());
		}

		public static string CreateSelectUpdateSql<T>(T t, string[] selectColumns, string[] whereColums) where T : CommonEntity
		{
			List<T> list = new List<T> { t };
			return CreateUpdateSql(list, whereColums, selectColumns, null);
		}

		public static string CreateSelectUpdateSql<T>(List<T> list, string[] selectColumns, string[] whereColums) where T : CommonEntity
		{
			return CreateUpdateSql(list, whereColums, selectColumns, null);
		}

		public static string CreateUpdateSql<T>(List<T> list, string[] whereColums, string[] selectColums, ColumService columService) where T : CommonEntity
		{
			StringBuilder stringBuilder = new StringBuilder("");
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
			if (whereColums != null)
			{
				dictionary = whereColums.ToDictionary<string, string, string>((string p) => p.ToUpper(), (string p) => "");
			}
			if (selectColums != null)
			{
				dictionary2 = selectColums.ToDictionary<string, string, string>((string p) => p.ToUpper(), (string p) => "");
			}
			DateTime now = DateTime.Now;
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			string text2 = "";
			string text3 = "UPDATE " + text + " #{colums} ";
			Dictionary<string, PropertyInfo> dictionary3 = new Dictionary<string, PropertyInfo>();
			Dictionary<string, TableFieldAttribute> dictionary4 = new Dictionary<string, TableFieldAttribute>();
			TableFieldAttribute tableFieldAttribute = null;
			TableFieldAttribute tableFieldAttribute2 = null;
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			string text4 = "";
			for (int i = 0; i < allMembers.Length; i++)
			{
				tableFieldAttribute2 = allMembers[i].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute2 != null)
				{
					dictionary3[allMembers[i].Name] = ((allMembers[i] is PropertyInfo) ? ((PropertyInfo)allMembers[i]) : typeFromHandle.GetProperty(UpperCaseFirst(allMembers[i].Name), bindingAttr));
					dictionary4[allMembers[i].Name] = tableFieldAttribute2;
					if (dictionary2.Count > 0 && !dictionary2.ContainsKey(allMembers[i].Name.ToUpper()))
					{
						tableFieldAttribute2.IsUpdate = false;
					}
					tableFieldAttribute2.JdbcType = allMembers[i].MemberType.ToString();
					if (tableFieldAttribute2.IsExist && !tableFieldAttribute2.PrimaryKey && tableFieldAttribute2.IsUpdate)
					{
						text4 = text4 + " " + tableFieldAttribute2.ColumName + "= #{" + allMembers[i].Name + "} ,";
					}
					if (dictionary.ContainsKey(allMembers[i].Name.ToUpper()))
					{
						stringBuilder.Append(" AND " + tableFieldAttribute2.ColumName + " = #{" + allMembers[i].Name + "}");
					}
				}
			}
			text4 = "set " + text4.Substring(0, text4.Length - 1);
			if (dictionary.Count > 0)
			{
				text4 = text4 + " WHERE 1=1 " + stringBuilder.ToString();
			}
			string text5 = "";
			object obj = "";
			string text6 = "";
			PropertyInfo propertyInfo = null;
			for (int j = 0; j < list.Count; j++)
			{
				text5 = text4;
				foreach (string key in dictionary4.Keys)
				{
					propertyInfo = dictionary3[key];
					tableFieldAttribute = dictionary4[key];
					if (tableFieldAttribute.Value != null)
					{
						obj = tableFieldAttribute.Value;
						text6 = obj.ToString();
					}
					else if (propertyInfo != (PropertyInfo)null)
					{
						obj = dictionary3[key].GetValue(list[j], null);
						text6 = ((obj == null) ? "''" : ("'" + obj.ToString() + "'"));
						if (!tableFieldAttribute.PrimaryKey)
						{
							text6 = (tableFieldAttribute.DefinedFunc ? columService.DoFormat(key, text6, tableFieldAttribute.PatternStr, j) : text6);
						}
					}
					else
					{
						text6 = "''";
					}
					text5 = text5.Replace("#{" + key + "}", text6);
				}
				text2 = text2 + text3.Replace("#{colums}", text5) + "\n;";
			}
			text2 = text2.Substring(0, text2.Length - 1);
			Console.WriteLine("update语句生成耗时{0}ms.", DateTime.Now.Subtract(now).TotalMilliseconds);
			return text2;
		}

		internal static MemberInfo[] GetAllMembers(Type type)
		{
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			Type type2 = type?.BaseType;
			Type type3 = type2?.BaseType;
			if (type == (Type)null)
			{
				return null;
			}
			MemberInfo[] array = type3?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] array2 = type2?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] array3 = type?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			Dictionary<string, MemberInfo> dictionary = new Dictionary<string, MemberInfo>();
			MemberInfo[] array4 = array;
			foreach (MemberInfo memberInfo in array4)
			{
				if (memberInfo is FieldInfo || memberInfo is PropertyInfo)
				{
					dictionary[memberInfo.Name] = memberInfo;
				}
			}
			MemberInfo[] array5 = array2;
			foreach (MemberInfo memberInfo2 in array5)
			{
				if (memberInfo2 is FieldInfo || memberInfo2 is PropertyInfo)
				{
					dictionary[memberInfo2.Name] = memberInfo2;
				}
			}
			MemberInfo[] array6 = array3;
			foreach (MemberInfo memberInfo3 in array6)
			{
				if (memberInfo3 is FieldInfo || memberInfo3 is PropertyInfo)
				{
					dictionary[memberInfo3.Name] = memberInfo3;
				}
			}
			return ((IEnumerable<KeyValuePair<string, MemberInfo>>)dictionary).Select<KeyValuePair<string, MemberInfo>, MemberInfo>((Func<KeyValuePair<string, MemberInfo>, MemberInfo>)((KeyValuePair<string, MemberInfo> p) => p.Value)).ToArray();
		}

		public static string CreateInsertSqlByDataTable(DataTable dataTable, string primaryKey, params string[] ignoreColumns)
		{
			StringBuilder stringBuilder = new StringBuilder("insert into " + dataTable.TableName + " (#[columns]#) values");
			DataRowCollection rows = dataTable.Rows;
			DataColumnCollection columns = dataTable.Columns;
			List<string> list = new List<string>();
			Dictionary<string, string> dictionary = ignoreColumns.ToDictionary<string, string, string>((string p) => p, (string p) => "");
			if (primaryKey != null)
			{
				list.Add(primaryKey);
			}
			for (int i = 0; i < columns.Count; i++)
			{
				if (!dictionary.ContainsKey(columns[i].ColumnName))
				{
					list.Add(columns[i].ColumnName);
				}
			}
			stringBuilder = stringBuilder.Replace("#[columns]#", string.Join(",", (IEnumerable<string>)list));
			for (int j = 0; j < rows.Count; j++)
			{
				List<string> list2 = new List<string>();
				stringBuilder.Append(" (");
				if (primaryKey != null)
				{
					list2.Add("'" + Guid.NewGuid().ToString("N") + "'");
				}
				for (int k = 0; k < columns.Count; k++)
				{
					if (!dictionary.ContainsKey(columns[k].ColumnName))
					{
						Type type = rows[j].ItemArray[k].GetType();
						string text = type.ToString();
						if (text.Contains("System.Nullable"))
						{
							list2.Add("NULL");
							continue;
						}
						if (rows[j].ItemArray[k] is DateTime dateTime)
						{
							list2.Add("'" + dateTime.ToString("yyyy-MM-dd HH:mm:ss").Replace(" 00:00:00", "") + "'");
							continue;
						}
						string text2 = rows[j].ItemArray[k].ToString();
						list2.Add("'" + text2 + "'");
					}
				}
				stringBuilder.Append(string.Join(",", (IEnumerable<string>)list2) + "),");
			}
			return stringBuilder.ToString().Substring(0, stringBuilder.Length - 1);
		}

		public static string UpperCaseFirst(string str)
		{
			if (string.IsNullOrEmpty(str))
			{
				return string.Empty;
			}
			char[] array = str.ToCharArray();
			char c = array[0];
			if ('a' <= c && c <= 'z')
			{
				c = (char)(c & 0xFFFFFFDFu);
			}
			array[0] = c;
			return new string(array);
		}

		public static string CreateAndDropTable<T>() where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			Type typeFromHandle2 = typeof(TableFieldAttribute);
			Type typeFromHandle3 = typeof(ColumnInfoAttribute);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			FieldInfo[] fields = typeFromHandle.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
			List<MemberInfo> list = new List<MemberInfo>(fields.Length + properties.Length);
			list.AddRange(fields);
			list.AddRange(properties);
			string text = ((typeFromHandle.GetCustomAttributes(typeof(TableNameAttribute), inherit: false).FirstOrDefault() is TableNameAttribute tableNameAttribute) ? tableNameAttribute.Value : null) ?? "NOT_FIND_TABLE";
			List<ColumnInfoAttribute> list2 = new List<ColumnInfoAttribute>();
			string text2 = "";
			bool flag = false;
			for (int i = 0; i < list.Count; i++)
			{
				TableFieldAttribute tableFieldAttribute = list[i].GetCustomAttributes(typeFromHandle2, inherit: false).FirstOrDefault() as TableFieldAttribute;
				ColumnInfoAttribute columnInfoAttribute = (list[i].GetCustomAttributes(typeFromHandle3, inherit: false).FirstOrDefault() as ColumnInfoAttribute) ?? new ColumnInfoAttribute(i, 255);
				if (tableFieldAttribute != null && tableFieldAttribute.IsExist)
				{
					if (list[i].MemberType == MemberTypes.Property)
					{
						text2 = (((list[i] as PropertyInfo).PropertyType != typeof(int)) ? (tableFieldAttribute.ColumName + "  TEXT") : (tableFieldAttribute.ColumName + "  INT(16)"));
					}
					else if (list[i].MemberType == MemberTypes.Field)
					{
						text2 = (((list[i] as FieldInfo).FieldType != typeof(int)) ? (tableFieldAttribute.ColumName + "  TEXT") : (tableFieldAttribute.ColumName + "  INT(16)"));
					}
					columnInfoAttribute.Pattern = ("Id".Equals(list[i].Name, StringComparison.CurrentCultureIgnoreCase) ? (tableFieldAttribute.ColumName + " TEXT PRIMARY KEY NOT NULL") : text2);
					list2.Add(columnInfoAttribute);
				}
			}
			IEnumerable<string> enumerable = ((IEnumerable<ColumnInfoAttribute>)list2.OrderBy<ColumnInfoAttribute, int>((ColumnInfoAttribute item) => item.Index)).Select<ColumnInfoAttribute, string>((Func<ColumnInfoAttribute, string>)((ColumnInfoAttribute item) => item.Pattern));
			return "drop table  IF EXISTS " + text + "; \r\n create table " + text + "(" + string.Join(",", enumerable) + ");";
		}

		public static string CreateSelectSql2<T>(T t, string selectColumnSql, string joinTableSql, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity
		{
			return CreateSelectSql2(t, selectColumnSql, joinTableSql, null, whereColums);
		}

		public static string CreateSelectSql2<T>(T t, string selectColumnSql, string joinTableSql, Expression<Func<T, object>>[] columns = null, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity
		{
			return CreateSelectSql2(t, selectColumnSql, joinTableSql, groupById: true, columns, whereColums);
		}

		public static string CreateSelectSql2<T>(T t, string selectColumnSql, string joinTableSql, bool groupById = true, Expression<Func<T, object>>[] columns = null, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			List<TableFieldAttribute> allTableFields = GetAllTableFields(typeFromHandle);
			StringBuilder stringBuilder = new StringBuilder("select ");
			int num = 0;
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			foreach (TableFieldAttribute item in allTableFields)
			{
				if (item == null)
				{
					continue;
				}
				if (columns == null || columns.Length == 0)
				{
					if (num++ != 0)
					{
						stringBuilder.Append(",");
					}
					stringBuilder.Append(text + "." + item.ColumName);
				}
				try
				{
					dictionary.Add(item.Value, item);
				}
				catch (Exception)
				{
					Console.WriteLine();
				}
			}
			if (columns != null && columns.Length != 0)
			{
				num = 0;
				for (int i = 0; i < columns.Length; i++)
				{
					try
					{
						string lastExpression = GetLastExpression(columns[i]);
						if (dictionary.TryGetValue(lastExpression, out var value))
						{
							if (num++ != 0)
							{
								stringBuilder.Append(",");
							}
							stringBuilder.Append(text + "." + value.ColumName);
						}
					}
					catch (Exception)
					{
						throw;
					}
				}
			}
			if (!string.IsNullOrWhiteSpace(selectColumnSql))
			{
				stringBuilder.Append("," + selectColumnSql + "\n");
			}
			stringBuilder.Append(" FROM " + text + "\n");
			if (!string.IsNullOrWhiteSpace(joinTableSql))
			{
				stringBuilder.Append(joinTableSql);
			}
			stringBuilder.Append("\n WHERE 1=1 ");
			for (int j = 0; j < whereColums.Length; j++)
			{
				try
				{
					object obj = whereColums[j].Compile().Invoke(t);
					string text2 = (obj ?? "").ToString();
					string lastExpression = GetLastExpression(whereColums[j]);
					if (obj is IEnumerable<string> enumerable)
					{
						StringBuilder stringBuilder2 = new StringBuilder();
						foreach (string item2 in enumerable)
						{
							stringBuilder2.Append(item2.StartsWith("'") ? (item2 + ",") : ("'" + item2 + "',"));
						}
						text2 = stringBuilder2.ToString().TrimEnd(',');
					}
					if (!string.IsNullOrWhiteSpace(text2) && dictionary.ContainsKey(lastExpression))
					{
						stringBuilder.Append("  AND " + text + "." + dictionary[lastExpression].ColumName + "= '" + text2 + "' ");
					}
				}
				catch (Exception)
				{
					throw;
				}
			}
			if (groupById)
			{
				stringBuilder.Append(" group by  " + text + ".ID ");
			}
			return stringBuilder.ToString();
		}

		private static List<TableFieldAttribute> GetAllTableFields(Type type)
		{
			if (type == (Type)null)
			{
				return new List<TableFieldAttribute>();
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] array = type.BaseType?.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] array2 = type.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] members = type.GetMembers(bindingAttr);
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			TableFieldAttribute tableFieldAttribute = null;
			MemberInfo[] array3 = array;
			foreach (MemberInfo memberInfo in array3)
			{
				tableFieldAttribute = memberInfo.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			MemberInfo[] array4 = array2;
			foreach (MemberInfo memberInfo2 in array4)
			{
				tableFieldAttribute = memberInfo2.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo2.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			MemberInfo[] array5 = members;
			foreach (MemberInfo memberInfo3 in array5)
			{
				tableFieldAttribute = memberInfo3.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo3.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			return ((IEnumerable<KeyValuePair<string, TableFieldAttribute>>)dictionary).Select<KeyValuePair<string, TableFieldAttribute>, TableFieldAttribute>((Func<KeyValuePair<string, TableFieldAttribute>, TableFieldAttribute>)((KeyValuePair<string, TableFieldAttribute> x) => x.Value)).ToList();
		}

		public static string CreateUpdateSqlV2<T>(IList<T> list, string[] selectColums, string[] whereColums) where T : CommonEntity
		{
			StringBuilder stringBuilder = new StringBuilder("");
			ColumService columService = new ColumServiceImpl();
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
			if (whereColums != null)
			{
				dictionary = whereColums.ToDictionary<string, string, string>((string p) => p.ToUpper(), (string p) => "");
			}
			if (selectColums != null)
			{
				dictionary2 = selectColums.ToDictionary<string, string, string>((string p) => p.ToUpper(), (string p) => "");
			}
			DateTime now = DateTime.Now;
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			Dictionary<string, TableFieldAttribute> dictionary3 = new Dictionary<string, TableFieldAttribute>();
			Dictionary<string, PropertyInfo> dictionary4 = new Dictionary<string, PropertyInfo>();
			PropertyInfo propertyInfo = null;
			TableFieldAttribute tableFieldAttribute = null;
			TableFieldAttribute tableFieldAttribute2 = null;
			StringBuilder stringBuilder2 = new StringBuilder("UPDATE " + text + " SET");
			StringBuilder stringBuilder3 = new StringBuilder(" WHERE 1=1 ");
			int num = 0;
			for (int i = 0; i < fields.Length; i++)
			{
				dictionary4.Add(fields[i].Name, typeFromHandle.GetProperty(UpperCaseFirst(fields[i].Name), bindingAttr));
				tableFieldAttribute2 = fields[i].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute2 == null)
				{
					continue;
				}
				dictionary3.Add(fields[i].Name, tableFieldAttribute2);
				if (tableFieldAttribute2.IsUpdate && dictionary2.ContainsKey(fields[i].Name.ToUpper()))
				{
					if (num++ != 0)
					{
						stringBuilder2.Append(" ,");
					}
					stringBuilder2.Append(" " + tableFieldAttribute2.ColumName + " =@" + fields[i].Name);
				}
				if (dictionary.ContainsKey(fields[i].Name.ToUpper()))
				{
					stringBuilder3.Append(" AND " + tableFieldAttribute2.ColumName + " =@" + fields[i].Name);
				}
			}
			num = 0;
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			for (int j = 0; j < properties.Length; j++)
			{
				dictionary4.Add(properties[j].Name, properties[j]);
				tableFieldAttribute2 = properties[j].GetCustomAttribute<TableFieldAttribute>();
				if (tableFieldAttribute2 == null)
				{
					continue;
				}
				dictionary3.Add(properties[j].Name, tableFieldAttribute2);
				if (tableFieldAttribute2.IsUpdate && dictionary2.ContainsKey(properties[j].Name.ToUpper()))
				{
					if (num++ != 0)
					{
						stringBuilder2.Append(" ,");
					}
					stringBuilder2.Append(" " + tableFieldAttribute2.ColumName + " =@" + properties[j].Name);
				}
				if (dictionary.ContainsKey(properties[j].Name.ToUpper()))
				{
					stringBuilder3.Append(" AND " + tableFieldAttribute2.ColumName + " =@" + properties[j].Name);
				}
			}
			stringBuilder2 = stringBuilder2.Append(stringBuilder3);
			string text2 = "";
			StringBuilder stringBuilder4 = new StringBuilder();
			string text3 = "";
			for (int k = 0; k < list.Count; k++)
			{
				text2 = stringBuilder2.ToString();
				foreach (string key in dictionary3.Keys)
				{
					propertyInfo = dictionary4[key];
					tableFieldAttribute = dictionary3[key];
					object value = dictionary4[key].GetValue(list[k], null);
					text3 = tableFieldAttribute.Value ?? value?.ToString();
					text3 = (string.IsNullOrEmpty(text3) ? "null" : ((!(value is DateTime dateTime) || 1 == 0) ? $"'{value}'" : ("'" + dateTime.ToString("yyyy-MM-dd HH:mm:ss") + "'")));
					if (!tableFieldAttribute.PrimaryKey && tableFieldAttribute.DefinedFunc)
					{
						text3 = columService?.DoFormat(key, text3, tableFieldAttribute.PatternStr, k);
					}
					text2 = text2.Replace("@" + key, text3);
				}
				if (k != 0)
				{
					stringBuilder4.Append(";");
				}
				stringBuilder4.Append(text2);
			}
			Console.WriteLine("update语句生成耗时{0}ms.", DateTime.Now.Subtract(now).TotalMilliseconds);
			return stringBuilder4.ToString();
		}

		public static string CreateCountSql<TEntity>(TEntity t, params Expression<Func<TEntity, object>>[] whereColums) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			TableFieldAttribute attr = null;
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			Dictionary<string, TableFieldAttribute> dictionary = ((IEnumerable<MemberInfo>)allMembers).Select<MemberInfo, TableFieldAttribute>((Func<MemberInfo, TableFieldAttribute>)delegate(MemberInfo p)
			{
				attr = p.GetCustomAttribute<TableFieldAttribute>();
				attr = ((attr == null || !attr.IsExist) ? null : attr);
				if (attr != null)
				{
					attr.Value = p.Name.ToUpper();
				}
				return attr;
			}).Where((Func<TableFieldAttribute, bool>)((TableFieldAttribute p) => p != null)).ToDictionaryByLocal<TableFieldAttribute, string, TableFieldAttribute>((TableFieldAttribute p) => p.Value, (TableFieldAttribute p) => p);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			StringBuilder stringBuilder = new StringBuilder("select count(0) FROM " + text + "\n");
			stringBuilder.Append("\n WHERE 1=1 ");
			for (int i = 0; i < whereColums.Length; i++)
			{
				try
				{
					string text2 = (whereColums[i].Compile().Invoke(t) ?? "").ToString();
					string lastExpression = GetLastExpression(whereColums[i]);
					if (!string.IsNullOrWhiteSpace(text2) && dictionary.ContainsKey(lastExpression))
					{
						stringBuilder.Append("  AND " + text + "." + dictionary[lastExpression].ColumName + "= '" + text2 + "' ");
					}
				}
				catch (Exception ex)
				{
					throw ex;
				}
			}
			return stringBuilder.ToString();
		}

		public static string CreateUpdateSqlByEntity<T, IIList>(IIList list, Expression<Func<T, object>>[] columns = null, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity where IIList : IList<T>
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (T item in list)
			{
				string text = SQLiteSqlUtils.CreateUpdateSqlByEntity<T>(item, columns, whereColums);
				stringBuilder.Append(text + ";");
			}
			return stringBuilder.ToString().TrimEnd(';');
		}

		public static string CreateUpdateSqlByEntity<T>(T t, Expression<Func<T, object>>[] columns = null, params Expression<Func<T, object>>[] whereColums) where T : CommonEntity
		{
			StringBuilder stringBuilder = new StringBuilder("");
			ColumService columService = new ColumServiceImpl();
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			Dictionary<string, string> dictionary2 = new Dictionary<string, string>();
			Type typeFromHandle = typeof(T);
			Dictionary<string, Expression<Func<T, object>>> dictionary3 = columns.ToDictionary<Expression<Func<T, object>>, string, Expression<Func<T, object>>>((Expression<Func<T, object>> p) => GetLastExpression(p), (Expression<Func<T, object>> p) => p);
			Dictionary<string, Expression<Func<T, object>>> dictionary4 = whereColums.ToDictionary<Expression<Func<T, object>>, string, Expression<Func<T, object>>>((Expression<Func<T, object>> p) => GetLastExpression(p), (Expression<Func<T, object>> p) => p);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			StringBuilder stringBuilder2 = new StringBuilder("UPDATE " + customAttribute.Value + " SET ");
			StringBuilder stringBuilder3 = new StringBuilder(" WHERE ");
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			MemberInfo[] array = allMembers;
			foreach (MemberInfo memberInfo in array)
			{
				TableFieldAttribute customAttribute2 = memberInfo.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute2 != null && customAttribute2.IsExist)
				{
					string key = memberInfo.Name.ToUpper();
					if (dictionary3.ContainsKey(key) && !customAttribute2.PrimaryKey)
					{
						stringBuilder2.Append($"{customAttribute2.ColumName} = {FormatValue(dictionary3[key].Compile().Invoke(t))},");
					}
					if (dictionary4.ContainsKey(key))
					{
						stringBuilder3.Append($" {customAttribute2.ColumName} = {FormatValue(dictionary4[key].Compile().Invoke(t))} AND");
					}
				}
			}
			string value = stringBuilder2.ToString().TrimEnd(',', ' ');
			stringBuilder2.Clear();
			stringBuilder2.Append(value);
			stringBuilder2.Append(stringBuilder3.ToString().TrimEnd('A', 'N', 'D', ' '));
			return stringBuilder2.ToString();
		}

		private static object FormatValue(object value)
		{
			if (value == null)
			{
				return "null";
			}
			if (value is DateTime dateTime)
			{
				return "'" + dateTime.ToString("yyyy-MM-dd HH:mm:ss") + "'";
			}
			if (value is string text)
			{
				return "'" + text + "'";
			}
			return value;
		}

		public static string GenDataSync<T>(IEnumerable<string> ids, Guid guid, string _primaryKey = "ID", string _whereColumn = "ID") where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			IEnumerable<string> first = ((IEnumerable<FieldInfo>)fields).Where((Func<FieldInfo, bool>)delegate(FieldInfo p)
			{
				TableFieldAttribute customAttribute4 = p.GetCustomAttribute<TableFieldAttribute>();
				return customAttribute4 != null && !customAttribute4.PrimaryKey && customAttribute4.IsExist && !customAttribute4.ColumName.Equals(_whereColumn, StringComparison.CurrentCultureIgnoreCase);
			}).Select<FieldInfo, string>((Func<FieldInfo, string>)delegate(FieldInfo p)
			{
				TableFieldAttribute customAttribute3 = p.GetCustomAttribute<TableFieldAttribute>();
				return customAttribute3.ColumName ?? "";
			});
			IEnumerable<string> second = ((IEnumerable<PropertyInfo>)properties).Where((Func<PropertyInfo, bool>)delegate(PropertyInfo p)
			{
				TableFieldAttribute customAttribute2 = p.GetCustomAttribute<TableFieldAttribute>();
				return customAttribute2 != null && !customAttribute2.PrimaryKey && customAttribute2.IsExist && !customAttribute2.ColumName.Equals(_whereColumn, StringComparison.CurrentCultureIgnoreCase);
			}).Select<PropertyInfo, string>((Func<PropertyInfo, string>)delegate(PropertyInfo p)
			{
				TableFieldAttribute customAttribute = p.GetCustomAttribute<TableFieldAttribute>();
				return customAttribute.ColumName ?? "";
			});
			IEnumerable<string> enumerable = first.Union(second);
			string value = typeFromHandle.GetCustomAttribute<TableNameAttribute>().Value;
			string text = string.Join(",", enumerable);
			string text2 = guid.ToString("N").Substring(10, 11);
			string text3;
			if (!_primaryKey.Equals(_whereColumn))
			{
				text3 = "INSERT INTO " + value + " (" + _primaryKey + "," + text + "," + _whereColumn + ")";
				return text3 + " SELECT substring(" + _primaryKey + ",0,20)||'" + text2 + "' " + _primaryKey + "," + text + ",substring(" + _whereColumn + ",0,20)||'" + text2 + "' " + _whereColumn + " FROM " + value + " WHERE " + _whereColumn + " IN (" + string.Join(",", ids) + ")";
			}
			text3 = "INSERT INTO " + value + " (" + _primaryKey + "," + text + ")";
			return text3 + " SELECT substring(" + _primaryKey + ",0,20)||'" + text2 + "' " + _primaryKey + "," + text + " FROM " + value + " WHERE " + _whereColumn + " IN (" + string.Join(",", ids) + ")";
		}

		public static string CreateBinaryInsert<T>(IEnumerable<T> list, Dictionary<string, string> extraParams, out List<SQLiteParameter> paramters) where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			paramters = new List<SQLiteParameter>();
			StringBuilder stringBuilder = new StringBuilder();
			int num = 0;
			foreach (T item in list)
			{
				StringBuilder stringBuilder2 = new StringBuilder("INSERT INTO " + (customAttribute.Value ?? typeFromHandle.Name) + "(");
				StringBuilder stringBuilder3 = new StringBuilder(" VALUES(");
				int num2 = 0;
				MemberInfo[] array = allMembers;
				foreach (MemberInfo memberInfo in array)
				{
					TableFieldAttribute customAttribute2 = memberInfo.GetCustomAttribute<TableFieldAttribute>();
					if ((customAttribute2 != null && customAttribute2.IsExist) || (extraParams != null && extraParams.ContainsKey(memberInfo.Name)))
					{
						string text = ((extraParams != null && extraParams.ContainsKey(memberInfo.Name)) ? extraParams[memberInfo.Name] : customAttribute2.ColumName);
						if (num2 != 0)
						{
							stringBuilder2.Append(",");
							stringBuilder3.Append(",");
						}
						stringBuilder2.Append(text);
						stringBuilder3.Append($"@{text}{num}");
						num2++;
						DbType dbType = DbType.String;
						SQLiteParameter sQLiteParameter = null;
						if (memberInfo is FieldInfo fieldInfo && entityDbMap.ContainsKey(fieldInfo.FieldType))
						{
							sQLiteParameter = new SQLiteParameter($"@{text}{num}", ((FieldInfo)memberInfo).GetValue(item))
							{
								DbType = DbType.String
							};
							dbType = entityDbMap[fieldInfo.FieldType];
							sQLiteParameter.DbType = dbType;
						}
						else if (memberInfo is PropertyInfo propertyInfo && entityDbMap.ContainsKey(propertyInfo.PropertyType))
						{
							sQLiteParameter = new SQLiteParameter($"@{text}{num}", ((PropertyInfo)memberInfo).GetValue(item))
							{
								DbType = DbType.String
							};
							dbType = entityDbMap[propertyInfo.PropertyType];
							sQLiteParameter.DbType = dbType;
						}
						paramters.Add(sQLiteParameter);
					}
				}
				stringBuilder2.Append(" )");
				stringBuilder3.Append(" );");
				stringBuilder.Append(stringBuilder2);
				stringBuilder.Append(stringBuilder3);
				num++;
			}
			return stringBuilder.ToString();
		}

		public static string CreateBinaryUpdate<T>(IEnumerable<T> list, Expression<Func<T, object>>[] updateCols, Expression<Func<T, object>>[] whereCols, out List<SQLiteParameter> paramters) where T : CommonEntity
		{
			Type typeFromHandle = typeof(T);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			MemberInfo[] allMembers = GetAllMembers(typeFromHandle);
			paramters = new List<SQLiteParameter>();
			StringBuilder stringBuilder = new StringBuilder();
			StringBuilder stringBuilder2 = new StringBuilder("UPDATE " + (customAttribute.Value ?? typeFromHandle.Name) + " SET ");
			StringBuilder stringBuilder3 = new StringBuilder(" WHERE 1=1 ");
			Type typeFromHandle2 = typeof(T);
			Dictionary<string, Expression<Func<T, object>>> dictionary = updateCols.ToDictionary<Expression<Func<T, object>>, string, Expression<Func<T, object>>>((Expression<Func<T, object>> p) => GetLastExpression(p), (Expression<Func<T, object>> p) => p);
			Dictionary<string, Expression<Func<T, object>>> dictionary2 = whereCols.ToDictionary<Expression<Func<T, object>>, string, Expression<Func<T, object>>>((Expression<Func<T, object>> p) => GetLastExpression(p), (Expression<Func<T, object>> p) => p);
			MemberInfo[] array = allMembers;
			foreach (MemberInfo memberInfo in array)
			{
				TableFieldAttribute customAttribute2 = memberInfo.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute2 != null && customAttribute2.IsExist)
				{
					string text = memberInfo.Name.ToUpper();
					if (dictionary != null && dictionary.ContainsKey(text))
					{
						stringBuilder2.Append(customAttribute2.ColumName + "=" + text + "#count#,");
					}
					if (dictionary2.ContainsKey(text))
					{
						stringBuilder3.Append(" AND " + customAttribute2.ColumName + "=" + text + "#count#");
					}
				}
			}
			string value = stringBuilder2.ToString().TrimEnd(',') + "\n" + stringBuilder3.ToString() + ";";
			int num = 0;
			StringBuilder stringBuilder4 = new StringBuilder();
			foreach (T item in list)
			{
				StringBuilder stringBuilder5 = new StringBuilder(value);
				MemberInfo[] array2 = allMembers;
				foreach (MemberInfo memberInfo2 in array2)
				{
					TableFieldAttribute customAttribute3 = memberInfo2.GetCustomAttribute<TableFieldAttribute>();
					string text2 = memberInfo2.Name.ToUpper();
					Expression<Func<T, object>> value2 = null;
					if (dictionary.TryGetValue(text2, out value2) || dictionary2.TryGetValue(text2, out value2))
					{
						stringBuilder5.Replace("=" + text2 + "#count#", $"=@{text2}_{num}");
						object obj = value2.Compile().Invoke(item);
						SQLiteParameter sQLiteParameter = new SQLiteParameter($"@{text2}_{num}", obj)
						{
							DbType = DbType.String
						};
						if (obj != null && entityDbMap.ContainsKey(obj.GetType()))
						{
							sQLiteParameter.DbType = entityDbMap[obj.GetType()];
						}
						paramters.Add(sQLiteParameter);
					}
				}
				num++;
				stringBuilder4.AppendLine(stringBuilder5.ToString());
			}
			return stringBuilder4.ToString();
		}

		public static string GetLastExpression<T>(Expression<Func<T, object>> func) where T : CommonEntity
		{
			string[] array = func.Body.ToString().Split('.');
			string input = ((array.Length > 1) ? array[1] : array[0]);
			return removePattern.Replace(input, string.Empty).ToUpper();
		}

		public static string CreatePageTotalCountSql<TEntity>(SqlWrapper<TEntity> sqlWrapper) where TEntity : CommonEntity
		{
			string text = sqlWrapper.Build(SqlStrategy.Select, SqlFunctionEnum.PlaceHolder);
			StringBuilder stringBuilder = new StringBuilder("select count(0) count from (");
			if (sqlWrapper.TableDict.Count > 1)
			{
				ISet<string> extraColumns = sqlWrapper.ExtraColumns;
				if (extraColumns != null && extraColumns.Count > 0)
				{
					stringBuilder.AppendLine(Regex.Split(text.Replace("#[placeHolder]", " null,"), " limit")[0]);
					goto IL_008c;
				}
			}
			stringBuilder.AppendLine(Regex.Split(text.Replace("#[placeHolder]", " null "), " limit")[0]);
			goto IL_008c;
			IL_008c:
			stringBuilder.AppendLine(" ) table_main");
			return stringBuilder.ToString().Trim();
		}
	}
	internal class StackTraceHelper
	{
		public static void CallChain()
		{
			StackTrace stackTrace = new StackTrace();
			StackFrame[] frames = stackTrace.GetFrames();
			StackFrame[] array = frames;
			foreach (StackFrame stackFrame in array)
			{
				MethodBase method = stackFrame.GetMethod();
				string name = method.Name;
				string name2 = method.DeclaringType.Name;
				int iLOffset = stackFrame.GetILOffset();
				int fileColumnNumber = stackFrame.GetFileColumnNumber();
				Console.WriteLine($"所在类:{name2}，方法名:{name},行号:{stackFrame.GetFileLineNumber()},偏移量:{iLOffset}");
			}
		}
	}
	public static class StringFormatHelper
	{
		public static string RemoveCnBracket(this string str)
		{
			if (str == null)
			{
				return str;
			}
			return str.Replace("（", "(")?.Replace("）", ")");
		}
	}
	public static class StringUtil
	{
		public static string GetAfterKeyData(this string str, string key)
		{
			int num = str.LastIndexOf(key) + key.Length;
			int length = str.Length - num;
			return str.Substring(num, length);
		}

		public static string GetBeforeKeyData(this string str, string key)
		{
			int length = str.LastIndexOf(key);
			return str.Substring(0, length);
		}

		public static string GetLimitStr(this string str, string star, string end)
		{
			if (star.IndexOf(end, 0) != -1)
			{
				return "";
			}
			int num = str.IndexOf(star);
			int num2 = str.IndexOf(end);
			if (num == -1 || num2 == -1)
			{
				return "";
			}
			return str.Substring(num + star.Length, num2 - num - star.Length);
		}

		public static bool IsNullOrEmpty(this string str)
		{
			return string.IsNullOrEmpty(str);
		}

		public static int ToInt(this string str)
		{
			return str.Contains("-") ? (-int.Parse(str.Replace("-", ""))) : int.Parse(str);
		}

		public static string StrBuild(this string str, Action<StringBuilder> callBack)
		{
			StringBuilder stringBuilder = new StringBuilder(str);
			callBack?.Invoke(stringBuilder);
			return stringBuilder.ToString();
		}

		public static bool IsEmpty(string source)
		{
			return source == null || "".Equals(source);
		}

		public static bool IsBlack(string source)
		{
			return source == null || "".Equals(source.Trim());
		}

		public static bool IsEmpty(StringBuilder sbf)
		{
			return sbf == null || "".Equals(sbf.ToString());
		}

		public static bool IsBlack(StringBuilder sbf)
		{
			return sbf == null || "".Equals(sbf.ToString().Trim());
		}

		public static string UpperCaseFirst(string str)
		{
			if (string.IsNullOrWhiteSpace(str))
			{
				return string.Empty;
			}
			char[] array = str.ToCharArray();
			char c = array[0];
			if ('a' <= c && c <= 'z')
			{
				c = (char)(c & 0xFFFFFFDFu);
			}
			array[0] = c;
			return new string(array);
		}

		public static string FillWhiteSpace(string curValue, string defaultValue)
		{
			if (string.IsNullOrEmpty(curValue))
			{
				return defaultValue;
			}
			return curValue;
		}

		public static string FillWhiteSpace(StringBuilder curValue, string defaultValue)
		{
			if (string.IsNullOrWhiteSpace(curValue?.ToString()))
			{
				return defaultValue;
			}
			return curValue.ToString();
		}
	}
	public class UTCReadUtil
	{
		public static string _fixPinCode = "ssss1800";

		public static UKResult _uKResult = new UKResult
		{
			Result = "200@000000000000000ABU@测试有限责任公司",
			Code = "0",
			Message = "",
			Check = false
		};

		public static UKResult GetDNInfo(string pinCode)
		{
			if (_fixPinCode.Equals(pinCode))
			{
				return _uKResult;
			}
			UTCAEKey uTCAEKey = new UTCAEKey();
			UKResult pin = GetPin(pinCode);
			if (pin.Code != "0")
			{
				if ((pin.Message ?? "").Contains("没有获取到电子钥匙证书"))
				{
					pin.Message = "获取不到电子钥匙证书，请安装64位电子钥匙控件";
				}
				MessageBox.Show(pin.Message, "提示", MessageBoxButtons.OK);
				return pin;
			}
			string dn = uTCAEKey.GetDn();
			pin = JsonConvert.DeserializeObject<UKResult>(dn);
			if (pin.Code != "0")
			{
				if ((pin.Message ?? "").Contains("没有获取到电子钥匙证书"))
				{
					pin.Message = "获取不到电子钥匙证书，请安装64位电子钥匙控件";
				}
				MessageBox.Show(pin.Message, "提示", MessageBoxButtons.OK);
				return pin;
			}
			return pin;
		}

		public static string CheckPin(string pinCode)
		{
			UTCAEKey uTCAEKey = new UTCAEKey();
			string value = uTCAEKey.ChkKeyPin(pinCode);
			UKResult uKResult = JsonConvert.DeserializeObject<UKResult>(value);
			return ("0".Equals(uKResult.Code) && "Correct pin".Equals(uKResult.Result)) ? "ok" : uKResult.Message;
		}

		public static UKResult GetPin(string pinCode)
		{
			UKResult uKResult = null;
			string text = null;
			UTCAEKey uTCAEKey = new UTCAEKey();
			text = uTCAEKey.ChkKeyPin(pinCode);
			return JsonConvert.DeserializeObject<UKResult>(text);
		}

		public static R VerifyAdobeSignature(string filePath)
		{
			//IL_00e6: Unknown result type (might be due to invalid IL or missing references)
			//IL_00f0: Expected O, but got Unknown
			R r = new R();
			AdobeSeal adobeSeal = new AdobeSeal();
			try
			{
				string value = adobeSeal.VerifyAdobeSignature(filePath);
				UKResult uKResult = JsonConvert.DeserializeObject<UKResult>(value);
				if (uKResult.Code == "0")
				{
					r.Successful = true;
					if (uKResult.Result == null || uKResult.Result == "")
					{
						r.Successful = false;
						r.ResultHint = "该文件没有做电子签章，请确认。";
					}
				}
				else
				{
					r.Successful = false;
					r.ResultHint = "验签失败:" + uKResult.Message;
				}
			}
			catch (Exception)
			{
				r.Successful = false;
				string signToolExe = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UKEY_ESeal_V10.exe");
				if (File.Exists(signToolExe))
				{
					Task.Factory.StartNew((Action)delegate
					{
						Process.Start(signToolExe).WaitForExit(120000);
					});
					r.ResultHint = "后台正在优泰签章工具，安装成功请后重启电脑！";
				}
				else
				{
					r.ResultHint = "系统缺少签章工具，请安装优泰签章工具并安装成功后重启电脑！";
				}
			}
			return r;
		}

		public static UKResult GetAdobeSignature(string filePath)
		{
			UTCAEKey uTCAEKey = new UTCAEKey();
			string certDn = uTCAEKey.GetCertDn();
			UKResult uKResult = JsonConvert.DeserializeObject<UKResult>(certDn);
			if (uKResult.Code == "0")
			{
				string dn = Regex.Split(uKResult.Result, "\\|\\|", RegexOptions.None)[0];
				global::UTCCrypt.UTCCrypt uTCCrypt = new global::UTCCrypt.UTCCrypt();
				string value = uTCCrypt.SignFileDetachedToMsg(filePath, dn, global::UTCCrypt.UTCCrypt.SHA1);
				uKResult = JsonConvert.DeserializeObject<UKResult>(value);
			}
			return uKResult;
		}
	}
	public class VerifyUtil
	{
		public static MessageInfo<T> Verify<T>(List<T> lists, string businessDiff, bool needColum, string tipPreffix)
		{
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			messageInfo.ExistError = false;
			try
			{
				string text = "";
				StringBuilder stringBuilder = new StringBuilder("");
				StringBuilder stringBuilder2 = new StringBuilder("");
				if (lists != null && lists.Count > 0)
				{
					T val = Activator.CreateInstance<T>();
					Type typeFromHandle = typeof(T);
					BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
					FieldInfo[] fields = typeFromHandle.GetFields(bindingAttr);
					VerifyAttribute verifyAttribute = null;
					Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
					Dictionary<string, VerifyAttribute> dictionary2 = new Dictionary<string, VerifyAttribute>();
					string text2 = "";
					for (int i = 0; i < fields.Length; i++)
					{
						verifyAttribute = fields[i].GetCustomAttribute<VerifyAttribute>();
						if (verifyAttribute != null && CheckPattern(verifyAttribute.BusinessDiff, businessDiff))
						{
							text2 = SQLiteSqlUtils.UpperCaseFirst(fields[i].Name);
							dictionary.Add(text2, typeFromHandle.GetProperty(text2));
							dictionary2.Add(text2, verifyAttribute);
						}
					}
					PropertyInfo[] properties = typeFromHandle.GetProperties();
					for (int j = 0; j < properties.Length; j++)
					{
						verifyAttribute = properties[j].GetCustomAttribute<VerifyAttribute>();
						if (verifyAttribute != null && CheckPattern(verifyAttribute.BusinessDiff, businessDiff))
						{
							dictionary.Add(properties[j].Name, properties[j]);
							dictionary2.Add(properties[j].Name, verifyAttribute);
						}
					}
					Dictionary<string, string> dictionary3 = new Dictionary<string, string>();
					Dictionary<string, PropertyInfo>.KeyCollection keys = dictionary.Keys;
					object obj = null;
					int num = 0;
					string text3 = "";
					bool flag = false;
					foreach (T list in lists)
					{
						text = (needColum ? "第{0}行 " : "");
						num++;
						foreach (string item in keys)
						{
							obj = dictionary[item].GetValue(list);
							VerifyAttribute verifyAttribute2 = dictionary2[item];
							if (verifyAttribute2 == null)
							{
								break;
							}
							text3 = ((verifyAttribute2.Title != null && verifyAttribute2.Title != "") ? verifyAttribute2.Title : dictionary[item].Name);
							flag = verifyAttribute2.PromptOnly;
							if (!verifyAttribute2.Nullable && (obj == null || obj.ToString().Trim() == ""))
							{
								if (flag)
								{
									messageInfo.ExistError = true;
									stringBuilder2.Append((tipPreffix + text + text3 + "不得为空\n").Replace("{0}", num.ToString() ?? ""));
								}
								else
								{
									messageInfo.ExistError = true;
									stringBuilder.Append((tipPreffix + text + text3 + "不得为空\n").Replace("{0}", num.ToString() ?? ""));
								}
							}
							if (verifyAttribute2.MaxLength > 0 && obj != null && obj.ToString().Length > verifyAttribute2.MaxLength)
							{
								messageInfo.ExistError = true;
								if (flag)
								{
									stringBuilder2.Append((tipPreffix + text + text3 + "长度不得超过" + verifyAttribute2.MaxLength + "个字符\n").Replace("{0}", num.ToString() ?? ""));
								}
								else
								{
									stringBuilder.Append((tipPreffix + text + text3 + "长度不得超过" + verifyAttribute2.MaxLength + "个字符\n").Replace("{0}", num.ToString() ?? ""));
								}
							}
							if (verifyAttribute2.Repeat && obj != null && !StringUtil.IsBlack(obj.ToString()))
							{
								if (dictionary3.ContainsKey(item + "-||-" + obj.ToString()))
								{
									messageInfo.ExistError = true;
									stringBuilder.Append((tipPreffix + text + text3 + "存在重复").Replace("{0}", num.ToString() ?? ""));
								}
								else
								{
									dictionary3.Add(item + "-||-" + obj.ToString(), "");
								}
							}
							if (obj != null && verifyAttribute2.Pattern != null && verifyAttribute2.Pattern != "" && obj.ToString().Trim().Length > 0 && !Regex.IsMatch(obj.ToString().Trim(), verifyAttribute2.Pattern))
							{
								messageInfo.ExistError = true;
								if (flag)
								{
									stringBuilder2.Append((tipPreffix + text + text3 + "格式不正确\n").Replace("{0}", num.ToString() ?? ""));
								}
								else
								{
									stringBuilder.Append((tipPreffix + text + text3 + "格式不正确\n").Replace("{0}", num.ToString() ?? ""));
								}
							}
						}
					}
					messageInfo.ErrorInfo = stringBuilder.ToString();
					messageInfo.PromptInfo = stringBuilder2.ToString();
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
			return messageInfo;
		}

		public static MessageInfo<T> Verify<T>(List<T> lists, bool needColum, string tipPreffix)
		{
			return Verify(lists, null, needColum, tipPreffix);
		}

		private static bool CheckPattern(string[] businessDifField, string businessSource)
		{
			if (businessDifField == null || businessDifField.Length == 0)
			{
				return true;
			}
			return businessDifField.Contains(businessSource);
		}

		public static MessageInfo<T> Verify<T>(T t, Dictionary<string, Expression<Func<T, bool>>> _ExpArrayDict, Dictionary<string, Control> verifyDict = null, ErrorProvider errorProvider = null)
		{
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			messageInfo.ExistError = false;
			try
			{
				StringBuilder stringBuilder = new StringBuilder("");
				foreach (KeyValuePair<string, Expression<Func<T, bool>>> item in _ExpArrayDict)
				{
					if (item.Value.Compile().Invoke(t))
					{
						stringBuilder.Append(item.Key + "\r\n");
						errorProvider?.SetError(verifyDict[item.Key], item.Key);
					}
					else
					{
						errorProvider?.SetError(verifyDict[item.Key], null);
					}
				}
				messageInfo.ErrorInfo = stringBuilder.ToString();
				messageInfo.ExistError = messageInfo.ErrorInfo.Length > 0;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
			return messageInfo;
		}

		public static MessageInfo<T> Verify<T>(T t)
		{
			if (t != null)
			{
				List<T> list = new List<T>();
				list.Add(t);
				return Verify(list, needColum: false, "");
			}
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			messageInfo.ExistError = false;
			return messageInfo;
		}

		public static MessageInfo<T> Verify<T>(T t, string businessDiff)
		{
			if (t != null)
			{
				List<T> list = new List<T>();
				list.Add(t);
				return Verify(list, businessDiff, needColum: false, "");
			}
			MessageInfo<T> messageInfo = new MessageInfo<T>();
			messageInfo.ExistError = false;
			return messageInfo;
		}

		public static bool CheckNull(DataRow row, Dictionary<string, string> titleMapper, StringBuilder sbf)
		{
			DataColumnCollection columns = row.Table.Columns;
			int num = 0;
			for (int i = 0; i < columns.Count; i++)
			{
				if (titleMapper.ContainsKey(columns[i].ColumnName) && (row.IsNull(columns[i].ColumnName) || string.IsNullOrWhiteSpace(row[columns[i].ColumnName].ToString())))
				{
					sbf.Append(string.Format("第{0}行,{1}不能为空\r\n", row["CurExcelIndex"], titleMapper[columns[i].ColumnName]));
					num++;
				}
			}
			return num <= 0;
		}
	}
	public class WebRequestUtil
	{
		public static R PostBasicEntityDataApi<T>(string api, List<T> list)
		{
			return PostBasicEntityDataApi(api, list, null);
		}

		public static R PostBasicEntityDataApi<T>(string api, List<T> list, string[] ingoreFields)
		{
			List<string> list2 = new List<string>();
			list2 = getCurIngores(ingoreFields);
			Type typeFromHandle = typeof(T);
			BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			PropertyInfo currentProp = null;
			if (list.Count > 0)
			{
				foreach (T item in list)
				{
					RemoveFieldValue(item, typeFromHandle, bindingFlags, currentProp, list2);
				}
			}
			return PostBasicApi(api, list);
		}

		private static List<string> getCurIngores(string[] ingoreFields)
		{
			List<string> list;
			if (ingoreFields != null)
			{
				list = new List<string>(ingoreFields);
				if (!list.Contains("CreateTime"))
				{
					list.Add("CreateTime");
				}
				if (!list.Contains("UpdateTime"))
				{
					list.Add("UpdateTime");
				}
			}
			else
			{
				list = new List<string>(new string[2] { "CreateTime", "UpdateTime" });
			}
			return list;
		}

		private static void RemoveFieldValue<T>(T item, Type tbc, BindingFlags bindingFlags, PropertyInfo currentProp, List<string> curIngores)
		{
			try
			{
				foreach (string curIngore in curIngores)
				{
					currentProp = tbc.GetProperty(SQLiteSqlUtils.UpperCaseFirst(curIngore), bindingFlags);
					if (currentProp != (PropertyInfo)null)
					{
						currentProp.SetValue(item, null);
					}
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
			}
		}

		public static R PostBasicEntityDataApi<T>(string api, T t)
		{
			return PostBasicEntityDataApi(api, t, null);
		}

		public static R PostBasicEntityDataApi<T>(string api, T t, string[] ingoreFields)
		{
			List<string> list = new List<string>();
			list = getCurIngores(ingoreFields);
			Type typeFromHandle = typeof(T);
			BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			PropertyInfo currentProp = null;
			if (t != null)
			{
				RemoveFieldValue(t, typeFromHandle, bindingFlags, currentProp, list);
			}
			return PostBasicApi(api, t);
		}

		public static R PostBasicApi(string api, object entity, int Timeout = 300000, bool needDecrypt = true)
		{
			try
			{
				if (Timeout < 90000)
				{
					Timeout = 300000;
				}
				string valueByKey = ConfigHelper.GetValueByKey("dataApiPrefixUrl");
				api = Regex.Replace(api, "^(\\\\|//)", "");
				string text = valueByKey + api;
				text = text + "?rnd=" + DateTime.Now.ToFileTimeUtc();
				LogsHelper.AddLog("当前请求url:" + text, PublicVo.EntName);
				string jsonStr = "";
				if (entity != null)
				{
					jsonStr = JsonConvert.SerializeObject(entity);
				}
				string value = DoHttpPost(text, jsonStr, Timeout);
				R r = JsonConvert.DeserializeObject<R>(value);
				string pattern = "[一-龥]";
				if (needDecrypt && r.ResultValue != null && !Regex.IsMatch(r.ResultValue.ToString(), pattern))
				{
					r.ResultValue = SM4Util.decryptEcb(ContantUtils.SM4_KEY, r.ResultValue.ToString());
				}
				r.NeedDecrypt = needDecrypt;
				return r;
			}
			catch (Exception ex)
			{
				return new R(9, bl: false, "请求服务器异常,异常信息:" + ex.Message)
				{
					NeedDecrypt = needDecrypt
				};
			}
		}

		public static R PostDeleteRemoteDataApi(string api, string deleteIdsStr)
		{
			try
			{
				string valueByKey = ConfigHelper.GetValueByKey("dataApiPrefixUrl");
				api = Regex.Replace(api, "^(\\\\|//)", "");
				string text = valueByKey + api;
				text = text + "?rnd=" + DateTime.Now.ToFileTimeUtc() + "&token=" + PublicVo.Token + "&supplierId=" + PublicVo.SupplyId;
				string value = DoHttpPost(text, JsonConvert.SerializeObject(deleteIdsStr.Replace("'", "").Split(',')));
				return JsonConvert.DeserializeObject<R>(value);
			}
			catch (Exception ex)
			{
				return new R(9, bl: false, "请求服务器异常,异常信息:" + ex.Message);
			}
		}

		public static string DoHttpPost(string Url, string jsonStr, int Timeout = 300000)
		{
			if (!string.IsNullOrWhiteSpace(jsonStr))
			{
				jsonStr = SM4Util.encryptEcb(ContantUtils.SM4_KEY, jsonStr);
				jsonStr = "{\"_encData\":\"" + jsonStr + "\"}";
			}
			if (Timeout < 90000)
			{
				Timeout = 300000;
			}
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(Url);
			httpWebRequest.Method = "POST";
			httpWebRequest.ReadWriteTimeout = 300000;
			httpWebRequest.Timeout = Timeout;
			if (PublicVo.Id != null)
			{
			}
			httpWebRequest.KeepAlive = false;
			byte[] bytes = Encoding.Default.GetBytes(jsonStr);
			httpWebRequest.ContentType = "application/json";
			httpWebRequest.ContentLength = bytes.Length;
			Stream stream = null;
			stream = httpWebRequest.GetRequestStream();
			stream.Write(bytes, 0, bytes.Length);
			HttpWebResponse httpWebResponse = null;
			try
			{
				httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
				string result = streamReader.ReadToEnd();
				streamReader.Close();
				responseStream.Close();
				return result;
			}
			catch (Exception ex)
			{
				R value = new R
				{
					Code = 502,
					Successful = false,
					ResultHint = ex.Message
				};
				return JsonConvert.SerializeObject(value);
			}
		}

		public static R ShowAttach(string api, object entity, int Timeout = 300000)
		{
			try
			{
				string valueByKey = ConfigHelper.GetValueByKey("dataApiPrefixUrl");
				api = Regex.Replace(api, "^(\\\\|//)", "");
				string text = valueByKey + api;
				text = text + (text.Contains("?") ? "&" : "?") + "rnd=" + DateTime.Now.ToFileTimeUtc();
				string jsonStr = "";
				if (entity != null)
				{
					jsonStr = JsonConvert.SerializeObject(entity);
				}
				return GetResposeImage(text, jsonStr, Timeout);
			}
			catch (Exception ex)
			{
				return new R(9, bl: false, "请求服务器异常,异常信息:" + ex.Message);
			}
		}

		private static R GetResposeImage(string Url, string jsonStr, int Timeout = 300000)
		{
			R r = new R();
			if (!string.IsNullOrWhiteSpace(jsonStr))
			{
				jsonStr = SM4Util.encryptEcb(ContantUtils.SM4_KEY, jsonStr);
				jsonStr = "{\"_encData\":\"" + jsonStr + "\"}";
			}
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(Url);
			httpWebRequest.Method = "POST";
			httpWebRequest.ReadWriteTimeout = 300000;
			httpWebRequest.Timeout = Timeout;
			httpWebRequest.KeepAlive = false;
			byte[] bytes = Encoding.Default.GetBytes(jsonStr);
			httpWebRequest.ContentType = "application/json";
			httpWebRequest.ContentLength = bytes.Length;
			Stream stream = null;
			stream = httpWebRequest.GetRequestStream();
			stream.Write(bytes, 0, bytes.Length);
			HttpWebResponse httpWebResponse = null;
			try
			{
				httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
				Image resultValue = Image.FromStream(responseStream);
				streamReader.Close();
				responseStream.Close();
				r.Successful = true;
				r.ResultValue = resultValue;
				return r;
			}
			catch (Exception ex)
			{
				return new R
				{
					Code = 502,
					Successful = false,
					ResultHint = ex.Message
				};
			}
		}

		public static R HttpPost(string url, object entity, int timeout = 100000, bool needDecrypt = true)
		{
			try
			{
				string text = url;
				url = url + "?rnd=" + DateTime.Now.ToFileTimeUtc();
				LogsHelper.AddLog("当前请求url:" + url, PublicVo.EntName);
				string jsonStr = "";
				if (entity != null)
				{
					jsonStr = JsonConvert.SerializeObject(entity);
				}
				string value = DoHttpPost(url, jsonStr, timeout);
				R r = JsonConvert.DeserializeObject<R>(value);
				string pattern = "[一-龥]";
				if (needDecrypt && r.ResultValue != null && !Regex.IsMatch(r.ResultValue.ToString(), pattern))
				{
					r.ResultValue = SM4Util.decryptEcb(ContantUtils.SM4_KEY, r.ResultValue.ToString());
				}
				r.NeedDecrypt = needDecrypt;
				return r;
			}
			catch (Exception ex)
			{
				return new R(9, bl: false, "请求服务器异常,异常信息:" + ex.Message)
				{
					NeedDecrypt = needDecrypt
				};
			}
		}
	}
	public class WindowsOsHelper
	{
		public static decimal GetHardDiskSpace(string hardDiskName)
		{
			double value = 0.0;
			hardDiskName = Path.Combine(hardDiskName, "");
			DriveInfo[] drives = DriveInfo.GetDrives();
			DriveInfo[] array = drives;
			foreach (DriveInfo driveInfo in array)
			{
				if (driveInfo.Name == hardDiskName)
				{
					value = driveInfo.TotalSize / 1073741824;
					break;
				}
			}
			decimal d = Convert.ToDecimal(value);
			return decimal.Round(d, 1);
		}

		public static decimal GetHardDiskFreeSpace(string hardDiskName)
		{
			double value = 0.0;
			hardDiskName = Path.Combine(hardDiskName, "");
			DriveInfo[] drives = DriveInfo.GetDrives();
			DriveInfo[] array = drives;
			foreach (DriveInfo driveInfo in array)
			{
				if (driveInfo.Name == hardDiskName)
				{
					value = driveInfo.TotalFreeSpace / 1073741824;
					break;
				}
			}
			decimal d = Convert.ToDecimal(value);
			return decimal.Round(d, 1);
		}
	}
	public class WinFormUtil
	{
		public static readonly float FontRadio = 0.92f;

		public static readonly string SourceHanSansLight = "思源黑体 CN Light";

		public static readonly string SourceHanSansNormal = "思源黑体 CN Normal";

		public static readonly string SourceHanSansRegular = "思源黑体 CN Regular";

		public static readonly string SourceHanSansMedium = "思源黑体 CN Medium";

		public static readonly Color BackGroudColor = Color.FromArgb(243, 244, 247);

		private static readonly PrivateFontCollection fontCollection = new PrivateFontCollection();

		public static void CreateNewDataTable(DataRow dataRow, ref DataTable dataTable, ref int operateIndex)
		{
			DataTable table = dataRow.Table;
			bool flag = dataTable != null;
			dataTable = dataTable ?? new DataTable();
			dataTable.TableName = table.TableName;
			DataColumnCollection columns = table.Columns;
			for (int i = 0; i < columns.Count; i++)
			{
				if (!flag)
				{
					DataColumn column = new DataColumn
					{
						ColumnName = columns[i].ColumnName,
						DataType = columns[i].DataType,
						DefaultValue = columns[i].DefaultValue
					};
					dataTable.Columns.Add(column);
				}
			}
			if (flag)
			{
				dataTable.Rows[0].ItemArray = dataRow.ItemArray;
				dataTable.Rows[0].AcceptChanges();
			}
			else
			{
				operateIndex = dataRow.Table.Rows.IndexOf(dataRow);
				dataTable.Rows.Add(dataRow.ItemArray);
			}
		}

		public static void DataBinding(Control mainControl, DataTable dataTable, string suffix = "_input")
		{
			Dictionary<Type, string> bindPropDict = GetBindPropDict();
			Stack<Control> stack = new Stack<Control>();
			stack.Push(mainControl);
			while (stack.Count > 0)
			{
				Control control = stack.Pop();
				Control.ControlCollection controls = control.Controls;
				foreach (Control item in controls)
				{
					string text = item.Name.Replace(suffix, "");
					if (dataTable.Columns.Contains(text) || dataTable.Columns.Contains(text.ToUpper()))
					{
						try
						{
							if (item is RadioControl radioControl)
							{
								radioControl.Font = GetSystemFont(FontStyle.Regular, SourceHanSansLight, 9f);
								radioControl.SetDataBinding(dataTable, item.Name);
								continue;
							}
							if (item is CheckBox checkBox)
							{
								checkBox.Font = GetSystemFont(FontStyle.Regular, SourceHanSansLight, checkBox.Font.Size);
								continue;
							}
							if (item is RadioButton radioButton)
							{
								radioButton.Font = GetSystemFont(FontStyle.Regular, SourceHanSansLight, radioButton.Font.Size);
								continue;
							}
							string text2 = (bindPropDict.ContainsKey(item.GetType()) ? bindPropDict[item.GetType()] : "Text");
							if (item is ComboBox { DataSource: not null })
							{
								text2 = "SelectedValue";
							}
							Binding binding = new Binding(text2, dataTable, item.Name);
							IEnumerator enumerator2 = item.DataBindings.GetEnumerator();
							bool flag = false;
							while (enumerator2.MoveNext())
							{
								if (enumerator2.Current is Binding binding2 && binding2.PropertyName == text2)
								{
									flag = true;
								}
							}
							if (!flag)
							{
								item.DataBindings.Add(binding);
							}
						}
						catch (Exception value)
						{
							Console.WriteLine(value);
						}
					}
					stack.Push(item);
				}
			}
		}

		private static Dictionary<Type, string> GetBindPropDict()
		{
			Dictionary<Type, string> dictionary = new Dictionary<Type, string>();
			dictionary.Add(typeof(TextBox), "Text");
			dictionary.Add(typeof(RichTextBox), "Text");
			dictionary.Add(typeof(NumericUpDown), "Value");
			dictionary.Add(typeof(ComboBox), "Text");
			dictionary.Add(typeof(DateTimePicker), "Value");
			return dictionary;
		}

		public static void InitFonts(params FontEnums[] fontEnums)
		{
			for (int i = 0; i < fontEnums.Length; i++)
			{
				FieldInfo field = fontEnums[i].GetType().GetField(fontEnums[i].ToString());
				DescriptionAttribute customAttribute = field.GetCustomAttribute<DescriptionAttribute>();
				if (customAttribute != null)
				{
					fontCollection.AddFontFile("fonts\\" + customAttribute.Description);
				}
			}
			Console.WriteLine("--");
		}

		public static void SetControlFont(Control mainControl, Control[] ingores, float fontSize = 10f, int left = 0)
		{
			try
			{
				float num = (LowResolution() ? FontRadio : 1f);
				Dictionary<string, string> dictionary = new Dictionary<string, string>();
				if (ingores != null)
				{
					for (int i = 0; i < ingores.Length; i++)
					{
						if (!dictionary.ContainsKey(ingores[i].Name))
						{
							dictionary.Add(ingores[i].Name, "");
						}
					}
				}
				Stack<Control> stack = new Stack<Control>();
				stack.Push(mainControl);
				while (stack.Count > 0)
				{
					mainControl = stack.Pop();
					if (mainControl is Label)
					{
						mainControl.Font = GetSystemFont(mainControl.Font.Style, SourceHanSansRegular, ((fontSize > 0f) ? fontSize : mainControl.Font.Size) * num);
					}
					else if (mainControl is TextBox || mainControl is ComboBox || mainControl is RichTextBox)
					{
						mainControl.Left = ((left > 0) ? left : mainControl.Left);
						mainControl.Font = GetSystemFont(mainControl.Font.Style, SourceHanSansLight, ((fontSize > 0f) ? fontSize : mainControl.Font.Size) * num);
					}
					else if (mainControl is Button)
					{
						mainControl.Font = GetSystemFont(mainControl.Font.Style, SourceHanSansRegular, ((fontSize > 0f) ? fontSize : mainControl.Font.Size) * num);
					}
					Control.ControlCollection controls = mainControl.Controls;
					foreach (Control item in controls)
					{
						if (!dictionary.ContainsKey(item.Name))
						{
							stack.Push(item);
						}
					}
				}
			}
			catch (Exception)
			{
			}
		}

		public static System.Drawing.Font GetSystemFont(FontStyle fontStyle, string familyName = null, float fontSize = 15f)
		{
			FontFamily family = new FontFamily(familyName ?? SourceHanSansRegular, fontCollection);
			return new System.Drawing.Font(family, fontSize, fontStyle);
		}

		public static void SetSystemFont(float size = 0f, string familyName = null, bool bold = false, params Control[] controls)
		{
			float num = (LowResolution() ? FontRadio : 1f);
			foreach (Control control in controls)
			{
				FontStyle fontStyle = control.Font.Style;
				if (bold)
				{
					fontStyle &= FontStyle.Bold;
				}
				System.Drawing.Font systemFont = GetSystemFont(fontStyle, familyName, ((size <= 0f) ? control.Font.Size : size) * num);
				control.Font = systemFont;
			}
		}

		public static void SetSystemFontByBasic(Control.ControlCollection controls, Control[] ingoresControl, float size = 0f, bool bold = false)
		{
			float num = (LowResolution() ? FontRadio : 1f);
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			if (ingoresControl != null)
			{
				foreach (Control control in ingoresControl)
				{
					if (!dictionary.ContainsKey(control.Name))
					{
						dictionary.Add(control.Name, null);
					}
				}
			}
			foreach (Control control2 in controls)
			{
				if (!dictionary.ContainsKey(control2.Name))
				{
					FontStyle fontStyle = control2.Font.Style;
					if (bold)
					{
						fontStyle |= FontStyle.Bold;
					}
					if (control2 is Label)
					{
						control2.Font = GetSystemFont(fontStyle, SourceHanSansRegular, ((size <= 0f) ? control2.Font.Size : size) * num);
					}
					else
					{
						control2.Font = GetSystemFont(fontStyle, SourceHanSansLight, ((size <= 0f) ? control2.Font.Size : size) * num);
					}
				}
			}
		}

		private static bool LowResolution()
		{
			GetResolutionRatio(out var width, out var height);
			if (width == 1366 && height == 768)
			{
				return true;
			}
			return false;
		}

		public static void SetSystemFont(ToolStripItemCollection toolStripItemList, string familyName = null, bool bold = false, float size = 0f)
		{
			float num = (LowResolution() ? FontRadio : 1f);
			foreach (ToolStripItem toolStripItem in toolStripItemList)
			{
				FontStyle fontStyle = toolStripItem.Font.Style;
				if (bold)
				{
					fontStyle &= FontStyle.Bold;
				}
				System.Drawing.Font systemFont = GetSystemFont(fontStyle, familyName, ((size <= 0f) ? toolStripItem.Font.Size : size) * num);
				toolStripItem.Font = systemFont;
			}
		}

		private static void GetResolutionRatio(out int width, out int height)
		{
			width = SystemInformation.VirtualScreen.Width;
			height = SystemInformation.VirtualScreen.Height;
		}

		public static T GetEntityByControls<T>(Control mainFormControl)
		{
			Control.ControlCollection controls = mainFormControl.Controls;
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			Type typeFromHandle = typeof(T);
			T val = Activator.CreateInstance<T>();
			for (int i = 0; i < controls.Count; i++)
			{
				string name = controls[i].Name.UpperCaseFirst();
				string content = controls[i].Text;
				PropertyInfo property = typeFromHandle.GetProperty(name, bindingAttr);
				if (controls[i] is DateTimePicker dateTimePicker)
				{
					content = (dateTimePicker.Checked ? dateTimePicker.Text : "");
				}
				if (controls[i] is ComboBox)
				{
					ComboBox comboBox = (ComboBox)controls[i];
					content = ((comboBox.SelectedIndex >= 0) ? comboBox.Text : "");
				}
				if (controls[i] is ICustomControl customControl)
				{
					content = customControl.GetValue();
				}
				if (property != (PropertyInfo)null)
				{
					property.SetValue(val, content);
				}
			}
			return val;
		}

		public static void RowPostPaint(object sender, DataGridViewRowPostPaintEventArgs e)
		{
			DataGridView dataGridView = sender as DataGridView;
			int num = Convert.ToInt32(dataGridView.Tag ?? "0");
			DataGridViewColumnCollection columns = dataGridView.Columns;
			int num2 = 0;
			int width = 0;
			for (int i = 0; i < columns.Count && i <= num; i++)
			{
				if (i != num)
				{
					num2 += columns[i].Width;
				}
				else
				{
					width = (int)((double)columns[i].Width * 0.8);
				}
			}
			Rectangle bounds = new Rectangle(num2, e.RowBounds.Location.Y, width, e.RowBounds.Height);
			TextRenderer.DrawText(e.Graphics, (e.RowIndex + 1).ToString(), dataGridView.RowHeadersDefaultCellStyle.Font, bounds, dataGridView.RowHeadersDefaultCellStyle.ForeColor, TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
		}

		public static List<T> GetListByDataGrid<T>(DataGridView dataGridView)
		{
			List<T> list = new List<T>();
			try
			{
				int count = dataGridView.Rows.Count;
				int count2 = dataGridView.Columns.Count;
				BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
				Type typeFromHandle = typeof(T);
				string text = null;
				string text2 = null;
				PropertyInfo propertyInfo = null;
				for (int i = 0; i < count; i++)
				{
					T val = Activator.CreateInstance<T>();
					for (int j = 0; j < count2; j++)
					{
						text = StringUtil.UpperCaseFirst(dataGridView.Columns[j].Name);
						if (dataGridView.Rows[i].Cells[j].Value != null)
						{
							text2 = dataGridView.Rows[i].Cells[j].ToString();
							propertyInfo = typeFromHandle.GetProperty(text, bindingAttr);
							if (propertyInfo != (PropertyInfo)null)
							{
								propertyInfo.SetValue(val, text2);
							}
						}
					}
					list.Add(val);
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
			return list;
		}

		public static void SetControlsByEntity<T>(T t, Control mainFormControl)
		{
			if (t == null)
			{
				return;
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic;
			Type type = t.GetType();
			Control.ControlCollection controls = mainFormControl.Controls;
			string text = null;
			PropertyInfo propertyInfo = null;
			for (int i = 0; i < controls.Count; i++)
			{
				if (controls[i] is TextBox || controls[i] is DateTimePicker || controls[i] is ComboBox)
				{
					if (!(controls[i] is ComboBox) || (controls[i] as ComboBox).DataSource == null)
					{
						text = StringUtil.UpperCaseFirst(controls[i].Name);
						propertyInfo = type.GetProperty(text, bindingAttr);
						if (propertyInfo != (PropertyInfo)null)
						{
							object value = propertyInfo.GetValue(t);
							controls[i].Text = value?.ToString();
						}
					}
				}
				else if (controls[i] is ICustomControl customControl && (!(controls[i] is ComboBox) || (controls[i] as ComboBox).DataSource == null))
				{
					text = StringUtil.UpperCaseFirst(customControl.ControlName);
					propertyInfo = type.GetProperty(text, bindingAttr);
					if (propertyInfo != (PropertyInfo)null)
					{
						object value2 = propertyInfo.GetValue(t);
						customControl.SetValue(value2);
					}
				}
			}
		}

		public static void Check_KeyPress(object sender, KeyPressEventArgs e)
		{
			try
			{
				TextBox textBox = (TextBox)sender;
				string value = e.KeyChar.ToString();
				int selectionStart = textBox.SelectionStart;
				int selectionLength = textBox.SelectionLength;
				string text;
				if ("\u0016".Equals(value))
				{
					text = Clipboard.GetText();
				}
				else if ("\u0018".Equals(value))
				{
					text = "";
				}
				else
				{
					if ("\u001a".Equals(value) || "\u0003".Equals(value) || "\b".Equals(value) || "\r".Equals(value) || "\t".Equals(value))
					{
						return;
					}
					text = e.KeyChar.ToString();
				}
				string text2 = "";
				text2 = ((selectionLength == 0) ? textBox.Text.Insert(selectionStart, text) : (textBox.Text.Substring(0, selectionStart) + text + textBox.Text.Substring(selectionStart + selectionLength)));
				string text3 = textBox.Tag?.ToString();
				if (!string.IsNullOrEmpty(text3) && !string.IsNullOrEmpty(text2) && !Regex.IsMatch(text2, text3))
				{
					e.Handled = true;
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		public static void Control_Validating(object sender, CancelEventArgs e)
		{
			Control control = sender as Control;
			string pattern = "^0\\d{1,5}";
			if (control != null)
			{
				string text = control.Text ?? "-";
				if (Regex.IsMatch(text, pattern))
				{
					text = text.Substring(1, text.Length - 1);
				}
				if (text.EndsWith("."))
				{
					text = text.Substring(0, text.Length - 1);
				}
				control.Text = text;
			}
		}

		public static void CommonDataGridViewStyle(DataGridView dataGridView, bool buttonStyle = true, float head_fontSize = 10.5f, float content_fontSize = 11.2f)
		{
			try
			{
				head_fontSize = ((head_fontSize <= 0f) ? 10.5f : head_fontSize);
				content_fontSize = ((content_fontSize <= 0f) ? 11.2f : content_fontSize);
				dataGridView.AutoGenerateColumns = false;
				dataGridView.ColumnHeadersDefaultCellStyle.Font = GetSystemFont(FontStyle.Regular, SourceHanSansRegular, head_fontSize);
				dataGridView.RowsDefaultCellStyle.Font = GetSystemFont(FontStyle.Regular, SourceHanSansLight, content_fontSize);
				dataGridView.RowsDefaultCellStyle.ForeColor = Color.Black;
				dataGridView.RowsDefaultCellStyle.BackColor = Color.White;
				dataGridView.RowTemplate.DefaultCellStyle.SelectionBackColor = Color.FromArgb(215, 233, 246);
				dataGridView.RowTemplate.DefaultCellStyle.SelectionForeColor = Color.Black;
				if (!buttonStyle)
				{
					return;
				}
				DataGridViewColumnCollection columns = dataGridView.Columns;
				foreach (DataGridViewColumn item in columns)
				{
					if (item is DataGridViewButtonColumn dataGridViewButtonColumn)
					{
						dataGridViewButtonColumn.CellTemplate.Style.BackColor = Color.FromArgb(243, 244, 247);
						dataGridViewButtonColumn.FlatStyle = FlatStyle.Popup;
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
			}
		}

		public static Dictionary<string, Control> GetChildControl(Control parentControl)
		{
			Dictionary<string, Control> dictionary = new Dictionary<string, Control>();
			Stack<Control> stack = new Stack<Control>();
			stack.Push(parentControl);
			while (stack.Count > 0)
			{
				Control control = stack.Pop();
				if (!control.Visible)
				{
					continue;
				}
				Control.ControlCollection controls = control.Controls;
				if (dictionary.ContainsKey(control.Name))
				{
					continue;
				}
				dictionary.Add(control.Name, control);
				foreach (Control item in controls)
				{
					stack.Push(item);
				}
			}
			return dictionary;
		}

		public static Dictionary<string, Expression<Func<TEntity, bool>>> GetFormVerifyRules<TEntity>(Control mainControl, string suffix = "不能为空", Dictionary<string, Control> _verifyDict = null, Func<Control, string, string> VerifyTitle = null)
		{
			Dictionary<string, Control> childControl = GetChildControl(mainControl);
			Dictionary<string, Expression<Func<TEntity, bool>>> dictionary = new Dictionary<string, Expression<Func<TEntity, bool>>>();
			FieldInfo[] fields = typeof(TEntity).GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
			PropertyInfo[] properties = typeof(TEntity).GetProperties();
			FieldInfo[] array = fields;
			foreach (FieldInfo fl in array)
			{
				TableFieldAttribute customAttribute = fl.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute == null || !customAttribute.IsExist || !customAttribute.Require)
				{
					continue;
				}
				Control value = null;
				if (!childControl.TryGetValue(customAttribute.ColumName, out value) && !childControl.TryGetValue(fl.Name, out value))
				{
					continue;
				}
				string iKey = VerifyTitle?.Invoke(value, customAttribute.Remark) ?? (customAttribute.Remark + suffix);
				_verifyDict?.AddIfAbsent(iKey, value);
				if (value is ComboBox)
				{
					dictionary.AddIfAbsent(iKey, (Expression<Func<TEntity, bool>>)((TEntity p) => fl.GetValue(p) == null || "请选择".Equals(fl.GetValue(p).ToString()) || string.IsNullOrEmpty(fl.GetValue(p).ToString())));
				}
				else
				{
					dictionary.AddIfAbsent(iKey, (Expression<Func<TEntity, bool>>)((TEntity p) => fl.GetValue(p) == null || string.IsNullOrEmpty(fl.GetValue(p).ToString())));
				}
			}
			PropertyInfo[] array2 = properties;
			foreach (PropertyInfo pf in array2)
			{
				TableFieldAttribute customAttribute = pf.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute == null || !customAttribute.IsExist || !customAttribute.Require)
				{
					continue;
				}
				Control value2 = null;
				if (!childControl.TryGetValue(customAttribute.ColumName, out value2) && !childControl.TryGetValue(pf.Name, out value2))
				{
					continue;
				}
				string iKey = VerifyTitle?.Invoke(value2, customAttribute.Remark) ?? (customAttribute.Remark + suffix);
				_verifyDict?.AddIfAbsent(iKey, value2);
				if (value2 is ComboBox)
				{
					dictionary.AddIfAbsent(iKey, (Expression<Func<TEntity, bool>>)((TEntity p) => pf.GetValue(p) == null || "请选择".Equals(pf.GetValue(p).ToString()) || string.IsNullOrEmpty(pf.GetValue(p).ToString())));
				}
				else
				{
					dictionary.AddIfAbsent(iKey, (Expression<Func<TEntity, bool>>)((TEntity p) => pf.GetValue(p) == null || string.IsNullOrEmpty(pf.GetValue(p).ToString())));
				}
			}
			return dictionary;
		}

		public static void DataBinding<TEntity>(Control mainControl, TEntity entity, params string[] ingoreControlNames)
		{
			Stack<Control> stack = new Stack<Control>();
			stack.Push(mainControl);
			Type type = entity.GetType();
			PropertyInfo[] properties = type.GetProperties();
			FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			FieldInfo[] array = fields;
			foreach (FieldInfo fieldInfo in array)
			{
				TableFieldAttribute customAttribute = fieldInfo.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute != null)
				{
					dictionary.AddIfAbsent(customAttribute.ColumName, fieldInfo.Name.UpperCaseFirst());
					dictionary.AddIfAbsent(fieldInfo.Name, fieldInfo.Name.UpperCaseFirst());
				}
			}
			PropertyInfo[] array2 = properties;
			foreach (PropertyInfo propertyInfo in array2)
			{
				TableFieldAttribute customAttribute = propertyInfo.GetCustomAttribute<TableFieldAttribute>();
				if (customAttribute != null)
				{
					dictionary.AddIfAbsent(customAttribute.ColumName, propertyInfo.Name);
					dictionary.AddIfAbsent(propertyInfo.Name, propertyInfo.Name);
				}
			}
			Dictionary<string, string> dictionary2 = ingoreControlNames.ToDictionary<string, string, string>((string p) => p, (string p) => p);
			while (stack.Count > 0)
			{
				Control control = stack.Pop();
				Control.ControlCollection controls = control.Controls;
				if (dictionary.ContainsKey(control.Name) && !dictionary2.ContainsKey(control.Name))
				{
					if (control is ComboBox)
					{
						control.DataBindings.Add("SelectedItem", entity, dictionary[control.Name]);
					}
					else
					{
						control.DataBindings.Add("Text", entity, dictionary[control.Name]);
					}
				}
				foreach (Control item in controls)
				{
					stack.Push(item);
				}
			}
		}

		public static void SystemDpi(out int x, out int y)
		{
			using Graphics graphics = Graphics.FromHwnd(IntPtr.Zero);
			x = (int)graphics.DpiX;
			y = (int)graphics.DpiY;
			graphics.Dispose();
		}

		public static double Scaling(int DpiIndex)
		{
			return DpiIndex switch
			{
				96 => 1.0, 
				120 => 1.25, 
				144 => 1.5, 
				168 => 1.75, 
				192 => 2.0, 
				_ => 1.0, 
			};
		}

		public static void SetDataSource(ListControl listControl, bool firstOption = true, params string[] options)
		{
			List<ComboBoxVo> list = new List<ComboBoxVo>();
			if (firstOption)
			{
				list.Insert(0, new ComboBoxVo
				{
					KeyName = "请选择",
					KeyValue = ""
				});
			}
			for (int i = 0; i < options.Length; i++)
			{
				list.Add(new ComboBoxVo
				{
					KeyName = options[i],
					KeyValue = options[i]
				});
			}
			listControl.DataSource = list;
			listControl.DisplayMember = "KeyName";
			listControl.ValueMember = "KeyValue";
		}

		public static void SetMultDataSource(string[] options, bool firstOption = true, params ListControl[] listControl)
		{
			foreach (ListControl listControl2 in listControl)
			{
				SetDataSource(listControl2, firstOption, options);
			}
		}

		public static IEnumerable GetChildControls(Control control)
		{
			Control.ControlCollection controls = control.Controls;
			foreach (Control item in controls)
			{
				yield return item;
			}
		}
	}
	public enum FontEnums
	{
		[Description("SourceHanSansCN-Light.ttf")]
		SourceHanSansCN_Light,
		[Description("SourceHanSansCN-Normal.ttf")]
		SourceHanSansCN_Normal,
		[Description("SourceHanSansCN-Regular.ttf")]
		SourceHanSansCN_Regular,
		[Description("SourceHanSansCN-Medium.ttf")]
		SourceHanSansCN_Medium
	}
	public class XmlHelper
	{
		public static string myConfigXmlPath;

		public static string[] DefaultDlls;

		public static Dictionary<string, string> EscapeCharDicts;

		static XmlHelper()
		{
			myConfigXmlPath = ConfigHelper.GetValueByKey("myConfigXml");
			DefaultDlls = new string[1] { "Model.dll" };
			EscapeCharDicts = new Dictionary<string, string>();
			EscapeCharDicts.Add("and", "&&");
			EscapeCharDicts.Add("or", "or");
			EscapeCharDicts.Add("'", "\"");
			EscapeCharDicts.Add("<>", "!=");
			EscapeCharDicts.Add("&lt;", "<");
			EscapeCharDicts.Add("&gt;", ">");
			EscapeCharDicts.Add("&le;", "<=");
			EscapeCharDicts.Add("&ge;", ">=");
			EscapeCharDicts.Add("&amp;", "&");
			EscapeCharDicts.Add("&nbsp;", " ");
		}

		public static string GetXMlContentById(string xmlName, string id, Func<string, bool> CalFormatter = null)
		{
			XmlNode xmlNode = null;
			try
			{
				XmlDocument xmlDocument = new XmlDocument();
				XmlReaderSettings xmlReaderSettings = new XmlReaderSettings();
				xmlReaderSettings.IgnoreComments = true;
				xmlReaderSettings.DtdProcessing = DtdProcessing.Ignore;
				XmlReader reader = XmlReader.Create(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "mapper\\" + xmlName + ".xml"), xmlReaderSettings);
				xmlDocument.Load(reader);
				XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDocument.NameTable);
				XmlElement documentElement = xmlDocument.DocumentElement;
				xmlNode = documentElement.SelectSingleNode("descendant::select[@id='" + id + "']", nsmgr) ?? documentElement.SelectSingleNode("descendant::insert[@id='" + id + "']", nsmgr) ?? documentElement.SelectSingleNode("descendant::update[@id='" + id + "']", nsmgr) ?? documentElement.SelectSingleNode("descendant::delete[@id='" + id + "']", nsmgr);
				if (xmlNode == null)
				{
					throw new Exception(id + "方法不存在~");
				}
				XmlNodeList childNodes = xmlNode.ChildNodes;
				if (childNodes != null)
				{
					foreach (object item in childNodes)
					{
						if (!(item is XmlElement xmlElement))
						{
							continue;
						}
						string attribute = xmlElement.GetAttribute("test");
						string attribute2 = xmlElement.GetAttribute("value");
						if (xmlElement.Name == "if" && !string.IsNullOrWhiteSpace(attribute))
						{
							xmlElement.InnerText = ((CalFormatter != null && CalFormatter.Invoke(attribute)) ? attribute2 : null);
						}
						string attribute3 = xmlElement.GetAttribute("refid");
						XmlNodeList childNodes2 = documentElement.ChildNodes;
						foreach (XmlNode item2 in childNodes2)
						{
							if (item2 is XmlElement { Name: "sql" } xmlElement2 && xmlElement2.GetAttribute("id") == attribute3)
							{
								xmlElement.InnerText = xmlElement2.InnerText;
								break;
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageHelper.ShowError(ex);
			}
			return xmlNode.InnerText;
		}

		public static List<SysUploadConfigInfo> ReadUploadConfig(string key, string path = null)
		{
			List<SysUploadConfigInfo> list = new List<SysUploadConfigInfo>();
			path = (string.IsNullOrEmpty(path) ? myConfigXmlPath : path);
			XmlDocument xmlDocument = new XmlDocument();
			xmlDocument.Load(path);
			XmlNodeList xmlNodeList = xmlDocument.SelectNodes("//UpLoadConfig");
			SysUploadConfigInfo sysUploadConfigInfo = null;
			Type type = new SysUploadConfigInfo().GetType();
			FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic);
			Dictionary<string, FieldInfo> dictionary = new Dictionary<string, FieldInfo>();
			FieldInfo[] array = fields;
			foreach (FieldInfo fieldInfo in array)
			{
				dictionary.Add(fieldInfo.Name, fieldInfo);
			}
			foreach (XmlElement item in xmlNodeList)
			{
				sysUploadConfigInfo = new SysUploadConfigInfo();
				XmlAttributeCollection attributes = item.Attributes;
				foreach (XmlAttribute item2 in attributes)
				{
					if (dictionary.ContainsKey(item2.Name))
					{
						if (dictionary[item2.Name].FieldType.ToString().Contains("System.Nullable"))
						{
							dictionary[item2.Name].SetValue(sysUploadConfigInfo, Convert.ChangeType(item2.InnerText, Nullable.GetUnderlyingType(dictionary[item2.Name].FieldType)));
						}
						else
						{
							dictionary[item2.Name].SetValue(sysUploadConfigInfo, Convert.ChangeType(item2.InnerText, dictionary[item2.Name].FieldType));
						}
					}
				}
				if (string.IsNullOrEmpty(key) || "*".Equals(key) || sysUploadConfigInfo.NodeCode.Equals(key))
				{
					list.Add(sysUploadConfigInfo);
				}
			}
			return list;
		}

		public static SysUploadConfigInfo FindOneUploadConfig(string key, string path = null)
		{
			List<SysUploadConfigInfo> list = ReadUploadConfig(key, path);
			return (list.Count > 0) ? list[0] : null;
		}

		public static List<TabPageConfig> FindAllTabPages(string model, string path = null)
		{
			return ReadUAllTabPages(model, path);
		}

		public static List<TabPageConfig> ReadUAllTabPages(string model, string path = null)
		{
			List<TabPageConfig> list = new List<TabPageConfig>();
			path = (string.IsNullOrEmpty(path) ? myConfigXmlPath : path);
			XmlDocument xmlDocument = new XmlDocument();
			xmlDocument.Load(path);
			XmlNodeList xmlNodeList = xmlDocument.SelectNodes("//TabPageConfigs//" + model);
			XmlNode xmlNode = xmlNodeList.Item(0);
			XmlAttributeCollection attributes = xmlNode.Attributes;
			string text = "select {columnName} from {tableName} limit 0,1";
			string value = null;
			string text2 = "";
			foreach (XmlAttribute item in attributes)
			{
				if ("tableName".Equals(item.Name))
				{
					text = text.Replace("{tableName}", item.InnerText);
				}
				else if ("columName".Equals(item.Name))
				{
					text = text.Replace("{columnName}", item.InnerText);
					value = item.InnerText;
				}
			}
			DataTable dataTable = SQLiteLibrary.SelectBySql(text);
			if (dataTable != null && dataTable.Rows.Count > 0)
			{
				text2 = dataTable.Rows[0].ItemArray[0].ToString();
				text2 = ((!"PURCHASE_TYPE".Equals(value)) ? text2 : ("01".Equals(text2) ? "物资招标" : text2));
			}
			XmlNodeList xmlNodeList2 = xmlDocument.SelectNodes("//TabPageConfigs//" + model + "//TabPage");
			Type typeFromHandle = typeof(TabPageConfig);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			Dictionary<string, PropertyInfo> dictionary = new Dictionary<string, PropertyInfo>();
			PropertyInfo[] array = properties;
			foreach (PropertyInfo propertyInfo in array)
			{
				dictionary.Add(propertyInfo.Name.ToLower(), propertyInfo);
			}
			foreach (XmlElement item2 in xmlNodeList2)
			{
				TabPageConfig tabPageConfig = new TabPageConfig();
				XmlAttributeCollection attributes2 = item2.Attributes;
				foreach (XmlAttribute item3 in attributes2)
				{
					string key = (item3.Name ?? "").ToLower();
					if (dictionary.ContainsKey(key))
					{
						dictionary[key].SetValue(tabPageConfig, Convert.ChangeType(item3.InnerText, dictionary[key].PropertyType), null);
					}
				}
				tabPageConfig.IsShow = "*".Equals(tabPageConfig.ShowCondition) || text2.StartsWith(tabPageConfig.ShowCondition);
				list.Add(tabPageConfig);
			}
			return list;
		}

		public static Dictionary<string, MarkType> GetMarkTypes(string path = null)
		{
			Dictionary<string, MarkType> dictionary = new Dictionary<string, MarkType>();
			path = (string.IsNullOrEmpty(path) ? myConfigXmlPath : path);
			XmlDocument xmlDocument = new XmlDocument();
			xmlDocument.Load(path);
			XmlNodeList xmlNodeList = xmlDocument.SelectNodes("//MarkTypes//MarkType");
			Type typeFromHandle = typeof(MarkType);
			PropertyInfo[] properties = typeFromHandle.GetProperties();
			Dictionary<string, PropertyInfo> dictionary2 = new Dictionary<string, PropertyInfo>();
			PropertyInfo[] array = properties;
			foreach (PropertyInfo propertyInfo in array)
			{
				dictionary2.Add(propertyInfo.Name.ToLower(), propertyInfo);
			}
			foreach (XmlElement item in xmlNodeList)
			{
				MarkType markType = new MarkType();
				XmlAttributeCollection attributes = item.Attributes;
				foreach (XmlAttribute item2 in attributes)
				{
					string key = (item2.Name ?? "").ToLower();
					if (dictionary2.ContainsKey(key))
					{
						dictionary2[key].SetValue(markType, Convert.ChangeType(item2.InnerText, dictionary2[key].PropertyType), null);
					}
				}
				if (!dictionary.ContainsKey(markType.Name))
				{
					dictionary.Add(markType.Name, markType);
				}
			}
			return dictionary;
		}
	}
	public class ScreenRlnRatio
	{
		[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
		public struct DEVMODE
		{
			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
			public string dmDeviceName;

			public short dmSpecVersion;

			public short dmDriverVersion;

			public short dmSize;

			public short dmDriverExtra;

			public int dmFields;

			public short dmOrientation;

			public short dmPaperSize;

			public short dmPaperLength;

			public short dmPaperWidth;

			public short dmScale;

			public short dmCopies;

			public short dmDefaultSource;

			public short dmPrintQuality;

			public short dmColor;

			public short dmDuplex;

			public short dmYResolution;

			public short dmTTOption;

			public short dmCollate;

			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
			public string dmFormName;

			public short dmLogPixels;

			public int dmBitsPerPel;

			public int dmPelsWidth;

			public int dmPelsHeight;

			public int dmDisplayFlags;

			public int dmDisplayFrequency;
		}

		private static int i = Screen.PrimaryScreen.Bounds.Width;

		private static int j = Screen.PrimaryScreen.Bounds.Height;

		[DllImport("user32.dll", CharSet = CharSet.Auto)]
		private static extern int ChangeDisplaySettings([In] ref DEVMODE lpDevMode, int dwFlags);

		[DllImport("user32.dll", CharSet = CharSet.Auto)]
		private static extern bool EnumDisplaySettings(string lpszDeviceName, int iModeNum, ref DEVMODE lpDevMode);

		public static void ChangeRatio(int width, int height, int refreshRatio = 60)
		{
			DEVMODE lpDevMode = default(DEVMODE);
			lpDevMode.dmSize = (short)Marshal.SizeOf(typeof(DEVMODE));
			bool flag = EnumDisplaySettings(null, 0, ref lpDevMode);
			lpDevMode.dmPelsWidth = width;
			lpDevMode.dmPelsHeight = height;
			lpDevMode.dmDisplayFrequency = refreshRatio;
			lpDevMode.dmBitsPerPel = 32;
			long value = ChangeDisplaySettings(ref lpDevMode, 0);
			Console.WriteLine(value);
		}

		public static void ResetRatio()
		{
			DEVMODE lpDevMode = default(DEVMODE);
			lpDevMode.dmSize = (short)Marshal.SizeOf(typeof(DEVMODE));
			bool flag = EnumDisplaySettings(null, 0, ref lpDevMode);
			lpDevMode.dmPelsWidth = i;
			lpDevMode.dmPelsHeight = j;
			lpDevMode.dmDisplayFrequency = 60;
			lpDevMode.dmBitsPerPel = 32;
			long num = ChangeDisplaySettings(ref lpDevMode, 0);
		}
	}
}
namespace MyCommon.tasks
{
	public class LimitedConcurrencyLevelTaskScheduler : TaskScheduler
	{
		[ThreadStatic]
		private static bool _currentThreadIsProcessingItems;

		private readonly LinkedList<Task> _tasks = new LinkedList<Task>();

		private readonly int _maxDegreeOfParallelism;

		private int _delegatesQueuedOrRunning = 0;

		public sealed override int MaximumConcurrencyLevel => _maxDegreeOfParallelism;

		public LimitedConcurrencyLevelTaskScheduler(int maxDegreeOfParallelism)
		{
			if (maxDegreeOfParallelism < 1)
			{
				throw new ArgumentOutOfRangeException("maxDegreeOfParallelism");
			}
			_maxDegreeOfParallelism = maxDegreeOfParallelism;
		}

		protected sealed override void QueueTask(Task task)
		{
			lock (_tasks)
			{
				_tasks.AddLast(task);
				if (_delegatesQueuedOrRunning < _maxDegreeOfParallelism)
				{
					_delegatesQueuedOrRunning++;
					NotifyThreadPoolOfPendingWork();
				}
			}
		}

		private void NotifyThreadPoolOfPendingWork()
		{
			ThreadPool.UnsafeQueueUserWorkItem(delegate
			{
				_currentThreadIsProcessingItems = true;
				try
				{
					while (true)
					{
						Task value;
						lock (_tasks)
						{
							if (_tasks.Count == 0)
							{
								_delegatesQueuedOrRunning--;
								break;
							}
							value = _tasks.First.Value;
							_tasks.RemoveFirst();
						}
						((TaskScheduler)this).TryExecuteTask(value);
					}
				}
				finally
				{
					_currentThreadIsProcessingItems = false;
				}
			}, null);
		}

		protected sealed override bool TryExecuteTaskInline(Task task, bool taskWasPreviouslyQueued)
		{
			if (!_currentThreadIsProcessingItems)
			{
				return false;
			}
			if (taskWasPreviouslyQueued)
			{
				if (((TaskScheduler)this).TryDequeue(task))
				{
					return ((TaskScheduler)this).TryExecuteTask(task);
				}
				return false;
			}
			return ((TaskScheduler)this).TryExecuteTask(task);
		}

		protected sealed override bool TryDequeue(Task task)
		{
			lock (_tasks)
			{
				return _tasks.Remove(task);
			}
		}

		protected sealed override IEnumerable<Task> GetScheduledTasks()
		{
			bool flag = false;
			try
			{
				Monitor.TryEnter((object)_tasks, ref flag);
				if (flag)
				{
					return _tasks;
				}
				throw new NotSupportedException();
			}
			finally
			{
				if (flag)
				{
					Monitor.Exit(_tasks);
				}
			}
		}
	}
}
namespace MyCommon.styles
{
	public class ControlStyle
	{
		private int fontSize;

		private Color foreColor;

		private Color backColor;

		private int width;

		private int height;

		public int FontSize
		{
			get
			{
				return fontSize;
			}
			set
			{
				fontSize = value;
			}
		}

		public Color ForeColor
		{
			get
			{
				return foreColor;
			}
			set
			{
				foreColor = value;
			}
		}

		public Color BackColor
		{
			get
			{
				return backColor;
			}
			set
			{
				backColor = value;
			}
		}

		public int Width
		{
			get
			{
				return width;
			}
			set
			{
				width = value;
			}
		}

		public int Height
		{
			get
			{
				return height;
			}
			set
			{
				height = value;
			}
		}
	}
	public class ControlStyleDictionary<Key>
	{
		private Dictionary<Key, ControlStyle> controlStyleDict = new Dictionary<Key, ControlStyle>();

		public void Add(Key name, ControlStyle style)
		{
			controlStyleDict[name] = style;
		}

		public bool Contain(Key name)
		{
			return controlStyleDict.ContainsKey(name);
		}

		public ControlStyle TryGetValue(Key name)
		{
			ControlStyle value = null;
			controlStyleDict.TryGetValue(name, out value);
			return value;
		}
	}
}
namespace MyCommon.structs
{
	public struct PdfPage
	{
		public int StartNumber { get; private set; }

		public int EndNumber { get; private set; }

		public void SetStartNumber(int startNumber)
		{
			StartNumber = startNumber;
		}

		public void SetEndNumber(int endNumber)
		{
			EndNumber = endNumber;
		}
	}
}
namespace MyCommon.serivce
{
	public interface ColumService
	{
		string DoFormat(string key, string value, string patternStr, int incrementValue);
	}
	public interface ICustomControl
	{
		string ControlName { get; }

		string GetValue();

		void SetValue(object value);
	}
}
namespace MyCommon.serivce.impl
{
	public class ColumServiceImpl : ColumService
	{
		protected readonly string[] NullValues = new string[3] { "''", "null", "NULL" };

		protected readonly Dictionary<string, string> TimeDict = new Dictionary<string, string>
		{
			{ "createtime", null },
			{ "create_time", null },
			{ "updatetime", null },
			{ "update_time", null }
		};

		public virtual string DoFormat(string key, string value, string patternStr, int incrementValue)
		{
			if (key.Equals("ID", StringComparison.CurrentCultureIgnoreCase))
			{
				return (!string.IsNullOrWhiteSpace(value) && !NullValues.Contains(value)) ? value : ("'" + Guid.NewGuid().ToString("N") + "'");
			}
			if (TimeDict.ContainsKey(key.ToLower()))
			{
				return "'" + DateTime.Now.AddMilliseconds(incrementValue).ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
			}
			return " to_timestamp(" + value + ",'" + patternStr + "')";
		}
	}
	public class DepositColumService : ColumServiceImpl
	{
		public string PrimaryKeyValue { get; private set; }

		public DepositColumService(string primaryKeyValue)
		{
			PrimaryKeyValue = primaryKeyValue;
		}

		public override string DoFormat(string key, string value, string patternStr, int incrementValue)
		{
			if ("id".Equals(key, StringComparison.OrdinalIgnoreCase))
			{
				return "'" + PrimaryKeyValue + "'";
			}
			return base.DoFormat(key, key, patternStr, incrementValue);
		}
	}
	public class PRAndImpExpService
	{
	}
}
namespace MyCommon.regs
{
	public class RegexConf
	{
		public static string Six_Bit_Num_Regex = "^\\d{1,8}(\\.|\\.\\d{1,6})?$";

		public static string Bit_Num_Regex = "^(([1-9]\\d{0,24})|0)$";

		public static string Bit_Decimal_Num_Regex = "^(([1-9]\\d{0,14})|0)(\\.)?(([1-9]\\d{0,14})|0)?$";

		public static string Percent_Num_Regex = "^(([1-9]\\d{0,1}|0)(\\.\\d{0,3})?|100)(%)?$";

		public const string Positive_Integer_Regex1 = "^([0-9]|[1-9][\\d]{1,8})$";

		public const string DayTime_Regex = "^((19|20)\\d{2})-(0[1-9]|1[0-2])-([0-2]\\d|3[01])$";

		public const string MonthTime_Regex = "^((19|20)\\d{2})-(0[1-9]|1[0-2])$";
	}
}
namespace MyCommon.options
{
	public class ExpertOption
	{
		private string businessId;

		private string headContent;

		private bool openOffice = true;

		public string BusinessId
		{
			get
			{
				return businessId;
			}
			set
			{
				businessId = value;
			}
		}

		public string HeadContent
		{
			get
			{
				return headContent;
			}
			set
			{
				headContent = value;
			}
		}

		public bool OpenOffice
		{
			get
			{
				return openOffice;
			}
			set
			{
				openOffice = value;
			}
		}
	}
}
namespace MyCommon.message
{
	public interface IBaseRecipient
	{
	}
	public interface IRecipient<TMessage> : IBaseRecipient where TMessage : SendMsg
	{
		void Receive(TMessage message, string flag = null);
	}
	public static class MessageConstant
	{
		public const string SupplierInfoChange = "SupplierInfoChange";

		public const string UploadProgress = "UploadProgress";

		public const string UploadSignFile = "UploadSignFile";

		public const string UpdateList = "UpdateList";

		public const string RefData = "RefData";

		public const string Auth = "Auth";

		public const string CloseSignal = "CloseSignal";

		public const string Refresh = "Refresh";
	}
	public interface SendMsg
	{
	}
	public class SValue<TValue> : SendMsg
	{
		public TValue Value { get; set; }

		internal SValue(TValue value)
		{
			Value = value;
		}

		internal SValue()
		{
		}
	}
	public class SValue<TValue1, TValue2> : SendMsg
	{
		public TValue1 Value1 { get; set; }

		public TValue2 Value2 { get; set; }

		internal SValue(TValue1 value1, TValue2 value2)
		{
			Value1 = value1;
			Value2 = value2;
		}

		internal SValue()
		{
		}
	}
	public class SValue<TValue1, TValue2, TValue3> : SendMsg
	{
		public TValue1 Value1 { get; set; }

		public TValue2 Value2 { get; set; }

		public TValue3 Value3 { get; set; }

		internal SValue(TValue1 value1, TValue2 value2, TValue3 value3)
		{
			Value1 = value1;
			Value2 = value2;
			Value3 = value3;
		}

		internal SValue()
		{
		}
	}
	public class SValueCreator
	{
		public static SValue<TValue> Build<TValue>(TValue tvalue)
		{
			return new SValue<TValue>(tvalue);
		}

		public static SValue<TValue1, TValue2> Build<TValue1, TValue2>(TValue1 tvalue1, TValue2 tvalue2)
		{
			return new SValue<TValue1, TValue2>(tvalue1, tvalue2);
		}

		public static SValue<TValue1, TValue2, TValue3> Build<TValue1, TValue2, TValue3>(TValue1 tvalue1, TValue2 tvalue2, TValue3 value3)
		{
			return new SValue<TValue1, TValue2, TValue3>(tvalue1, tvalue2, value3);
		}
	}
}
namespace MyCommon.jsonDto
{
	public class GenModelConfig
	{
		[JsonProperty("modelTitle")]
		public string ModelTitle { get; set; }

		[JsonProperty("visibility")]
		public bool Visibility { get; set; }

		[JsonProperty("first_category")]
		public string FirstCategory { get; set; }

		[JsonProperty("second_category")]
		public string SecondCategory { get; set; }
	}
}
namespace MyCommon.extendmethods
{
	public static class DictionaryExtension
	{
		public static void AddIfAbsent<TValue>(this Dictionary<string, TValue> sourceDict, string iKey, TValue tValue, Dictionary<string, string> filterDict)
		{
			if (!sourceDict.ContainsKey(iKey) && filterDict.ContainsKey(iKey))
			{
				sourceDict.Add(iKey, tValue);
			}
		}

		public static void AddIfAbsent<TKey, TValue>(this Dictionary<TKey, TValue> sourceDict, TKey iKey, TValue tValue)
		{
			if (!sourceDict.ContainsKey(iKey))
			{
				sourceDict.Add(iKey, tValue);
			}
		}

		public static void AddIfNotAbsent<TKey, TValue>(this Dictionary<TKey, TValue> sourceDict, TKey iKey, TValue tValue)
		{
			if (!sourceDict.ContainsKey(iKey))
			{
				sourceDict.Add(iKey, tValue);
			}
			else
			{
				sourceDict[iKey] = tValue;
			}
		}

		public static TValue GetValueByForce<TKey, TValue>(this Dictionary<TKey, TValue> sourceDict, TKey iKey)
		{
			if (sourceDict.ContainsKey(iKey))
			{
				return sourceDict[iKey];
			}
			return default(TValue);
		}
	}
	public static class IEmumerableExtension
	{
		public static Dictionary<TKey, IValue> ToDictionaryByLocal<TSource, TKey, IValue>(this IEnumerable<TSource> source, Func<TSource, TKey> keyer, Func<TSource, IValue> valuer, Dictionary<TKey, IValue> dict = null, bool addIfNotAbsent = true)
		{
			dict = dict ?? new Dictionary<TKey, IValue>();
			if (source == null)
			{
				return null;
			}
			if (keyer == null)
			{
				throw new ArgumentNullException("keyer");
			}
			if (valuer == null)
			{
				throw new ArgumentNullException("valuer");
			}
			foreach (TSource item in source)
			{
				TKey key = keyer.Invoke(item);
				if (!dict.ContainsKey(key))
				{
					dict.Add(key, valuer.Invoke(item));
				}
				else if (addIfNotAbsent)
				{
					dict[key] = valuer.Invoke(item);
				}
			}
			return dict;
		}
	}
	public static class LinqExtend
	{
		public static IEnumerable<TSource> WhereWithDistinct<TSource>(this IEnumerable<TSource> source, params Func<TSource, object>[] funcs)
		{
			ISet<string> set = new HashSet<string>();
			Func<TSource, bool> predicate = delegate(TSource v)
			{
				List<object> list = ((IEnumerable<Func<TSource, object>>)funcs).Select<Func<TSource, object>, object>((Func<Func<TSource, object>, object>)((Func<TSource, object> f) => f.Invoke(v))).ToList();
				return set.Add(string.Join<object>("-&-", (IEnumerable<object>)list));
			};
			return source.Where(predicate);
		}
	}
	public static class StringBuilderExtend
	{
		public static bool AppendLineLimit(this StringBuilder sbd, string text, int maxLength = 600, string suffix = "\r\n.....")
		{
			int valueOrDefault = (sbd.Length + text?.Length).GetValueOrDefault();
			if (maxLength > valueOrDefault)
			{
				sbd.AppendLine(text);
				return true;
			}
			if (suffix != null && !sbd.ToString().EndsWith(suffix.Replace("\r\n", "")))
			{
				sbd.Append(suffix);
			}
			return false;
		}

		public static bool AppendLimit(this StringBuilder sbd, string text, int maxLength = 1024, string suffix = "\r\n.....")
		{
			int valueOrDefault = (sbd.Length + text?.Length).GetValueOrDefault();
			if (maxLength > valueOrDefault)
			{
				sbd.Append(text);
				return true;
			}
			if (suffix != null && !sbd.ToString().Contains(suffix))
			{
				sbd.Append(suffix);
			}
			return false;
		}
	}
	public static class StringExtend
	{
		public static bool IsNullOrWhiteSpace(this string strThis, string str)
		{
			if (str == null)
			{
				return true;
			}
			return string.IsNullOrEmpty(str);
		}

		public static string UpperCaseFirst(this string strThis)
		{
			if (strThis == null)
			{
				return strThis;
			}
			char[] array = strThis.ToCharArray();
			if (array[0] >= 'a' && array[0] <= 'z')
			{
				array[0] = char.ToUpper(array[0]);
			}
			return new string(array);
		}
	}
	public static class TypeExtend
	{
		public static T GetCustomAttribute<T>(this Type type)
		{
			object[] customAttributes = type.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static T GetCustomAttribute<T>(this FieldInfo field)
		{
			object[] customAttributes = field.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static T GetCustomAttribute<T>(this MemberInfo field)
		{
			object[] customAttributes = field.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static T GetCustomAttribute<T>(this PropertyInfo field)
		{
			object[] customAttributes = field.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static object GetValue<T>(this PropertyInfo prop, T t)
		{
			return prop.GetValue(t, null);
		}

		public static void SetValue<T>(this PropertyInfo prop, T t, object content)
		{
			prop.SetValue(t, content, null);
		}
	}
	public delegate R ContinueToDoSomeThing(string api);
	public static class SwitchingLineExtesion
	{
		public static bool mulLines = "TRUE" == ApiConfigHelper.GetValueByKey("mulLines");

		public static R TryContinueToDo(this R result, string newApi, object paramData, int timeout = 100000, bool? needDecrypt = null)
		{
			if (!mulLines)
			{
				return result;
			}
			if (result.Code == 0 && result.Successful)
			{
				return result;
			}
			string relateApi = GetRelateApi(newApi);
			relateApi = Regex.Replace(relateApi, "^(\\\\|//)", "");
			string valueByKey = ApiConfigHelper.GetValueByKey("dataApiPrefixUrl2");
			return WebRequestUtil.HttpPost(valueByKey + relateApi, paramData, timeout, needDecrypt ?? result.NeedDecrypt);
		}

		public static R TryContinueToDo(this R result, string newApi, ContinueToDoSomeThing func)
		{
			if (!mulLines)
			{
				return result;
			}
			if (result.Code == 0 && result.Successful)
			{
				return result;
			}
			string[] array = Regex.Split(newApi, "\\?rnd");
			string[] array2 = Regex.Split(array[0], "/api/rest/");
			string input = ((array2.Length > 1) ? array2[1] : array2[0]);
			input = Regex.Replace(input, "^(\\\\|//)", "");
			string valueByKey = ApiConfigHelper.GetValueByKey("dataApiPrefixUrl2");
			return func(valueByKey + input);
		}

		private static string GetRelateApi(string newApi)
		{
			string[] array = Regex.Split(newApi, "\\?rnd");
			string[] array2 = Regex.Split(array[0], "/api/rest/");
			return (array2.Length > 1) ? array2[1] : array2[0];
		}
	}
}
namespace MyCommon.exception
{
	public class RunTimeException : Exception
	{
		public string AdditionalInfo { get; set; }

		public override string ToString()
		{
			return base.ToString() + "\r\n" + AdditionalInfo;
		}
	}
}
namespace MyCommon.enums
{
	public enum PageStatusEnum
	{
		None,
		DisableAll
	}
	public enum SqlFunctionEnum
	{
		None,
		Ave,
		Sum,
		Min,
		Max,
		Count,
		PlaceHolder
	}
	public class HttpState
	{
		private ManualResetEvent wait;

		private DateTime beginTime;

		private Exception operationException = null;

		public long Size { get; set; }

		public HttpWebRequest Request { get; set; }

		public string FullName { get; set; }

		public string StatusDescription { get; set; }

		public ManualResetEvent OperationComplete => wait;

		public Exception OperationException
		{
			get
			{
				return operationException;
			}
			set
			{
				operationException = value;
			}
		}

		public byte[] PostHeaderBytes { get; set; }

		public byte[] BoundaryBytes { get; set; }

		public HttpState()
		{
			wait = new ManualResetEvent(initialState: false);
			beginTime = DateTime.Now;
		}
	}
	public class MyDirectory
	{
		public int Level { get; set; }

		public DirectoryInfo Dir { get; set; }

		public MyDirectory(int level, DirectoryInfo dir)
		{
			Level = level;
			Dir = dir;
		}
	}
}
namespace MyCommon.constants
{
	public class SubConfigConstant
	{
		public const string NEED_PRICE_FILE = "NEED_PRICE_FILE";

		public const string TECH_SPECIFY_ID = "TECH_SPECIFY_ID";
	}
}
namespace MyCommon.condition
{
	public class ConditionExp<TEntity>
	{
		public List<Expression<Func<OsZbPurchaseProjectInfo, object>>> Colums { get; set; } = new List<Expression<Func<OsZbPurchaseProjectInfo, object>>>();


		public Func<OsZbPurchaseProjectInfo, object> ConditionFunc { get; set; }

		public Func<OsZbPurchaseProjectInfo, string> WhereFunc { get; set; }

		public Func<OsZbPurchaseProjectInfo, string> GroupByFunc { get; set; }

		public ConditionExp(Func<OsZbPurchaseProjectInfo, object> conditionFunc, Func<OsZbPurchaseProjectInfo, string> whereFunc, Func<OsZbPurchaseProjectInfo, string> groupByFunc, params Expression<Func<OsZbPurchaseProjectInfo, object>>[] columns)
		{
			ConditionFunc = conditionFunc;
			WhereFunc = whereFunc;
			GroupByFunc = groupByFunc;
			Colums = columns?.ToList();
		}
	}
	public class CoverHashSet<T> : ICollection<T>, IEnumerable<T>, IEnumerable, ISet<T>
	{
		private Dictionary<T, string> dict = new Dictionary<T, string>();

		public int Count => dict.Count;

		public bool IsReadOnly => false;

		public bool Add(T item)
		{
			if (item == null)
			{
				return false;
			}
			dict[item] = null;
			return true;
		}

		public bool Add(T item, bool coverOldValue)
		{
			if (item == null)
			{
				return false;
			}
			if (!coverOldValue && dict.ContainsKey(item))
			{
				return false;
			}
			dict[item] = null;
			return true;
		}

		public void Clear()
		{
			dict.Clear();
		}

		public bool Contains(T item)
		{
			return dict.ContainsKey(item);
		}

		public void CopyTo(T[] array, int arrayIndex)
		{
			throw new NotImplementedException();
		}

		public void ExceptWith(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public IEnumerator<T> GetEnumerator()
		{
			return dict?.Keys.GetEnumerator();
		}

		public void IntersectWith(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool IsProperSubsetOf(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool IsProperSupersetOf(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool IsSubsetOf(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool IsSupersetOf(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool Overlaps(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public bool Remove(T item)
		{
			return dict.Remove(item);
		}

		public bool SetEquals(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public void SymmetricExceptWith(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		public void UnionWith(IEnumerable<T> other)
		{
			throw new NotImplementedException();
		}

		void ICollection<T>.Add(T item)
		{
			throw new NotImplementedException();
		}

		IEnumerator IEnumerable.GetEnumerator()
		{
			throw new NotImplementedException();
		}
	}
	public enum WhereCondition
	{
		AND,
		OR
	}
	public enum OrderCondition
	{
		Asc,
		Desc
	}
	public enum JoinEnum
	{
		INNER,
		LEFT,
		RIGHT
	}
	public enum SqlStrategy
	{
		Select = 0,
		Insert = 1,
		Update = 2,
		Delete = 3,
		DataMigration = 1
	}
	public enum SqlSymbol
	{
		None,
		BracketLeft,
		BracketRight
	}
	public class Table
	{
		public string Name { get; set; }

		public string Alias { get; set; }

		public Dictionary<string, string> FieldColumnDict { get; set; } = new Dictionary<string, string>();


		public Table(string name, string alias)
		{
			Name = name;
			Alias = alias;
		}
	}
	public class SqlWrapper<TEntity> where TEntity : CommonEntity
	{
		public Expression<Func<TEntity, object>>[] Columns = new Expression<Func<TEntity, object>>[0];

		internal ISet<string> ExcludeColumns = new HashSet<string>();

		internal ISet<string> ExtraColumns = new HashSet<string>();

		internal ISet<string> ExtraOrderByColumns = new HashSet<string>();

		internal ISet<string> ExtraGroupByColumns = new HashSet<string>();

		internal ISet<string> WhereColums = new HashSet<string>();

		internal Dictionary<Expression<Func<TEntity, object>>, string> OrderByDict = new Dictionary<Expression<Func<TEntity, object>>, string>();

		internal Dictionary<Type, Table> TableDict = new Dictionary<Type, Table>();

		internal IList<string> JoinSqlList = new List<string>();

		internal string havingFragment = "";

		internal static readonly Regex removePattern = new Regex("[\\(\\（\\[\\[#\\)\\）]{1,6}");

		public string DbScheme { get; set; }

		public TEntity QueryOrEdit { get; set; }

		public string MainTableAlias { get; private set; } = "t0";


		public int PageNumber { get; set; } = -1;


		public int PageSize { get; set; } = 10;


		public SqlWrapper()
		{
		}

		public SqlWrapper(TEntity queryOrEdit)
		{
			QueryOrEdit = queryOrEdit;
			SetTableDict(typeof(TEntity));
		}

		public SqlWrapper(TEntity queryOrEdit, params Expression<Func<TEntity, object>>[] columns)
		{
			QueryOrEdit = queryOrEdit;
			if (columns == null)
			{
				throw new ArgumentException("columns");
			}
			Columns = columns;
			SetTableDict(typeof(TEntity));
		}

		public void ClearCondition()
		{
			WhereColums?.Clear();
			ExtraOrderByColumns?.Clear();
			ExtraGroupByColumns?.Clear();
		}

		public SqlWrapper<TEntity> PageConfig(int pageNumber, int pageSize)
		{
			PageNumber = pageNumber;
			PageSize = pageSize;
			return this;
		}

		internal void SetTableDict(Type type)
		{
			if (TableDict.ContainsKey(type))
			{
				return;
			}
			TableNameAttribute[] array = (TableNameAttribute[])type.GetCustomAttributes(typeof(TableNameAttribute), inherit: false);
			array = ((array.Length != 0) ? array : new TableNameAttribute[1]
			{
				new TableNameAttribute(type.Name)
			});
			Table table = new Table(array[0].Value, $"t{TableDict.Count}");
			MemberInfo[] allMembers = SQLiteSqlUtils.GetAllMembers(type);
			MemberInfo[] array2 = allMembers;
			foreach (MemberInfo memberInfo in array2)
			{
				TableFieldAttribute[] array3 = (TableFieldAttribute[])memberInfo.GetCustomAttributes(typeof(TableFieldAttribute), inherit: false);
				if (array3.Length != 0 && array3[0].IsExist)
				{
					table.FieldColumnDict[memberInfo.Name.ToUpper()] = array3[0].ColumName;
				}
			}
			TableDict[type] = table;
		}

		public SqlWrapper<TEntity> Join<TEntityJoin1>(Expression<Func<TEntityJoin1, TEntity, bool>> defineExpression, JoinEnum joinEnum = JoinEnum.INNER, params string[] values) where TEntityJoin1 : CommonEntity
		{
			if (this != null && TableDict?.Count >= 5)
			{
				throw new Exception("禁止超过5张表关联");
			}
			Type typeFromHandle = typeof(TEntityJoin1);
			SetTableDict(typeFromHandle);
			SqlExpressionVisitor<TEntity> sqlExpressionVisitor = new SqlExpressionVisitor<TEntity>();
			sqlExpressionVisitor.SetShareWrapper(this);
			sqlExpressionVisitor.Visit(defineExpression.Body);
			string text = sqlExpressionVisitor.GetSeqmentSql();
			if (values != null && values.Length != 0)
			{
				for (int i = 0; i < values.Length; i++)
				{
					text = text.Replace($"'@con{i}'", values[i]).Replace($"@const{i}", values[i]);
				}
			}
			switch (joinEnum)
			{
			case JoinEnum.INNER:
				JoinSqlList.Add("INNER JOIN " + TableDict[typeFromHandle].Name + " " + TableDict[typeFromHandle].Alias + " ON " + text + " ");
				break;
			case JoinEnum.LEFT:
				JoinSqlList.Add("LEFT JOIN " + TableDict[typeFromHandle].Name + " " + TableDict[typeFromHandle].Alias + " ON " + text + " ");
				break;
			case JoinEnum.RIGHT:
				JoinSqlList.Add("RIGHT JOIN " + TableDict[typeFromHandle].Name + " " + TableDict[typeFromHandle].Alias + " ON " + text + " ");
				break;
			default:
				JoinSqlList.Add("LEFT JOIN " + TableDict[typeFromHandle].Name + " " + TableDict[typeFromHandle].Alias + " ON " + text + " ");
				break;
			}
			return this;
		}

		public SqlWrapper<TEntity> Join<TEntityJoin1, TEntityJoin2>(Expression<Func<TEntityJoin1, TEntityJoin2, bool>> defineExpression, JoinEnum joinEnum = JoinEnum.INNER, string aliaName = null) where TEntityJoin1 : CommonEntity where TEntityJoin2 : CommonEntity
		{
			if (this != null && TableDict?.Count >= 5)
			{
				throw new Exception("禁止超过5张表关联");
			}
			Type typeFromHandle = typeof(TEntityJoin1);
			SetTableDict(typeFromHandle);
			Type typeFromHandle2 = typeof(TEntityJoin2);
			SetTableDict(typeFromHandle2);
			SqlExpressionVisitor<TEntity> sqlExpressionVisitor = new SqlExpressionVisitor<TEntity>();
			sqlExpressionVisitor.SetShareWrapper(this);
			sqlExpressionVisitor.Visit(defineExpression.Body);
			string text = sqlExpressionVisitor.GetSeqmentSql();
			if (!string.IsNullOrWhiteSpace(text) && !string.IsNullOrEmpty(aliaName))
			{
				text = text.Replace(TableDict[typeFromHandle].Alias + ".", aliaName + ".");
			}
			switch (joinEnum)
			{
			case JoinEnum.INNER:
				JoinSqlList.Add("INNER JOIN " + TableDict[typeFromHandle].Name + " " + (aliaName ?? TableDict[typeFromHandle].Alias) + " ON " + text + " ");
				break;
			case JoinEnum.LEFT:
				JoinSqlList.Add("LEFT JOIN " + TableDict[typeFromHandle].Name + " " + (aliaName ?? TableDict[typeFromHandle].Alias) + " ON " + text + " ");
				break;
			case JoinEnum.RIGHT:
				JoinSqlList.Add("RIGHT JOIN " + TableDict[typeFromHandle].Name + " " + (aliaName ?? TableDict[typeFromHandle].Alias) + " ON " + text + " ");
				break;
			default:
				JoinSqlList.Add("INNER JOIN " + TableDict[typeFromHandle].Name + " " + (aliaName ?? TableDict[typeFromHandle].Alias) + " ON " + text + " ");
				break;
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraColumn<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, string aliasName = null, SqlFunctionEnum sqlFunctionEnum = SqlFunctionEnum.None) where TEntityJoin : CommonEntity
		{
			Type typeFromHandle = typeof(TEntityJoin);
			if (!TableDict.ContainsKey(typeFromHandle))
			{
				throw new Exception("未关联此实体:" + typeFromHandle.Name);
			}
			string lastExpression = SQLiteSqlUtils.GetLastExpression(func);
			string text = "#[col]";
			switch (sqlFunctionEnum)
			{
			case SqlFunctionEnum.Ave:
				text = "Ave(#[col])";
				break;
			case SqlFunctionEnum.Sum:
				text = "Sum(#[col])";
				break;
			case SqlFunctionEnum.Min:
				text = "Min(#[col])";
				break;
			case SqlFunctionEnum.Max:
				text = "Max(#[col])";
				break;
			case SqlFunctionEnum.Count:
				text = "Count(#[col])";
				break;
			}
			Table table = TableDict[typeFromHandle];
			string alias = table.Alias;
			if (table.FieldColumnDict.TryGetValue(lastExpression, out var value))
			{
				text = text.Replace("#[col]", alias + "." + value);
				ExtraColumns.Add(string.IsNullOrWhiteSpace(aliasName) ? text : (text + " as " + aliasName));
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraColumnWithCustom<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, string aliasName, string funcFormat) where TEntityJoin : CommonEntity
		{
			Type typeFromHandle = typeof(TEntityJoin);
			if (!TableDict.ContainsKey(typeFromHandle))
			{
				throw new Exception("未关联此实体:" + typeFromHandle.Name);
			}
			string lastExpression = SQLiteSqlUtils.GetLastExpression(func);
			Table table = TableDict[typeFromHandle];
			string alias = table.Alias;
			if (table.FieldColumnDict.TryGetValue(lastExpression, out var value))
			{
				funcFormat = funcFormat.Replace("#[col]", alias + "." + value);
				ExtraColumns.Add(string.IsNullOrWhiteSpace(aliasName) ? funcFormat : (funcFormat + " as " + aliasName));
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraColumn(string sqlFragment)
		{
			if (!string.IsNullOrEmpty(sqlFragment))
			{
				ExtraColumns.Add(sqlFragment);
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraColumn<TEntityJoin>(params Expression<Func<TEntityJoin, object>>[] funcs) where TEntityJoin : CommonEntity
		{
			Type typeFromHandle = typeof(TEntityJoin);
			if (!TableDict.ContainsKey(typeFromHandle))
			{
				throw new Exception("未关联此实体:" + typeFromHandle.Name);
			}
			foreach (Expression<Func<TEntityJoin, object>> func in funcs)
			{
				ExtraColumn(func, "");
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraGroupBy<TEntityJoin>(params Expression<Func<TEntityJoin, object>>[] funcs) where TEntityJoin : CommonEntity
		{
			return CoreExtraColumn(ExtraGroupByColumns, null, funcs);
		}

		public SqlWrapper<TEntity> ExtraOrderBy<TEntityJoin>(OrderCondition orderBy = OrderCondition.Asc, params Expression<Func<TEntityJoin, object>>[] funcs) where TEntityJoin : CommonEntity
		{
			return CoreExtraColumn(ExtraOrderByColumns, orderBy.ToString(), funcs);
		}

		private SqlWrapper<TEntity> CoreExtraColumn<TEntityJoin>(ISet<string> extraColumns, string flag, params Expression<Func<TEntityJoin, object>>[] funcs) where TEntityJoin : CommonEntity
		{
			if (funcs.Length == 0)
			{
				throw new Exception("表达式不得为空");
			}
			Type typeFromHandle = typeof(TEntityJoin);
			if (!TableDict.ContainsKey(typeFromHandle))
			{
				throw new Exception("未关联此实体:" + typeFromHandle.Name);
			}
			Table table = TableDict[typeFromHandle];
			string alias = table.Alias;
			foreach (Expression<Func<TEntityJoin, object>> func in funcs)
			{
				string lastExpression = SQLiteSqlUtils.GetLastExpression(func);
				if (table.FieldColumnDict.TryGetValue(lastExpression, out var value))
				{
					extraColumns.Add(alias + "." + value + " " + flag);
				}
			}
			return this;
		}

		public SqlWrapper<TEntity> ExtraEq<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " = #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraNotEq<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " != #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraIsNull<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			SetPartWhereSql(func, " IS NULL ", null, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraIsNotNull<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			SetPartWhereSql(func, " IS NOT NULL ", null, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraGt<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " > #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraLt<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " < #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraGtOrEq<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " >= #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraLtOrEq<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, object value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " <= #[col] ", value, whereCondition, useSymbol: true, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraIn<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, IEnumerable<object> value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || !value.Any())
			{
				return this;
			}
			StringBuilder stringBuilder = new StringBuilder();
			foreach (object item in value)
			{
				if (item.GetType() == typeof(string) && !((string)item).StartsWith("'"))
				{
					stringBuilder.Append($"'{item}',");
				}
				else
				{
					stringBuilder.Append($"{item},");
				}
			}
			SetPartWhereSql(func, " IN (#[col]) ", stringBuilder.ToString().TrimEnd(','), whereCondition, useSymbol: false, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraNotIn<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, IEnumerable<object> value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || !value.Any())
			{
				return this;
			}
			StringBuilder stringBuilder = new StringBuilder();
			foreach (object item in value)
			{
				if (item.GetType() == typeof(string) && !((string)item).StartsWith("'"))
				{
					stringBuilder.Append($"'{item}',");
				}
				else
				{
					stringBuilder.Append($"{item},");
				}
			}
			SetPartWhereSql(func, " NOT IN (#[col]) ", stringBuilder.ToString().TrimEnd(','), whereCondition, useSymbol: false, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraLike<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, string value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " LIKE '%#[col]%' ", value, whereCondition, useSymbol: false, sqlSymbol);
			return this;
		}

		public SqlWrapper<TEntity> ExtraNotLike<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, string value, WhereCondition whereCondition = WhereCondition.AND, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			if (value == null || string.IsNullOrEmpty(value.ToString()))
			{
				return this;
			}
			SetPartWhereSql(func, " NOT LIKE '%#[col]%' ", value, whereCondition, useSymbol: false, sqlSymbol);
			return this;
		}

		internal void SetPartWhereSql<TEntityJoin>(Expression<Func<TEntityJoin, object>> func, string placeHolder, object value, WhereCondition condition, bool useSymbol = true, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntityJoin : CommonEntity
		{
			Dictionary<SqlSymbol, string> dictionary = new Dictionary<SqlSymbol, string>
			{
				{
					SqlSymbol.BracketLeft,
					"("
				},
				{
					SqlSymbol.BracketRight,
					")"
				}
			};
			StringBuilder stringBuilder = new StringBuilder();
			Type typeFromHandle = typeof(TEntityJoin);
			if (!TableDict.ContainsKey(typeFromHandle))
			{
				throw new Exception("未关联此实体:" + typeFromHandle.Name);
			}
			Table table = TableDict[typeFromHandle];
			string lastExpression = GetLastExpression(func);
			if (table.FieldColumnDict.TryGetValue(lastExpression, out var value2))
			{
				if (sqlSymbol == SqlSymbol.BracketLeft)
				{
					stringBuilder.Append($" {condition} {dictionary[sqlSymbol]} {table?.Alias}.{value2} ");
				}
				else
				{
					stringBuilder.Append($" {condition} {table?.Alias}.{value2} ");
				}
			}
			if (value is string text)
			{
				stringBuilder.Append(placeHolder.Replace("#[col]", useSymbol ? ("'" + text + "'") : (text ?? "")));
			}
			else if (value is DateTime dateTime)
			{
				stringBuilder.Append(placeHolder.Replace("#[col]", "'" + dateTime.ToString("yyyy-mm-dd") + "'"));
			}
			else
			{
				stringBuilder.Append(placeHolder.Replace("#[col]", $"{value}"));
			}
			if (sqlSymbol == SqlSymbol.BracketRight)
			{
				stringBuilder.Append(" ) ");
			}
			WhereColums.Add(stringBuilder.ToString());
		}

		public void SetWhereColumns(string wherecolumns)
		{
			WhereColums.Add(wherecolumns);
		}

		public string GetLastExpression<T>(Expression<Func<T, object>> func) where T : CommonEntity
		{
			string[] array = func.Body.ToString().Split('.');
			string input = ((array.Length > 1) ? array[1] : array[0]);
			return removePattern.Replace(input, string.Empty).ToUpper();
		}

		public override bool Equals(object obj)
		{
			SqlWrapper<TEntity> sqlWrapper = obj as SqlWrapper<TEntity>;
			bool flag = sqlWrapper != null && QueryOrEdit.GetType() == sqlWrapper.QueryOrEdit.GetType() && DbScheme == sqlWrapper.DbScheme && MainTableAlias == sqlWrapper.MainTableAlias && PageNumber == sqlWrapper.PageNumber && ExtraColumns.Count == sqlWrapper.ExtraColumns.Count && ExtraOrderByColumns.Count == sqlWrapper.ExtraOrderByColumns.Count && WhereColums.Count == sqlWrapper.WhereColums.Count && JoinSqlList.Count == sqlWrapper.JoinSqlList.Count && PageSize == sqlWrapper.PageSize;
			if (!flag)
			{
				return false;
			}
			foreach (string extraColumn in sqlWrapper.ExtraColumns)
			{
				if (!ExtraColumns.Contains(extraColumn))
				{
					return false;
				}
			}
			foreach (KeyValuePair<Expression<Func<TEntity, object>>, string> item in sqlWrapper.OrderByDict)
			{
				if (!OrderByDict.ContainsValue(item.Value))
				{
					return false;
				}
			}
			foreach (string extraOrderByColumn in sqlWrapper.ExtraOrderByColumns)
			{
				if (!ExtraOrderByColumns.Contains(extraOrderByColumn))
				{
					return false;
				}
			}
			foreach (string extraGroupByColumn in sqlWrapper.ExtraGroupByColumns)
			{
				if (!ExtraGroupByColumns.Contains(extraGroupByColumn))
				{
					return false;
				}
			}
			foreach (string whereColum in sqlWrapper.WhereColums)
			{
				if (!WhereColums.Contains(whereColum))
				{
					return false;
				}
			}
			foreach (string joinSql in sqlWrapper.JoinSqlList)
			{
				if (!JoinSqlList.Contains(joinSql))
				{
					return false;
				}
			}
			return flag;
		}

		public SqlWrapper<TEntity> ExtraHaving(string having)
		{
			havingFragment = " having " + having;
			return this;
		}
	}
}
namespace MyCommon.condition.factory
{
	public class SqlWrapperFactory
	{
		private SqlWrapperFactory()
		{
		}

		public static SqlWrapper<TEntity> Instance<TEntity>(TEntity query) where TEntity : CommonEntity
		{
			return new SqlWrapper<TEntity>(query);
		}

		public static SqlWrapper<TEntity> Instance<TEntity>() where TEntity : CommonEntity, new()
		{
			return new SqlWrapper<TEntity>(new TEntity());
		}

		public static SqlWrapper<TEntity> Instance<TEntity>(TEntity query, params Expression<Func<TEntity, object>>[] columns) where TEntity : CommonEntity
		{
			return new SqlWrapper<TEntity>(query, columns);
		}

		public static SqlWrapper<TEntity> Instance<TEntity>(params Expression<Func<TEntity, object>>[] columns) where TEntity : CommonEntity, new()
		{
			return new SqlWrapper<TEntity>(new TEntity(), columns);
		}
	}
	public static class SqlWrapperExtesion
	{
		private static readonly Dictionary<Type, Func<object, string>> handler = new Dictionary<Type, Func<object, string>>
		{
			{
				typeof(int),
				(object value) => value.ToString()
			},
			{
				typeof(int?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(long),
				(object value) => value.ToString()
			},
			{
				typeof(long?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(short),
				(object value) => value.ToString()
			},
			{
				typeof(short?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(float),
				(object value) => value.ToString()
			},
			{
				typeof(float?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(double),
				(object value) => value.ToString()
			},
			{
				typeof(double?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(decimal),
				(object value) => value.ToString()
			},
			{
				typeof(decimal?),
				(object value) => (value == null) ? "NULL" : $"{value}"
			},
			{
				typeof(string),
				(object value) => (value == null) ? "NULL" : $"'{value}'"
			},
			{
				typeof(DateTime),
				(object value) => (value == null) ? "NULL" : $"'{(DateTime)value:yyyy-MM-dd HH:mm:ss}'"
			},
			{
				typeof(DateTime?),
				(object value) => (value == null) ? "NULL" : $"'{(DateTime)value:yyyy-MM-dd HH:mm:ss}'"
			},
			{
				typeof(DateTimeOffset),
				(object value) => (value == null) ? "NULL" : $"'{(DateTime)value:yyyy-MM-dd HH:mm:ss}'"
			},
			{
				typeof(DateTimeOffset?),
				(object value) => (value == null) ? "NULL" : $"'{(DateTime)value:yyyy-MM-dd HH:mm:ss}'"
			},
			{
				typeof(bool),
				(object value) => ((bool)value) ? "1" : "0"
			},
			{
				typeof(bool?),
				(object value) => ((bool?)value == true) ? "1" : "0"
			}
		};

		private static readonly Dictionary<SqlFunctionEnum, string> sqlFuncDict = new Dictionary<SqlFunctionEnum, string>
		{
			{
				SqlFunctionEnum.None,
				null
			},
			{
				SqlFunctionEnum.Ave,
				" AVE(#[col]) AVE_0 "
			},
			{
				SqlFunctionEnum.Sum,
				" SUM(#[col]) SUM_0 "
			},
			{
				SqlFunctionEnum.Max,
				" MAX(#[col]) MAX_0 "
			},
			{
				SqlFunctionEnum.Min,
				" MIN(#[col]) MIN_0 "
			},
			{
				SqlFunctionEnum.Count,
				" COUNT(0) COUNT_0 "
			},
			{
				SqlFunctionEnum.PlaceHolder,
				" #[placeHolder] "
			}
		};

		internal static readonly Regex removePattern = new Regex("[\\(\\（\\[\\[#\\)\\）]{1,6}");

		public static SqlWrapper<TEntity> ExcludeColumn<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func) where TEntity : CommonEntity
		{
			string lastExpression = wrapperThis.GetLastExpression(func);
			wrapperThis.ExcludeColumns.Add(lastExpression);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> Eq<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " = #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> NotEq<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " != #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> Gt<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " > #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> Lt<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " < #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> GtOrEq<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " >= #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> LtOrEq<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " <= #[col] ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> Like<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " LIKE '%#[col]%' ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> LikeRight<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " LIKE '#[col]%' ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> NotLike<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " NOT LIKE '%#[col]%' ", value, sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> IsNull<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " IS NULL ", value ?? " IS NULL ", sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> IsNullOrEmpty<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			Table table = wrapperThis.TableDict[typeFromHandle];
			string[] array = func.Body.ToString().Split('.');
			string input = ((array.Length > 1) ? array[1] : array[0]).ToUpper();
			input = removePattern.Replace(input, string.Empty).ToUpper();
			if (table.FieldColumnDict.TryGetValue(input, out var value2))
			{
				wrapperThis.SetWhereColumns($"{whereCondition} ({value2} IS NULL OR {value2} = '' )");
			}
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> IsNotNull<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition = WhereCondition.AND, object value = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			SetPartWhereCondition(wrapperThis, func, whereCondition, " IS NOT NULL ", value ?? " IS NOT NULL ", sqlSymbol);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> In<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> columFunc, Expression<Func<TEntity, IEnumerable<object>>> valueFunc, WhereCondition whereCondition = WhereCondition.AND) where TEntity : CommonEntity
		{
			IEnumerable<object> value = valueFunc.Compile().Invoke(wrapperThis.QueryOrEdit);
			wrapperThis.ExtraIn(columFunc, value, whereCondition);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> In<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> columFunc, IEnumerable<object> values, WhereCondition whereCondition = WhereCondition.AND) where TEntity : CommonEntity
		{
			wrapperThis.ExtraIn(columFunc, values, whereCondition);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> NotIn<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> columFunc, Expression<Func<TEntity, IEnumerable<object>>> valueFunc, WhereCondition whereCondition = WhereCondition.AND) where TEntity : CommonEntity
		{
			IEnumerable<object> value = valueFunc.Compile().Invoke(wrapperThis.QueryOrEdit);
			wrapperThis.ExtraNotIn(columFunc, value, whereCondition);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> NotIn<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> columFunc, IEnumerable<object> values, WhereCondition whereCondition = WhereCondition.AND) where TEntity : CommonEntity
		{
			wrapperThis.ExtraNotIn(columFunc, values, whereCondition);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> GroupBy<TEntity>(this SqlWrapper<TEntity> wrapperThis, params Expression<Func<TEntity, object>>[] funcs) where TEntity : CommonEntity
		{
			SqlWrapper<TEntity> sqlWrapper = wrapperThis.ExtraGroupBy(funcs);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> Having<TEntity>(this SqlWrapper<TEntity> wrapperThis, string having) where TEntity : CommonEntity
		{
			wrapperThis.ExtraHaving(having);
			return wrapperThis;
		}

		public static SqlWrapper<TEntity> OrderBy<TEntity>(this SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, OrderCondition order = OrderCondition.Asc) where TEntity : CommonEntity
		{
			wrapperThis.OrderByDict[func] = $" t0.#[col] {order} ";
			return wrapperThis;
		}

		private static void SetPartWhereCondition<TEntity>(SqlWrapper<TEntity> wrapperThis, Expression<Func<TEntity, object>> func, WhereCondition whereCondition, string placeHolder, object vaulue = null, SqlSymbol sqlSymbol = SqlSymbol.None) where TEntity : CommonEntity
		{
			vaulue = vaulue ?? func.Compile().Invoke(wrapperThis.QueryOrEdit);
			bool useSymbol = !placeHolder.Contains(" LIKE ");
			if (vaulue is string value && !string.IsNullOrEmpty(value))
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, value, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is short num)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is int num2)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num2, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is long num3)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num3, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is float num4)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num4, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is double num5)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num5, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is decimal num6)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, num6, whereCondition, useSymbol, sqlSymbol);
			}
			else if (vaulue is DateTime dateTime)
			{
				wrapperThis.SetPartWhereSql(func, placeHolder, dateTime, whereCondition, useSymbol, sqlSymbol);
			}
		}

		public static string Build<TEntity>(this SqlWrapper<TEntity> wrapperThis, SqlStrategy sqlStrategy, SqlFunctionEnum sqlFunctionEnum = SqlFunctionEnum.None, string sqlFuncColumn = null) where TEntity : CommonEntity
		{
			return sqlStrategy switch
			{
				SqlStrategy.Select => BuildWithSelect(wrapperThis, sqlFunctionEnum, sqlFuncColumn), 
				SqlStrategy.Insert => BuildWithInsert(wrapperThis), 
				SqlStrategy.Update => BuildWithUpdate(wrapperThis), 
				SqlStrategy.Delete => BuildWithDelete(wrapperThis), 
				_ => null, 
			};
		}

		private static string BuildWithDelete<TEntity>(SqlWrapper<TEntity> sqlWrapper) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			StringBuilder stringBuilder = new StringBuilder("DELETE FROM " + text + " WHERE 1=1 ");
			ISet<string> whereColums = sqlWrapper.WhereColums;
			if (whereColums.Any())
			{
				stringBuilder.Append(string.Join(" ", (IEnumerable<string>)whereColums).Replace(sqlWrapper.MainTableAlias + ".", ""));
			}
			return stringBuilder.ToString();
		}

		private static string BuildWithUpdate<TEntity>(SqlWrapper<TEntity> sqlWrapper) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			StringBuilder stringBuilder = new StringBuilder("UPDATE " + text + " SET ");
			List<TableFieldAttribute> allTableFields = GetAllTableFields(typeFromHandle);
			Expression<Func<TEntity, object>>[] columns = sqlWrapper.Columns;
			bool flag = columns != null && columns.Length != 0;
			Dictionary<string, PropertyInfo> dictionary = ((IEnumerable<MemberInfo>)SQLiteSqlUtils.GetAllMembers(typeFromHandle)).Where((Func<MemberInfo, bool>)((MemberInfo p) => p is PropertyInfo)).ToDictionary<MemberInfo, string, PropertyInfo>((MemberInfo p) => p.Name.ToUpper(), (MemberInfo p) => (PropertyInfo)p);
			Dictionary<string, string> dictionary2 = ((IEnumerable<Expression<Func<TEntity, object>>>)columns).Select<Expression<Func<TEntity, object>>, string>((Func<Expression<Func<TEntity, object>>, string>)((Expression<Func<TEntity, object>> p) => sqlWrapper.GetLastExpression(p))).ToDictionary<string, string, string>((string p) => p, (string p) => (string)null);
			TEntity queryOrEdit = sqlWrapper.QueryOrEdit;
			foreach (TableFieldAttribute item in allTableFields)
			{
				if (item != null && item.IsExist && !item.PrimaryKey && (!flag || dictionary2.ContainsKey(item.Value)))
				{
					object value;
					stringBuilder.Append(" " + item.ColumName + "=" + (((value = dictionary[item.Value].GetValue(queryOrEdit)) == null) ? "NULL," : (handler[value.GetType()].Invoke(value) + ",")));
				}
			}
			stringBuilder = new StringBuilder(stringBuilder.ToString().TrimEnd(','));
			stringBuilder.Append("\n WHERE 1=1 ");
			ISet<string> whereColums = sqlWrapper.WhereColums;
			if (whereColums.Any())
			{
				stringBuilder.Append(string.Join(" ", (IEnumerable<string>)whereColums).Replace(sqlWrapper.MainTableAlias + ".", ""));
			}
			return stringBuilder.ToString();
		}

		private static string BuildWithSelect<TEntity>(SqlWrapper<TEntity> sqlWrapper, SqlFunctionEnum sqlFunctionEnum = SqlFunctionEnum.None, string sqlFuncColumn = null) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			typeFromHandle.GetCustomAttributes(inherit: false);
			string dbScheme = sqlWrapper.DbScheme;
			string text = ((customAttribute != null) ? customAttribute.Value : "");
			List<TableFieldAttribute> allTableFields = GetAllTableFields(typeFromHandle);
			StringBuilder stringBuilder = new StringBuilder("SELECT ");
			string mainTableAlias = sqlWrapper.MainTableAlias;
			string text2 = sqlFuncDict[sqlFunctionEnum];
			ISet<string> excludeColumns = sqlWrapper.ExcludeColumns;
			if (text2 != null)
			{
				text2 = text2.Replace("#[col]", sqlFuncColumn ?? "#[col]");
				stringBuilder.Append(text2);
			}
			if (sqlFunctionEnum != 0 && text2.Contains("#[col]"))
			{
				throw new ArgumentNullException("聚合函数列名为定义,请设置参数{sqlFuncColumn}");
			}
			Expression<Func<TEntity, object>>[] columns = sqlWrapper.Columns;
			bool flag = columns != null && columns.Length != 0;
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			foreach (TableFieldAttribute item2 in allTableFields)
			{
				if (item2 != null)
				{
					string item = item2.ColumName?.Replace("-", "").Replace("_", "").ToUpper();
					if (!flag && text2 == null && !excludeColumns.Contains(item))
					{
						stringBuilder.Append(mainTableAlias + "." + item2.ColumName + ",");
					}
					dictionary[item2.Value] = item2;
				}
			}
			if (flag && text2 == null)
			{
				for (int i = 0; i < columns.Length; i++)
				{
					string lastExpression = sqlWrapper.GetLastExpression(columns[i]);
					if (dictionary.TryGetValue(lastExpression, out var value) && !excludeColumns.Contains(lastExpression))
					{
						stringBuilder.Append(mainTableAlias + "." + value.ColumName + ",");
					}
				}
			}
			ISet<string> extraColumns = sqlWrapper.ExtraColumns;
			if (extraColumns.Any())
			{
				stringBuilder.Append(string.Join(",", (IEnumerable<string>)extraColumns) ?? "");
			}
			stringBuilder = new StringBuilder(stringBuilder.ToString().TrimEnd(','));
			stringBuilder.AppendLine(" FROM " + ((dbScheme != null) ? (dbScheme + "." + text) : text) + " " + mainTableAlias + " ");
			IList<string> joinSqlList = sqlWrapper.JoinSqlList;
			foreach (string item3 in joinSqlList)
			{
				stringBuilder.Append(" " + item3 + " \n ");
			}
			stringBuilder.Append("\n WHERE 1=1 ");
			TEntity queryOrEdit = sqlWrapper.QueryOrEdit;
			ISet<string> whereColums = sqlWrapper.WhereColums;
			if (whereColums.Any())
			{
				stringBuilder.Append(string.Join(" ", (IEnumerable<string>)whereColums));
			}
			ISet<string> extraGroupByColumns = sqlWrapper.ExtraGroupByColumns;
			if (extraGroupByColumns.Any())
			{
				stringBuilder.Append(" GROUP BY " + string.Join(",", (IEnumerable<string>)extraGroupByColumns) + " ");
			}
			stringBuilder = new StringBuilder(stringBuilder.ToString().TrimEnd(',') + sqlWrapper.havingFragment);
			Dictionary<Expression<Func<TEntity, object>>, string> orderByDict = sqlWrapper.OrderByDict;
			if (orderByDict.Any())
			{
				stringBuilder.Append(" ORDER BY ");
				bool flag2 = true;
				foreach (KeyValuePair<Expression<Func<TEntity, object>>, string> item4 in orderByDict)
				{
					string lastExpression = sqlWrapper.GetLastExpression(item4.Key);
					if (dictionary.ContainsKey(lastExpression))
					{
						if (!flag2)
						{
							stringBuilder.Append(",");
						}
						stringBuilder.Append(item4.Value.Replace("#[col]", dictionary[lastExpression].ColumName));
						flag2 = false;
					}
				}
			}
			ISet<string> extraOrderByColumns = sqlWrapper.ExtraOrderByColumns;
			if (extraOrderByColumns.Any())
			{
				stringBuilder.Append((orderByDict.Any() ? "" : " ORDER BY ") ?? "");
				stringBuilder.Append(string.Join(",", (IEnumerable<string>)extraOrderByColumns) + " ");
			}
			if (sqlWrapper.PageNumber > 0)
			{
				stringBuilder.Append($" limit {(sqlWrapper.PageNumber - 1) * sqlWrapper.PageSize},{sqlWrapper.PageSize} ");
			}
			return stringBuilder.ToString();
		}

		public static string BuildWithInsert<TEntity>(SqlWrapper<TEntity> sqlWrapper) where TEntity : CommonEntity
		{
			return SQLiteSqlUtils.CreateInsertSql(sqlWrapper.QueryOrEdit);
		}

		public static string CreateMigrationInsertSql<TEntity>(SqlWrapper<TEntity> sqlWrapper, string migrationDbAlias) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			string mainTableAlias = sqlWrapper.MainTableAlias;
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : typeFromHandle.Name);
			List<TableFieldAttribute> allTableFields = GetAllTableFields(typeFromHandle);
			StringBuilder stringBuilder = new StringBuilder("INSERT INTO " + (string.IsNullOrEmpty(migrationDbAlias) ? "" : (migrationDbAlias + ".")) + text + " (");
			Expression<Func<TEntity, object>>[] columns = sqlWrapper.Columns;
			bool flag = columns != null && columns.Length != 0;
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			foreach (TableFieldAttribute item in allTableFields)
			{
				if (item != null)
				{
					if (!flag)
					{
						stringBuilder.Append(item.ColumName + ",");
					}
					dictionary[item.Value] = item;
				}
			}
			if (flag)
			{
				for (int i = 0; i < columns.Length; i++)
				{
					string lastExpression = sqlWrapper.GetLastExpression(columns[i]);
					if (dictionary.TryGetValue(lastExpression, out var value))
					{
						stringBuilder.Append(value.ColumName + ",");
					}
				}
			}
			stringBuilder = new StringBuilder(stringBuilder.ToString().TrimEnd(','));
			stringBuilder.Append(')');
			return stringBuilder.ToString();
		}

		public static string BuildWithInsert<TEntity>(this SqlWrapper<TEntity> sqlWrapper, IEnumerable<TEntity> list, Expression<Func<TEntity, object>>[] ignoreColumns = null) where TEntity : CommonEntity
		{
			Type typeFromHandle = typeof(TEntity);
			string mainTableAlias = sqlWrapper.MainTableAlias;
			TableNameAttribute customAttribute = typeFromHandle.GetCustomAttribute<TableNameAttribute>();
			string text = ((customAttribute != null) ? customAttribute.Value : typeFromHandle.Name);
			List<TableFieldAttribute> allTableFields = GetAllTableFields(typeFromHandle);
			StringBuilder stringBuilder = new StringBuilder("INSERT INTO " + text + " (");
			StringBuilder stringBuilder2 = new StringBuilder();
			Expression<Func<TEntity, object>>[] columns = sqlWrapper.Columns;
			bool flag = columns != null && columns.Length != 0;
			Dictionary<string, Expression<Func<TEntity, object>>> ingorePropDict = new Dictionary<string, Expression<Func<TEntity, object>>>();
			Dictionary<string, Expression<Func<TEntity, object>>> includePropDict = new Dictionary<string, Expression<Func<TEntity, object>>>();
			if (ignoreColumns != null)
			{
				ingorePropDict = ignoreColumns.ToDictionaryByLocal<Expression<Func<TEntity, object>>, string, Expression<Func<TEntity, object>>>((Expression<Func<TEntity, object>> p) => sqlWrapper.GetLastExpression(p), (Expression<Func<TEntity, object>> p) => p);
			}
			if (columns != null)
			{
				includePropDict = columns.ToDictionaryByLocal<Expression<Func<TEntity, object>>, string, Expression<Func<TEntity, object>>>((Expression<Func<TEntity, object>> p) => sqlWrapper.GetLastExpression(p), (Expression<Func<TEntity, object>> p) => p);
			}
			Dictionary<string, PropertyInfo> dictionary = ((IEnumerable<PropertyInfo>)typeFromHandle.GetProperties(BindingFlags.Instance | BindingFlags.Public)).Where((Func<PropertyInfo, bool>)((PropertyInfo p) => ingorePropDict.Count() <= 0 || !ingorePropDict.ContainsKey(p.Name.ToUpper()))).Where((Func<PropertyInfo, bool>)((PropertyInfo p) => includePropDict.Count() <= 0 || includePropDict.ContainsKey(p.Name.ToUpper()))).ToDictionary<PropertyInfo, string, PropertyInfo>((PropertyInfo p) => p.Name.ToUpper(), (PropertyInfo p) => p);
			Dictionary<string, TableFieldAttribute> dictionary2 = ((IEnumerable<TableFieldAttribute>)allTableFields).Where((Func<TableFieldAttribute, bool>)((TableFieldAttribute p) => ingorePropDict.Count() <= 0 || !ingorePropDict.ContainsKey(p.Value))).Where((Func<TableFieldAttribute, bool>)((TableFieldAttribute p) => includePropDict.Count() <= 0 || includePropDict.ContainsKey(p.Value))).ToDictionary<TableFieldAttribute, string, TableFieldAttribute>((TableFieldAttribute v) => v.Value, (TableFieldAttribute v) => v);
			foreach (KeyValuePair<string, PropertyInfo> item in dictionary)
			{
				if (dictionary2.TryGetValue(item.Key, out var value) && value != null)
				{
					stringBuilder.Append(value.ColumName + ",");
				}
			}
			AddInsertValue(list, stringBuilder2, dictionary, dictionary2);
			stringBuilder = new StringBuilder(stringBuilder.ToString().TrimEnd(','));
			stringBuilder.Append(')');
			stringBuilder.Append("VALUES");
			stringBuilder.Append(stringBuilder2.ToString().TrimEnd(','));
			return stringBuilder.ToString();
		}

		private static void AddInsertValue<TEntity>(IEnumerable<TEntity> list, StringBuilder valueSbf, Dictionary<string, PropertyInfo> propDict, Dictionary<string, TableFieldAttribute> propAttrDict) where TEntity : CommonEntity
		{
			foreach (TEntity item in list)
			{
				StringBuilder stringBuilder = new StringBuilder();
				valueSbf.Append("(");
				foreach (KeyValuePair<string, PropertyInfo> item2 in propDict)
				{
					if (propAttrDict.TryGetValue(item2.Key, out var value))
					{
						object value2 = item2.Value.GetValue(item);
						string text = value?.PatternStr;
						if (value2 == null)
						{
							stringBuilder.Append("NULL");
						}
						if (value2 is string text2)
						{
							stringBuilder.Append("'" + text2 + "'");
						}
						else if (value2 is DateTime dateTime)
						{
							stringBuilder.Append("'" + dateTime.ToString(text ?? "yyyy-MM-dd") + "'");
						}
						else if (value2 is bool flag)
						{
							stringBuilder.Append($"{(flag ? 1 : 0)}");
						}
						else
						{
							stringBuilder.Append($"{value2}");
						}
						stringBuilder.Append(",");
					}
				}
				valueSbf.Append(stringBuilder.ToString().TrimEnd(',') + "),");
			}
		}

		private static List<TableFieldAttribute> GetAllTableFields(Type type)
		{
			if (type == (Type)null)
			{
				return new List<TableFieldAttribute>();
			}
			BindingFlags bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
			MemberInfo[] array = type.BaseType?.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] array2 = type.BaseType?.GetMembers(bindingAttr) ?? new MemberInfo[0];
			MemberInfo[] members = type.GetMembers(bindingAttr);
			Dictionary<string, TableFieldAttribute> dictionary = new Dictionary<string, TableFieldAttribute>();
			TableFieldAttribute tableFieldAttribute = null;
			MemberInfo[] array3 = array;
			foreach (MemberInfo memberInfo in array3)
			{
				tableFieldAttribute = memberInfo.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			MemberInfo[] array4 = array2;
			foreach (MemberInfo memberInfo2 in array4)
			{
				tableFieldAttribute = memberInfo2.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo2.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			MemberInfo[] array5 = members;
			foreach (MemberInfo memberInfo3 in array5)
			{
				tableFieldAttribute = memberInfo3.GetCustomAttribute<TableFieldAttribute>();
				tableFieldAttribute = ((tableFieldAttribute == null || !tableFieldAttribute.IsExist) ? null : tableFieldAttribute);
				if (tableFieldAttribute != null)
				{
					tableFieldAttribute.Value = memberInfo3.Name.ToUpper();
					dictionary[tableFieldAttribute.ColumName] = tableFieldAttribute;
				}
			}
			return ((IEnumerable<KeyValuePair<string, TableFieldAttribute>>)dictionary).Select<KeyValuePair<string, TableFieldAttribute>, TableFieldAttribute>((Func<KeyValuePair<string, TableFieldAttribute>, TableFieldAttribute>)((KeyValuePair<string, TableFieldAttribute> x) => x.Value)).ToList();
		}

		public static string BuildWithDataMigration<TEntity>(this SqlWrapper<TEntity> wrapperThis, string migrationDbAlias) where TEntity : CommonEntity
		{
			string dbScheme = wrapperThis.DbScheme;
			wrapperThis.DbScheme = migrationDbAlias;
			string value = CreateMigrationInsertSql(wrapperThis, migrationDbAlias);
			wrapperThis.DbScheme = dbScheme;
			string value2 = BuildWithSelect(wrapperThis);
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(value);
			stringBuilder.Append(" ");
			stringBuilder.Append(value2);
			stringBuilder.Append(";");
			return stringBuilder.ToString();
		}
	}
}
namespace MyCommon.compare
{
	public class BaseEntityEqualityComparer : IEqualityComparer<OsZbPurchaseProjectInfo>
	{
		public bool Equals(OsZbPurchaseProjectInfo x, OsZbPurchaseProjectInfo y)
		{
			return x?.MarkNo + x?.PackNo == y?.MarkNo + y?.PackNo;
		}

		public int GetHashCode(OsZbPurchaseProjectInfo obj)
		{
			return obj.GetHashCode();
		}
	}
}
namespace MyCommon.Comparers
{
	public class FileNameSort : IComparer
	{
		[DllImport("Shlwapi.dll", CharSet = CharSet.Unicode)]
		private static extern int StrCmpLogicalW(string param1, string param2);

		public int Compare(object name1, object name2)
		{
			if (name1 == null && name2 == null)
			{
				return 0;
			}
			if (name1 == null)
			{
				return -1;
			}
			if (name2 == null)
			{
				return 1;
			}
			return StrCmpLogicalW(name1.ToString(), name2.ToString());
		}
	}
}
namespace MyCommon.api
{
	public class BendApi
	{
		public static string API_PREFIX = ConfigHelper.GetValueByKey("dataApiPrefixUrl");

		public static string API_PREFIX2 = ConfigHelper.GetValueByKey("dataApiPrefixUrl2");

		public const string UP_LOAD_BID = "uploadBaseInfoToOSS";

		public const string CHECK_DOWN_LOAD_BID = "checkBaseInfoToOSS";

		public const string DOWN_LOAD_BID = "downloadBaseOnOSS";

		public const string ENC_UPLOAD_LIST_API = "listRecord";

		public const string CHECK_IS_UPLOAD_API = "checkIsUpload";

		public const string ENC_UPLOAD_API = "uploadFileToOSS";

		public const string ENC_WITHDRAW_API = "deleteFile";

		public const string ENC_DOWNLOAD_API = "downloadFileOnOSS";

		public const string SUCCESS_DOWNLOAD_API = "downloadStatusChange";

		public const string DOWNLOAD_BEFORE_CHECK = "downloadBeforeCheck";

		public const string RECORD_FILE_HASH = "networkDisk/save";

		public const string FILE_HASH_LIST = "networkDisk/list";

		public const string ON_GOING_PROJECT = "listBase";

		public const string FEED_BACK_LIST = "questioninfo/list";

		public const string FEED_BACK_EDIT = "questioninfo/saveOrUpdate";

		public const string FEED_BACK_DELETE = "questioninfo/delete";

		public const string ATTACH_LIST = "common/fileList";

		public const string POLICY_INFO = "policy/info";

		public const string POLICY_PDF = "/policy/pdf";

		public const string ATTACH_SHOW_DOWN = "common/showOssFile";

		public const string ATTACH_UP_LOAD = "common/uploadAttach";

		public const string ATTACH_DELETE = "common/deleteAttachs";

		public const string FRAGMENT_UPLOAD = "uploadFileToOSSWithBlock";

		public const string GET_SOFT_VERSION = "getLasterVerInfo";

		public const string SysUser_List = "sysuserinfo/list";

		public const string SysUser_Save = "sysuserinfo/save";

		public const string SysUser_Update = "sysuserinfo/update";

		public const string SysUser_Delete = "sysuserinfo/delete";

		public const string SysUser_Info = "sysuserinfo/selectOne";

		public const string GET_PROJECT_LIST = "baseProject/queryBaseProjectList";

		public const string UPDATE_PROJECT_INFO = "baseProject/updateByProjectNo";

		public const string UPLOAD_LIST_API = "queryPageRecordList";

		public const string PolicyBatchInfo = "policy/BatchInfo";

		public const string UpgradeFileUpload = "pack/uploadPack";

		public const string UpgradeFileDownLoad = "pack/downPack";

		public const string UpdateToolVersion = "pack/updateToolVersion";

		public const string SEND_MONEY_BANK_API = "bidAccountTransaction/getBidAccountList";

		public const string SEND_MONEY_BANK_IMAGE_API = "bidAccountTransaction/getBidAccountPdf";
	}
}
namespace MyCommon.adapter
{
	public class IgnorePropertiesResolver : DefaultContractResolver
	{
		private readonly HashSet<string> _propertiesToIgnore;

		public IgnorePropertiesResolver(IEnumerable<string> propertiesToIgnore)
		{
			_propertiesToIgnore = new HashSet<string>(propertiesToIgnore);
		}

		protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
		{
			JsonProperty jsonProperty = base.CreateProperty(member, memberSerialization);
			if (_propertiesToIgnore.Contains(jsonProperty.PropertyName))
			{
				jsonProperty.Ignored = true;
			}
			return jsonProperty;
		}
	}
	public abstract class SqlCreateAdapter
	{
		protected Dictionary<Type, string> DbTypeDict { get; set; }

		protected Dictionary<string, string> ColumnRemarkDict { get; set; }

		public SqlCreateAdapter()
		{
			DbTypeDict = new Dictionary<Type, string>
			{
				{
					typeof(string),
					"VARCHAR(255)"
				},
				{
					typeof(short),
					"INT(16)"
				},
				{
					typeof(short?),
					"INT(16)"
				},
				{
					typeof(float),
					"INT(16)"
				},
				{
					typeof(int),
					"BIGINT (32)"
				},
				{
					typeof(long),
					"BIGINT (64)"
				},
				{
					typeof(bool),
					"INT(2)"
				},
				{
					typeof(double),
					"DECIMAL (32,4)"
				},
				{
					typeof(decimal),
					"DECIMAL (32,4)"
				},
				{
					typeof(float?),
					"INT(16)"
				},
				{
					typeof(int?),
					"BIGINT (32)"
				},
				{
					typeof(long?),
					"BIGINT (18)"
				},
				{
					typeof(bool?),
					"INT(2)"
				},
				{
					typeof(double?),
					"DECIMAL (18,4)"
				},
				{
					typeof(decimal?),
					"DECIMAL (16,4)"
				},
				{
					typeof(DateTime),
					"DATETIME(0)"
				},
				{
					typeof(DateTime?),
					"DATETIME(0)"
				}
			};
			ColumnRemarkDict = new Dictionary<string, string>
			{
				{ "PROJECT_NO", "项目编号/批次编号" },
				{ "MARK_NO", "分标编号" },
				{ "PACK_NO", "包号" },
				{ "PACK_NAME", "包名称" },
				{ "SUB_PACK", "子分包" },
				{ "SUPPLIER_ID", "供应商ID" },
				{ "ENT_NAME", "企业名称" },
				{ "SUPPLIER_NAME", "供应商名称" },
				{ "CREATE_TIME", "创建时间" },
				{ "CREATE_USER", "创建人" },
				{ "UPDATE_TIME", "更新时间" },
				{ "UPDATE_USER", "更新人" }
			};
		}

		public virtual string CreateTableFragmentSql(string tableName)
		{
			return " CREATE TABLE " + tableName + "( ";
		}

		public abstract string WriteTableColumn(TableFieldAttribute tableFieldAttribute, Type memberType, bool isLast, ref string primaryKeyColumn);

		public abstract string WriteExtraInfo(TableNameAttribute table, string primaryKeyColumn);

		public abstract bool CreateDataBase(string sqlScript, string rootPathOrConnectionStr);

		public virtual string CreateCoreFragmentSql(string tableName, List<MemberInfo> lmemberList, ref string primaryKeyColumn)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append(CreateTableFragmentSql(tableName));
			Dictionary<MemberInfo, TableFieldAttribute> dictionary = new Dictionary<MemberInfo, TableFieldAttribute>();
			foreach (MemberInfo lmember in lmemberList)
			{
				TableFieldAttribute customAttribute = lmember.GetCustomAttribute<TableFieldAttribute>();
				VerifyAttribute customAttribute2 = lmember.GetCustomAttribute<VerifyAttribute>();
				ColumnInfoAttribute customAttribute3 = lmember.GetCustomAttribute<ColumnInfoAttribute>();
				if (customAttribute != null && customAttribute.IsExist)
				{
					customAttribute.Remark = ((!string.IsNullOrEmpty(customAttribute.Remark)) ? (customAttribute3?.Title ?? customAttribute.Remark) : customAttribute2?.Title);
					dictionary[lmember] = customAttribute;
				}
			}
			int num = 0;
			foreach (KeyValuePair<MemberInfo, TableFieldAttribute> item in dictionary)
			{
				Type memberType = ((item.Key is FieldInfo) ? ((FieldInfo)item.Key).FieldType : ((PropertyInfo)item.Key).PropertyType);
				string value = WriteTableColumn(item.Value, memberType, num == dictionary.Count - 1, ref primaryKeyColumn);
				if (!string.IsNullOrEmpty(value))
				{
					stringBuilder.AppendLine(value);
				}
				num++;
			}
			return stringBuilder.ToString()?.Trim().TrimEnd(',');
		}
	}
	public class SqliteSqlCreateAdapter : SqlCreateAdapter
	{
		public SqliteSqlCreateAdapter()
		{
			base.DbTypeDict[typeof(string)] = "TEXT";
		}

		public override bool CreateDataBase(string sqlScript, string rootPathOrConnectionStr)
		{
			string sqliteDbName = DateTime.Now.ToString("yyyyMMddHHmmss") + ".sqlite";
			try
			{
				SQLiteLibrary.ExcuteSql("", compressDb: false, rootPathOrConnectionStr, sqliteDbName);
			}
			catch (Exception)
			{
			}
			SQLiteLibrary.ExcuteSql(sqlScript, compressDb: false, rootPathOrConnectionStr, sqliteDbName);
			return true;
		}

		public override string WriteExtraInfo(TableNameAttribute table, string primaryKeyColumn)
		{
			return " ); ";
		}

		public override string WriteTableColumn(TableFieldAttribute tableField, Type memberType, bool isLast, ref string primaryKeyColumn)
		{
			string value;
			string text = (base.DbTypeDict.TryGetValue(memberType, out value) ? value : "TEXT");
			string text2 = tableField.ColumName + " " + text;
			if (tableField.PrimaryKey)
			{
				text2 += "  PRIMARY KEY  NOT NULL ";
				primaryKeyColumn = tableField.ColumName;
			}
			string value2 = tableField.Remark;
			if (string.IsNullOrWhiteSpace(value2))
			{
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName, out value2);
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName.ToLower(), out value2);
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName.ToUpper(), out value2);
			}
			return text2 + (isLast ? (" -- " + value2 + " ") : (" ,-- " + value2 + " "));
		}
	}
	public class MySqlSqlCreateAdapter : SqlCreateAdapter
	{
		public override bool CreateDataBase(string sqlScript, string rootPathOrConnectionStr)
		{
			Console.WriteLine(sqlScript);
			return false;
		}

		public override string WriteExtraInfo(TableNameAttribute table, string primaryKeyColumn)
		{
			string text = null;
			if (!string.IsNullOrEmpty(primaryKeyColumn))
			{
				text = text + ",\rPRIMARY KEY(`" + primaryKeyColumn + "`) USING BTREE \r\n";
			}
			text += ")";
			return text + " ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_unicode_ci ROW_FORMAT = DYNAMIC COMMENT = '" + table.Remark + "';";
		}

		public override string WriteTableColumn(TableFieldAttribute tableField, Type memberType, bool isLast, ref string primaryKeyColumn)
		{
			string value;
			string text = (base.DbTypeDict.TryGetValue(memberType, out value) ? value : "TEXT");
			string text2 = " " + tableField.ColumName + " " + text;
			if (tableField.PrimaryKey)
			{
				text2 += " NOT NULL ";
				primaryKeyColumn = tableField.ColumName;
			}
			string value2 = tableField.Remark;
			if (string.IsNullOrWhiteSpace(value2))
			{
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName, out value2);
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName.ToLower(), out value2);
				base.ColumnRemarkDict.TryGetValue(tableField.ColumName.ToUpper(), out value2);
			}
			return text2 + (isLast ? (" COMMENT '" + value2 + "' ") : (" COMMENT '" + value2 + "', "));
		}
	}
	public sealed class SqlCreateAdapterFactory
	{
		public static readonly ReadOnlyDictionary<string, SqlCreateAdapter> adapterDict = new ReadOnlyDictionary<string, SqlCreateAdapter>(new Dictionary<string, SqlCreateAdapter>
		{
			{
				"MYSQL",
				new MySqlSqlCreateAdapter()
			},
			{
				"SQLITE",
				new SqliteSqlCreateAdapter()
			}
		});

		public static SqlCreateAdapter Build(string type)
		{
			if (type == null)
			{
				throw new ArgumentNullException("type");
			}
			if (!adapterDict.ContainsKey(type.ToUpper()))
			{
				throw new NotImplementedException("未找到适配的SqlCreateAdapter");
			}
			return adapterDict[type.ToUpper()];
		}
	}
}
