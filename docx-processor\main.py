#!/usr/bin/env python3
"""
Word文档处理主程序

用于测试和运行文档处理功能
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Fix for UnicodeEncodeError on Windows
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from processor import DocumentProcessor
from utils import get_test_folder, find_docx_files, format_processing_report, validate_file_structure

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('document_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_single_folder():
    """
    测试单个文件夹的处理
    """
    # 目标目录
    base_dir = r"F:\2025\7月\国网安徽\国网安徽电力2025年非电网及办公用品物资框架协议公开招标采购_招标文件包\办公家具\包1_完整招标文件_7534782003872905\包1_技术规范书_7534780372181727"
    
    print("正在查找测试文件夹...")
    
    # 获取测试文件夹
    test_folder = get_test_folder(base_dir)
    if not test_folder:
        print("❌ 未找到包含'办公桌'文档的项目文件夹")
        return
    
    print(f"✅ 找到测试文件夹: {test_folder}")
    
    # 查找文件夹中的docx文件
    docx_files = find_docx_files(test_folder, "办公桌")
    if not docx_files:
        print("❌ 测试文件夹中未找到'办公桌'相关的docx文件")
        return
    
    print(f"✅ 找到 {len(docx_files)} 个待处理文件:")
    for file in docx_files:
        print(f"  - {os.path.basename(file)}")
    
    # 验证文件结构
    print("\n验证文件结构...")
    for file_path in docx_files:
        validation = validate_file_structure(file_path)
        print(f"文件: {os.path.basename(file_path)}")
        for key, value in validation.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")
    
    # 创建处理器并处理文件
    print("\n开始处理文件...")
    processor = DocumentProcessor(test_folder)
    
    # 处理第一个文件作为测试
    test_file = docx_files[0]
    print(f"\n正在处理测试文件: {os.path.basename(test_file)}")
    
    try:
        result = processor.process_single_file(test_file)
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
        else:
            print("✅ 处理成功!")
            print(f"  项目ID: {result['project_id']}")
            print(f"  主文档: {os.path.basename(result['main_doc'])}")
            print(f"  技术参数表: {os.path.basename(result['tech_params'])}")
            print(f"  组件配置表: {os.path.basename(result['component_config'])}")
            print(f"  映射项数: {result['mapping_count']}")
            
            # 检查输出文件是否存在
            print("\n检查输出文件:")
            for key, path in result.items():
                if key.endswith('_doc') or key.endswith('_params') or key.endswith('_config'):
                    exists = os.path.exists(path)
                    status = "✅" if exists else "❌"
                    print(f"  {status} {os.path.basename(path)}: {'存在' if exists else '不存在'}")
                    
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        logger.error(f"处理文件时发生错误: {str(e)}", exc_info=True)

def process_all_files():
    """
    处理所有文件
    """
    base_dir = r"F:\2025\7月\国网安徽\国网安徽电力2025年非电网及办公用品物资框架协议公开招标采购_招标文件包\办公家具\包1_完整招标文件_7534782003872905\包1_技术规范书_7534780372181727"
    
    print("开始批量处理所有文件...")
    
    processor = DocumentProcessor(base_dir)
    results = processor.process_all_files("办公桌")
    
    # 生成报告
    report = format_processing_report(results)
    print(report)
    
    # 保存报告到文件
    report_file = "processing_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n处理报告已保存到: {report_file}")

def main():
    """
    主程序入口
    """
    parser = argparse.ArgumentParser(description='Word文档处理工具')
    parser.add_argument(
        '--mode', 
        choices=['test', 'all'], 
        default='test',
        help='运行模式: test(测试单个文件夹) 或 all(处理所有文件)'
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Word文档处理工具")
    print("=" * 60)
    
    try:
        if args.mode == 'test':
            test_single_folder()
        else:
            process_all_files()
            
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        logger.error(f"程序运行出错: {str(e)}", exc_info=True)
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
