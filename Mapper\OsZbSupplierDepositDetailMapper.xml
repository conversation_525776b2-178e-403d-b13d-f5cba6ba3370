<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>
	
	<!--
       查询保证金详情
	-->
	<select id="selectDetailInfo">
		SELECT OS_ZB_SUPPLIER_DEPOSIT_DETAIL.*,
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NAME,
		'No' AS isAddRow,
		OS_ZB_PURCHASE_PROJECT_INFO.BZJ_WD,
		(select '已上传数('||count(SYS_FILE_INFO.ID)||')' from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID =OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID and SYS_FILE_INFO.RELATED_KEY='accountBaseInfoAttach')
		as accountBaseInfoAttach,
		(select '已上传数('||count(SYS_FILE_INFO.ID)||')' from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID =OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID and SYS_FILE_INFO.RELATED_KEY='purchaseAccountInfoAttach')
		as purchaseAccountInfoAttach,
		(select '已上传数('||count(SYS_FILE_INFO.ID)||')' from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID =OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID and SYS_FILE_INFO.RELATED_KEY='bidDetailsInfoAttach')
		as bidDetailsInfoAttach
		FROM OS_ZB_SUPPLIER_DEPOSIT_DETAIL
		LEFT JOIN
		OS_ZB_SUPPLIER_DEPOSIT ON OS_ZB_SUPPLIER_DEPOSIT.ID = OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID
		LEFT JOIN
		OS_ZB_PURCHASE_PROJECT_INFO ON OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO = OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO AND
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		WHERE OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = OS_ZB_SUPPLIER_DEPOSIT.ID 
		GROUP BY OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID
		ORDER BY OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO,CAST(replace(ifnull(OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NAME,'0'),'包','') as decimal) asc
	</select>


	<!--查询上传附件信息-->
	<select id="selectUploadFiles">
		select
		(case when mainTable.RELATED_KEY='bidDetailsInfoAttach' then '投标保证金明细表未上传'
		when mainTable.RELATED_KEY='accountBaseInfoAttach' then  '基本账户证明未上传' else  '汇款账户证明/保险购买账户证明未上传' end) as warn_info
		from
		(select 'bidDetailsInfoAttach' as RELATED_KEY
		union
		select 'accountBaseInfoAttach' as RELATED_KEY
		union
		select 'purchaseAccountInfoAttach' as RELATED_KEY) as mainTable
		where RELATED_KEY not in(
		select SYS_FILE_INFO.RELATED_KEY from  SYS_FILE_INFO
		where SYS_FILE_INFO.RELATED_KEY in('bidDetailsInfoAttach','accountBaseInfoAttach','purchaseAccountInfoAttach') and SYS_FILE_INFO.RELATED_ID ='{Id}'
		)
	</select>



	<!--查询上传附件信息 按标段上传时校验-->
	<select id="selectUploadFilesByMarkNo">
		SELECT * from (
		SELECT
		(case  when count(SYS_FILE_INFO.ID)&lt;=0 then '投标保证金明细表未上传' else  null end) as warn_info
		FROM SYS_FILE_INFO
		inner join OS_ZB_SUPPLIER_DEPOSIT_DETAIL on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = SYS_FILE_INFO.RELATED_ID
		inner join OS_ZB_SUPPLIER_DEPOSIT on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = OS_ZB_SUPPLIER_DEPOSIT.ID and OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO=(SELECT PROJECT_NO FROM OS_ZB_PURCHASE_PROJECT_INFO WHERE MARK_NO='{markNo}' LIMIT 0,1)
		WHERE  SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT' and SYS_FILE_INFO.RELATED_KEY ='bidDetailsInfoAttach'

		UNION

		SELECT
		(case  when count(SYS_FILE_INFO.ID)&lt;=0 then '基本账户证明未上传' else null end) as warn_info
		FROM SYS_FILE_INFO
		inner join OS_ZB_SUPPLIER_DEPOSIT_DETAIL on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = SYS_FILE_INFO.RELATED_ID
		inner join OS_ZB_SUPPLIER_DEPOSIT on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = OS_ZB_SUPPLIER_DEPOSIT.ID and OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO=(SELECT PROJECT_NO FROM OS_ZB_PURCHASE_PROJECT_INFO WHERE MARK_NO='{markNo}' LIMIT 0,1)
		WHERE  SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT' and SYS_FILE_INFO.RELATED_KEY ='accountBaseInfoAttach'

		UNION

		SELECT
		(case  when count(SYS_FILE_INFO.ID)&lt;=0 then '汇款账户证明/保险购买账户证明' else null end) as warn_info
		FROM SYS_FILE_INFO
		inner join OS_ZB_SUPPLIER_DEPOSIT_DETAIL on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = SYS_FILE_INFO.RELATED_ID
		inner join OS_ZB_SUPPLIER_DEPOSIT on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID = OS_ZB_SUPPLIER_DEPOSIT.ID and OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO=(SELECT PROJECT_NO FROM OS_ZB_PURCHASE_PROJECT_INFO WHERE MARK_NO='{markNo}' LIMIT 0,1)
		WHERE  SYS_FILE_INFO.RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT' and SYS_FILE_INFO.RELATED_KEY ='purchaseAccountInfoAttach'
		) as main where main.warn_info is not null and (select count(0) from OS_ZB_SUPPLIER_DEPOSIT_DETAIL where PAY_MODE!='无')&gt;=0
	</select>


	<!--
       查询保证金详情
	-->
	<select id="selectDetailByPackName">
		SELECT GROUP_CONCAT(DISTINCT ACCOUNT_BASE_INFO) ACCOUNT_BASE_INFO,group_concat(DISTINCT (CASE OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PAY_MODE WHEN '01' THEN '保险-年度保险' WHEN '02' THEN '保险-批次保险' WHEN '03' THEN '电汇'  WHEN '04' THEN '其他' WHEN '05' THEN '保函' ELSE '无' END) ) PAY_MODE,
		GROUP_CONCAT(DISTINCT PURCHASE_ACCOUNT_INFO) PURCHASE_ACCOUNT_INFO
		FROM OS_ZB_SUPPLIER_DEPOSIT_DETAIL
		WHERE MARK_NO = '{markNo}' <if sql=""/>
	</select>
	


	<!-- 保证金填充 按标段填写的进行填充-->
	<select id="insertFillDepositDetailByMark">
		insert into OS_ZB_SUPPLIER_DEPOSIT_DETAIL(
		ID,
		PARENT_ID,
		MARK_NO,
		PACK_NAME,
		DEPOSIT_MONEY,
		DEPOSIT_INSURE,
		TOTAL_MONEY,
		PAY_TIME,
		CREATE_TIME,
		UPDATE_TIME,
		LSHBDH,
		PAY_MODE,
		SUPPLIER_ID,
		ACCOUNT_BASE_INFO,
		PURCHASE_ACCOUNT_INFO,
		ISSUING_BANK,
		IS_FILL,
		ORIGIN,
		REMIT_ACCOUNT
		)
		---到标
		select * from
		( SELECT replace(time('now','localtime'),':','')||substr(ID,15)||'@random' AS ID,
		'@parentId' AS PARENT_ID,
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO,
		null as PACK_NAME,
		'0' AS DEPOSIT_MONEY,
		'0' AS DEPOSIT_INSURE,
		'@totalMoney' AS TOTAL_MONEY,
		'@payTime' as  PAY_TIME,
		datetime('now','localtime') as CREATE_TIME,
		datetime('now','localtime') as UPDATE_TIME,
		'@LSHBDH' as LSHBDH,
		'@payMode' as PAY_MODE,
		'@supplierId' as SUPPLIER_ID,
		'@accountBaseInfo' as ACCOUNT_BASE_INFO,
		'@purchaseAccountBaseInfo' as PURCHASE_ACCOUNT_INFO,
		'@issuingBank' as ISSUING_BANK,
		'@isFill' as IS_FILL,
		'@origin' as ORIGIN,
		'@remitAccount' as RemitAccount
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1'  and OS_ZB_PURCHASE_PROJECT_INFO.BZJ_WD = 'MARK'
		and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO in (@markNos)
		group by MARK_NO
		order by MARK_NO
		)

	</select>



	<!-- 保证金填充 按分包填写的进行填充-->
	<select id="insertFillDepositDetailByPack">

		insert into OS_ZB_SUPPLIER_DEPOSIT_DETAIL(
		ID,
		PARENT_ID,
		MARK_NO,
		PACK_NAME,
		DEPOSIT_MONEY,
		DEPOSIT_INSURE,
		TOTAL_MONEY,
		PAY_TIME,
		CREATE_TIME,
		UPDATE_TIME,
		LSHBDH,
		PAY_MODE,
		SUPPLIER_ID,
		ACCOUNT_BASE_INFO,
		PURCHASE_ACCOUNT_INFO,
		ISSUING_BANK,
		IS_FILL,
		ORIGIN,
		REMIT_ACCOUNT
		)

		---到包
		select * from
		( SELECT replace(time('now','localtime'),':','')||substr(ID,15)||'@random'  AS ID,
		'@parentId' AS PARENT_ID,
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO,
		OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME,
		'0' AS DEPOSIT_MONEY,
		'0' AS DEPOSIT_INSURE,
		'@totalMoney' AS TOTAL_MONEY,
		'@payTime' as  PAY_TIME,
		datetime('now','localtime') as CREATE_TIME,
		datetime('now','localtime') as UPDATE_TIME,
		'@LSHBDH' as LSHBDH,
		'@payMode' as PAY_MODE,
		'@supplierId' as SUPPLIER_ID,
		'@accountBaseInfo' as ACCOUNT_BASE_INFO,
		'@purchaseAccountBaseInfo' as PURCHASE_ACCOUNT_INFO,
		'@issuingBank' as ISSUING_BANK,
		'@isFill' as IS_FILL,
		'@origin' as ORIGIN,
		'@remitAccount' as RemitAccount
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1'  and OS_ZB_PURCHASE_PROJECT_INFO.BZJ_WD = 'PACK'
		and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO in (@markNos)
		group by MARK_NO||PACK_NAME
		order by MARK_NO,CAST(replace(PACK_NAME,'包','') AS SIGNED integer)
		)

	</select>




</mapper>