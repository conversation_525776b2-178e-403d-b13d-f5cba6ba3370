<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--商务偏差和技术偏差表 上传的校验-->
	<select id="checkBtOffset">
		SELECT count(0) as count
		FROM MODEL_EXTRA_INFO
		left join sys_file_info on sys_file_info.RELATED_PAGE =MODEL_EXTRA_INFO.TABLE_NAME and sys_file_info.RELATED_KEY =MODEL_EXTRA_INFO.MODEL_CODE and
		sys_file_info.RELATED_ID =MODEL_EXTRA_INFO.UID
		WHERE TABLE_NAME = 'OFFSET_TABLE' AND
		MODEL_CODE = '@modelCode' AND  RESULT_1='有偏差' AND
		UID like '@Uid' and sys_file_info.ID is null
	</select>

	<!--查询委托授权书信息 附件关联信息-->
	<select id="selectAuthPersonFile">
		SELECT sys_file_info.*
		FROM OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join sys_file_info on sys_file_info.ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		where OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO='{markNo}'
		<nonEmpty_Con></nonEmpty_Con>
		group by sys_file_info.ID
	</select>


	<!--技术模块查询-->
	<select id="selectTechModel">
		--投标授权人信息(因为他是跟商务文件关联的)		
		SELECT * FROM 
		(select OS_ZB_SUPPLIER_AUTH_PERSON_INFO.*
		from OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join OS_ZB_SUPPLIER_AUTH_PERSON_INFO on OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		WHERE OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUPPLIER_ID = '{supplierId}' AND
		OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO = '{markNo}' AND
		(LENGTH(OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME) &lt;= 0	OR OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME is null)
		UNION
		select OS_ZB_SUPPLIER_AUTH_PERSON_INFO.*
		from OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join OS_ZB_SUPPLIER_AUTH_PERSON_INFO on OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		WHERE OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUPPLIER_ID = '{supplierId}' AND OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME = '{packName}');

		--供应商营业执照
		select OS_ZB_SUPPLIER_INFO.* from OS_ZB_SUPPLIER_INFO;

		---投标人概况表
		select OS_ZB_SUPPLIER_CREDIT_INFO.* from OS_ZB_SUPPLIER_CREDIT_INFO
		inner join OS_ZB_SUPPLIER_BIAO_CREDIT_INFO on OS_ZB_SUPPLIER_CREDIT_INFO.ID = OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.RELATE_ID
		where OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUPPLIER_ID ='{supplierId}' and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.TYPE ='jishu'
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME='{packName}')
		-- and (OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK='{subPack}') --暂时不到分包
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.ATTACH_TYPE in('bidder_profile');

		--项目管理机构组成表
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		where RELATED_KEY = 'pm_org_file' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}&amp;%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK;

		--主要人员简历表及证明文件
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		where RELATED_KEY = 'core_resume_file' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}&amp;%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK;

		--工作大纲、工作方案及服务承诺
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		where RELATED_KEY = 'work_line_file' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}&amp;%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK;

		--勘察设计负责人（项目经理/设总）及主要设计人员情况表
		select OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.*,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE as extraField1
		,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		,sys_file2.ID as _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1
		,sys_file3.ID as _file_id2,
		sys_file3.FILE_FORMAT as _file_format2,
		sys_file3.FILE_DISP_NAME as _file_disp_name2,
		sys_file3.FILE_PATH as _file_path2
		,sys_file4.ID as _file_id3,
		sys_file4.FILE_FORMAT as _file_format3,
		sys_file4.FILE_DISP_NAME as _file_disp_name3,
		sys_file4.FILE_PATH as _file_path3
		from OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.SUB_PACK
		--主要设计人员表及相关证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.ID and sys_file1.RELATED_KEY='core_peopel_info'
		--拟投入设备软件情况表
		left join SYS_FILE_INFO sys_file2 on sys_file2.RELATED_ID = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.ID and sys_file2.RELATED_KEY='software_info'
		--勘察设计主要人员承诺函
		left join SYS_FILE_INFO sys_file3 on sys_file3.RELATED_ID = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.ID and sys_file3.RELATED_KEY='core_peopel_commit'
		--勘察设计负责人（项目经理/设总）情况表
		left join SYS_FILE_INFO sys_file4 on sys_file4.RELATED_ID = OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.ID and sys_file4.RELATED_KEY='designer_info'
		where OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.SUB_PACK,OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL.CREATE_TIME;

		--拟分包项目情况表
		select OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.*,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE as extraField1
		,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		,sys_file2.ID as _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1
		from OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.SUB_PACK
		--拟分包项目情况表
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.ID and sys_file1.RELATED_KEY='propose_subpack'
		--施工项目管理关键人员承诺函
		left join SYS_FILE_INFO sys_file2 on sys_file2.RELATED_ID = OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.ID and sys_file2.RELATED_KEY='worker_commit'
		where OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.SUB_PACK,OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK.CREATE_TIME;

		--拟派总监及主要监理人员情况表
		select OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.*,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE as extraField1
		,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		,sys_file2.ID as _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1
		from OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.SUB_PACK
		--监理关键人员承诺函
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.ID and sys_file1.RELATED_KEY='propose_chiefStaffer'
		--上传拟派总监及主要监理人员情况表
		left join SYS_FILE_INFO sys_file2 on sys_file2.RELATED_ID = OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.ID and sys_file2.RELATED_KEY='core_listener_commit'
		where OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.SUB_PACK,OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER.CREATE_TIME;

		--安全、质量、进度等保障措施及方案
		select OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.*,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE as extraField1
		,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.SUB_PACK
		--安全、质量、进度等保障措施及方案的上传
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.ID and sys_file1.RELATED_KEY='safe_to_progress'
		where OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.SUB_PACK,OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS.CREATE_TIME asc;

		--工期响应情况表
		select OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.*,OS_ZB_PURCHASE_PROJECT_INFO.MARK_NAME as extraField1
		from OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.SUB_PACK
		where OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.SUB_PACK,OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE.CREATE_TIME;

		--评标办法中涉及的其他相关内容
		select OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.*,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE as extraField1,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS
		inner join OS_ZB_PURCHASE_PROJECT_INFO on OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.MARK_NO
		and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME and OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK = OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK
		---支撑材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.ID and sys_file1.RELATED_KEY='otherAttach'
		where OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUPPLIER_ID ='{supplierId}' and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.TYPE='jishu'
		and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME='{packName}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK,OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.CREATE_TIME;

		--商务偏差表
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		where RELATED_KEY = 'tech_offset' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}&amp;%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK;


		--企业类似项目业绩和实施经验（动态表格01）
		select OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.TYPE='jishu' and  OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK='{subPack}') and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;

		--企业类似项目业绩和实施经验（动态表格02）
		select OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.TYPE='jishu' and  OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK='{subPack}')  and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;
	</select>


	<!--商务模块查询-->
	<select id="selectBusinessModel">

		--投标授权人信息
		select OS_ZB_SUPPLIER_AUTH_PERSON_INFO.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path,
		sys_file2.ID as _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1
		from OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join OS_ZB_SUPPLIER_AUTH_PERSON_INFO on OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		inner join SYS_FILE_INFO as sys_file1  on sys_file1.ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		left join SYS_FILE_INFO as sys_file2  on sys_file2.ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.ATTACH_ID
		where
		OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO = '{markNo}'  and (OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME is null or OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME='{packName}')
		and (OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUB_PACK='{subPack}');
		--GROUP BY sys_file1.ID; --目前是一个附件关联多个附件

		--联合体协议书
		SELECT OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID,PROJECT_NO, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.MARK_NO, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK,
		OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUPPLIER_ID,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.IS_COMPLEX_BID,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.IS_COMPLEX_BID, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.RELATE_ID, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.TYPE,
		sys_file1.ID AS _file_id,
		sys_file1.FILE_FORMAT AS _file_format,
		sys_file1.FILE_DISP_NAME AS _file_disp_name,
		sys_file1.FILE_PATH AS _file_path
		from OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT
		left join sys_file_info sys_file1 on sys_file1.RELATED_KEY='complex_fj' and OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID=sys_file1.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME is null
		or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--供应商营业执照
		-- 企业法人营业执照 +有效的税务登记证明 + 组织结构
		SELECT OS_ZB_SUPPLIER_INFO.*,
		file_t1.ID AS _file_id,
		file_t1.FILE_FORMAT AS _file_format,
		file_t1.FILE_DISP_NAME AS _file_disp_name,
		file_t1.FILE_PATH AS _file_path,
		file_t2.ID AS _file_id1,
		file_t2.FILE_FORMAT AS _file_format1,
		file_t2.FILE_DISP_NAME AS _file_disp_name1,
		file_t2.FILE_PATH AS _file_path1,
		file_t3.ID AS _file_id2,
		file_t3.FILE_FORMAT AS _file_format2,
		file_t3.FILE_DISP_NAME AS _file_disp_name2,
		file_t3.FILE_PATH AS _file_path2,
		file_t4.ID AS _file_id3,
		file_t4.FILE_FORMAT AS _file_format3,
		file_t4.FILE_DISP_NAME AS _file_disp_name3,
		file_t4.FILE_PATH AS _file_path3
		FROM OS_ZB_SUPPLIER_INFO
		LEFT JOIN
		--szhyyyzz 营业执照
		sys_file_info file_t1 ON file_t1.RELATED_KEY='szhyyyzz' and OS_ZB_SUPPLIER_INFO.ID=file_t1.RELATED_ID
		--orgjg  组织结构
		LEFT JOIN sys_file_info file_t2 ON file_t2.RELATED_KEY='orgjg' and OS_ZB_SUPPLIER_INFO.ID=file_t2.RELATED_ID
		--yxswdj 有效税务登记证明
		LEFT JOIN sys_file_info file_t3 ON file_t3.RELATED_KEY='yxswdj' and OS_ZB_SUPPLIER_INFO.ID=file_t3.RELATED_ID
		--投标授权人法人身份证附件
		left join SYS_FILE_INFO file_t4 on  file_t4.RELATED_KEY='legalPersonFj' and file_t4.RELATED_ID=OS_ZB_SUPPLIER_INFO.ID
		WHERE OS_ZB_SUPPLIER_INFO.ID ='{supplierId}'  order by file_t1.SORT_VALUE,file_t2.SORT_VALUE,file_t3.SORT_VALUE;


		--企业资质等级证书原件影印件等，ISO 9000、ISO 14000、OHSAS18000等三标认证证书原件影印件（如有）
		select OS_ZB_SUPPLIER_QUALIFY_CERT.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_QUALIFY_CERT
		inner join OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT on  OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.RELATE_ID = OS_ZB_SUPPLIER_QUALIFY_CERT.ID
		--资质证书影印件
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_QUALIFY_CERT.ID and sys_file1.RELATED_KEY='certFj'
		where OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--企业类似项目业绩和实施经验（动态表格01）
		select OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.TYPE='shangwu' and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK='{subPack}') and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;

		--企业类似项目业绩和实施经验（动态表格02）
		select OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.TYPE='shangwu' and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK='{subPack}')  and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;

		-- 年度财务报告
		SELECT OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_FINANCE_INFO
		LEFT JOIN
		sys_file_info ON  sys_file_info.RELATED_KEY = 'cwsjbg' and
		OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}' or PACK_NAME is null)
		ORDER BY OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.CREATE_TIME DESC,sys_file_info.UPLOAD_TIME desc;

		---投标人概况表+履约能力说明资料+企业信誉证明文件+合同履约证明文件及类似项目获奖情况证明文件+“重法纪、讲诚信、提质量”承诺函+信用信息【国家企业信用信息公示系统查询结果PDF报告的打印版】
		select OS_ZB_SUPPLIER_CREDIT_INFO.* from OS_ZB_SUPPLIER_CREDIT_INFO
		inner join OS_ZB_SUPPLIER_BIAO_CREDIT_INFO on OS_ZB_SUPPLIER_CREDIT_INFO.ID = OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.RELATE_ID
		where OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUPPLIER_ID ='{supplierId}' and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.TYPE ='shangwu'
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK='{subPack}')
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.ATTACH_TYPE in({attachTypes});
		--and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.ATTACH_TYPE in('bidder_profile','done_capacity_files','company_credit_files','contract_award_files','tag_promise_files','country_public_credit_info');

		--评标办法中涉及的其他相关内容
		select OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS
		---支撑材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.ID and sys_file1.RELATED_KEY='otherAttach'
		where OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUPPLIER_ID ='{supplierId}'  and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.TYPE='shangwu'
		and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--商务偏差表
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;&amp;&amp;'
		where RELATED_KEY = 'business_offset' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO;
	</select>

	<!--商务模块查询 废弃（2023-10-31）-->
	<select id="selectBusinessModel_old">

		--投标授权人信息
		select OS_ZB_SUPPLIER_AUTH_PERSON_INFO.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path,
		sys_file2.ID as _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1
		from OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join OS_ZB_SUPPLIER_AUTH_PERSON_INFO on OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		inner join SYS_FILE_INFO as sys_file1  on sys_file1.ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		left join SYS_FILE_INFO as sys_file2  on sys_file2.ID = OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.ATTACH_ID
		where
		OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO = '{markNo}'  and (OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME is null or OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME='{packName}')
		and (OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.SUB_PACK='{subPack}');
		--GROUP BY sys_file1.ID; --目前是一个附件关联多个附件

		--联合体协议书
		SELECT OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID,PROJECT_NO, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.MARK_NO, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK,
		OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUPPLIER_ID,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.IS_COMPLEX_BID,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.IS_COMPLEX_BID, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.RELATE_ID, OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.TYPE,
		sys_file1.ID AS _file_id,
		sys_file1.FILE_FORMAT AS _file_format,
		sys_file1.FILE_DISP_NAME AS _file_disp_name,
		sys_file1.FILE_PATH AS _file_path
		from OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT
		left join sys_file_info sys_file1 on sys_file1.RELATED_KEY='complex_fj' and OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID=sys_file1.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME is null
		or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--供应商营业执照+网上年报（包含企业年度报告公示发布日期和企业基本信息）
		select OS_ZB_SUPPLIER_INFO.*,sys_file1.ID as  _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path,
		sys_file2.ID as  _file_id1,
		sys_file2.FILE_FORMAT as _file_format1,
		sys_file2.FILE_DISP_NAME as _file_disp_name1,
		sys_file2.FILE_PATH as _file_path1,
		sys_file3.ID as  _file_id2,
		sys_file3.FILE_FORMAT as _file_format2,
		sys_file3.FILE_DISP_NAME as _file_disp_name2,
		sys_file3.FILE_PATH as _file_path2
		from OS_ZB_SUPPLIER_INFO
		left join SYS_FILE_INFO sys_file1 on  OS_ZB_SUPPLIER_INFO.SZHYZZ_ATTACHID = sys_file1.ID
		left join SYS_FILE_INFO sys_file2 on  OS_ZB_SUPPLIER_INFO.ID = sys_file2.RELATED_ID and sys_file2.RELATED_KEY ='year_report'
		left join SYS_FILE_INFO sys_file3 on  sys_file3.RELATED_KEY='legalPersonFj' and sys_file3.RELATED_ID=OS_ZB_SUPPLIER_INFO.ID
		ORDER BY sys_file2.FILE_DISP_NAME ;


		--企业资质等级证书原件影印件等，ISO 9000、ISO 14000、OHSAS18000等三标认证证书原件影印件（如有）
		select OS_ZB_SUPPLIER_QUALIFY_CERT.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_QUALIFY_CERT
		inner join OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT on  OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.RELATE_ID = OS_ZB_SUPPLIER_QUALIFY_CERT.ID
		--资质证书影印件
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_QUALIFY_CERT.ID and sys_file1.RELATED_KEY='certFj'
		where OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--企业类似项目业绩和实施经验（动态表格01）
		select OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.SUB_PACK='{subPack}') and OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;

		--企业类似项目业绩和实施经验（动态表格02）
		select OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE
		---证明材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.ID and sys_file1.RELATED_KEY='proveFj'
		where OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUPPLIER_ID ='{supplierId}'
		and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.SUB_PACK='{subPack}')  and OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.MODEL_FLAG in('finish_list','designing_list')
		ORDER BY OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE.CREATE_TIME asc,sys_file1.UPLOAD_TIME desc;

		-- 年度财务报告
		SELECT OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_FINANCE_INFO
		LEFT JOIN
		sys_file_info ON  sys_file_info.RELATED_KEY = 'cwsjbg' and
		OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}' or PACK_NAME is null)
		ORDER BY OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.CREATE_TIME DESC,sys_file_info.UPLOAD_TIME desc;

		---投标人概况表+履约能力说明资料+企业信誉证明文件+合同履约证明文件及类似项目获奖情况证明文件+“重法纪、讲诚信、提质量”承诺函+信用信息【国家企业信用信息公示系统查询结果PDF报告的打印版】
		select OS_ZB_SUPPLIER_CREDIT_INFO.* from OS_ZB_SUPPLIER_CREDIT_INFO
		inner join OS_ZB_SUPPLIER_BIAO_CREDIT_INFO on OS_ZB_SUPPLIER_CREDIT_INFO.ID = OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.RELATE_ID
		where OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUPPLIER_ID ='{supplierId}' and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.TYPE ='shangwu'
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUB_PACK='{subPack}')
		and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.ATTACH_TYPE in('bidder_profile','done_capacity_files','company_credit_files','contract_award_files','tag_promise_files','country_public_credit_info');

		--评标办法中涉及的其他相关内容
		select OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.*,sys_file1.ID as _file_id,
		sys_file1.FILE_FORMAT as _file_format,
		sys_file1.FILE_DISP_NAME as _file_disp_name,
		sys_file1.FILE_PATH as _file_path
		from OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS
		---支撑材料
		left join SYS_FILE_INFO sys_file1 on sys_file1.RELATED_ID =OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.ID and sys_file1.RELATED_KEY='otherAttach'
		where OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUPPLIER_ID ='{supplierId}'  and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.TYPE='shangwu'
		and OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.MARK_NO = '{markNo}'  and (LENGTH(OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME) &lt;=0 or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME='{packName}' or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.PACK_NAME is null)
		and (OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK is null or OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.SUB_PACK='{subPack}')
		ORDER BY OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS.CREATE_TIME DESC,sys_file1.UPLOAD_TIME desc;

		--商务偏差表
		select SYS_FILE_INFO.ID,SYS_FILE_INFO.RELATED_PAGE,SYS_FILE_INFO.RELATED_KEY,SYS_FILE_INFO.FILE_PATH,SYS_FILE_INFO.FILE_FORMAT,SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.RELATED_ID,OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_TITLE
		FROM SYS_FILE_INFO
		inner join OS_ZB_PURCHASE_PROJECT_INFO on
		SYS_FILE_INFO.RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;&amp;&amp;'
		where RELATED_KEY = 'business_offset' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}%' group by RELATED_ID
		order by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO;
	</select>


	<!--校验相关文件上传信息-->
	<select  id="checkFileExport">

		--商务支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务支持文件未导入') as WARN_INO  from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null

		union
		--技术支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null


		union

		---到标的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null


		union

		---到标的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null

		union

		---到标的技术报价文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 报价文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术报价文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 报价文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY like '%PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术价格投标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 价格投标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格投标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 价格投标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union
		---到标的上架商品
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  || ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;%' AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND '{goodsImpBtn}'='YES'

		union
		---到包的上架商品
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || ',' ||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME|| ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;' || OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'PACK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND  '{goodsImpBtn}'='YES'

		union
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  || ' 技术支持文件缺少生产成本参数特性表') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS
		on OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PROJECT_NO = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO and OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.MARK_NO
		=OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO --and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY='MARK'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS='是' and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.ID is null

		union
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME || ' 技术支持文件缺少生产成本参数特性表') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS
		on OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PROJECT_NO = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO and OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.MARK_NO
		=OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME =OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PACK_NAME
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS='是' and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY like '%PACK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS in ('YES','是') AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.ID is null

		union
		----（关于响应信息）
		--技术响应信息到标
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术响应信息未填写') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_STECH_RESPONSE_INFO  on OS_ZB_STECH_RESPONSE_INFO.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_STECH_RESPONSE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标' and OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE in ('YES','是')
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		union
		--技术响应信息到分包或子包(针对公开招标)
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' ,包号: '||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' ,分包: '||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK||' 技术响应信息未填写') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_STECH_RESPONSE_INFO  on OS_ZB_STECH_RESPONSE_INFO.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_STECH_RESPONSE_INFO.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		and OS_ZB_STECH_RESPONSE_INFO.SUB_PACK =OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY in ('PACK','SUB_PACK')
		and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and IS_SERVICE_FRAME !='是'
		and OS_ZB_PURCHASE_PROJECT_INFO.SELECTED='1'  and OS_ZB_STECH_RESPONSE_INFO.ID is null  and OS_ZB_STECH_RESPONSE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标' and OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE in ('YES','是')
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK
		union
		--技术响应信息到分包(针对服务框架)
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' ,包号: '||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术响应信息未填写') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_STECH_RESPONSE_INFO  on OS_ZB_STECH_RESPONSE_INFO.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_STECH_RESPONSE_INFO.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK'
		and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and IS_SERVICE_FRAME =='是'
		and OS_ZB_PURCHASE_PROJECT_INFO.SELECTED='1'  and OS_ZB_STECH_RESPONSE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标' and OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE in ('YES','是')
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME

		union
		--商务响应信息到标
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务响应信息未填写') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SBUSINESS_RESPONSE_INFO  on OS_ZB_SBUSINESS_RESPONSE_INFO.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SBUSINESS_RESPONSE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标' and OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE in ('YES','是')
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		union
		--商务响应信息到包 （商务应该不会到子包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||',包号:'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务响应信息未填写') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SBUSINESS_RESPONSE_INFO  on OS_ZB_SBUSINESS_RESPONSE_INFO.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SBUSINESS_RESPONSE_INFO.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SBUSINESS_RESPONSE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标' and OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE in ('YES','是')
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME
	</select>



	<!--
		附件信息+  保证金主表信息
	-->
	<select id="insertAttachInfoTemp">
		--项目信息
		insert into db1.OS_ZB_PURCHASE_PROJECT_INFO SELECT * FROM OS_ZB_PURCHASE_PROJECT_INFO WHERE MARK_NO ='{markNo}' and SELECTED='1';
		--更新项目信息
		update db1.OS_ZB_PURCHASE_PROJECT_INFO SET SUPPLIER_ID ='{supplierId}';
		--保证金
		update db1.OS_ZB_SUPPLIER_DEPOSIT SET SUPPLIER_ID ='{supplierId}';

		--更新供应商id信息
		--附件信息
		insert into db1.SYS_FILE_INFO
		--pdf文件/word文件(商务+技术的支持文件)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		INNER JOIN
		OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.ID = SYS_FILE_INFO.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BID_ATTACHMENT.PROJECT_NO = '{projectNo}' AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = '{markNo}'
		union
		--ecp压缩文件+价格压缩文件+上架商品压缩包
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;%'
		union
		--保证金明细附件(按项目)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT_ATTACH' and RELATED_ID like '{projectNo}'
		--union
		--授权人信息
		--select SYS_FILE_INFO.*
		--FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO inner JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID
		union
		--保证金附件部分
		select  SYS_FILE_INFO.*  from SYS_FILE_INFO
		inner join OS_ZB_SUPPLIER_DEPOSIT_DETAIL on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID =SYS_FILE_INFO.RELATED_ID
		where SYS_FILE_INFO.RELATED_KEY in('accountBaseInfoAttach','purchaseAccountInfoAttach','bidDetailsInfoAttach') and OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO='{markNo}';
	</select>




	<!--获取签章模块导入的信息-->
	<select  id="getUploadFiles">
		--商务支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		--技术支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--技术支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		---到标的商务压缩包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union
		---到标的技术压缩包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的技术压缩包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union
		---到标的技术报价文件包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\报价\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的技术报价文件包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\报价\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union
		---到标的技术价格投标文件包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的技术价格投标文件包
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union
		---到标的技术响应情况一览表
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的技术响应情况一览表
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union
		---到标的上架商品
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID LIKE '%'||OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%'  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union
		---到包的上架商品
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		---所有的的保证金附件
		union
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金\' as ROOT,SYS_FILE_INFO.BINARY_FILE from SYS_FILE_INFO
		where SYS_FILE_INFO.RELATED_KEY in('bidDetailsInfoAttach','accountBaseInfoAttach','purchaseAccountInfoAttach')
		
		union
		---资格预审
		SELECT SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\{packName}\资格预审\' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}'
		AND RELATED_KEY ='ZgysZip';
	</select>


</mapper>