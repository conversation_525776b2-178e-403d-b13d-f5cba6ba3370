﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>


	<!--
		查询列表信息
	-->
	<select id="selectList">
		SELECT OS_ZB_SUPPLIER_QUALIFY_CERT.*,
		'已上传数(' || count(fj_1.id) || ')' AS certFj
		FROM OS_ZB_SUPPLIER_QUALIFY_CERT
		left JOIN
		sys_file_info AS fj_1 ON fj_1.RELATED_ID = OS_ZB_SUPPLIER_QUALIFY_CERT.ID AND fj_1.RELATED_KEY = 'certFj'
		group by OS_ZB_SUPPLIER_QUALIFY_CERT.ID
	</select>


</mapper>
