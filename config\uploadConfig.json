[
    {
        "remark": "财务信息/状况",
        "modelName": "OS_ZB_SUPPLIER_FINANCE_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10, // 个数
                "imgCount": 10,
                "imgSize": 0.5, // M(兆)计算
                "fileKeys": [ "cwsjbg" ] //财务报告-证明文件
            }
        ]
    },
    {
        "remark": "销售业绩",
        "modelName": "OS_ZB_SUPPLIER_SALES_INFO",
        "uploads": [
            {
                "pdfCount": 1,
                "imgCount": 10,
                "pdfSize": 15,
                "imgSize": 0.5,
                "fileKeys": [] //所有附件
            }
        ]
    },
    {
        "remark": "试验报告",
        "modelName": "OS_ZB_SUPPLIER_REPORT_INFO",
        "uploads": [
            {
                "pdfCount": 1,
                "imgCount": 10,
                "pdfSize": 40,
                "imgSize": 0.5,
                "fileKeys": [] //所有附件
            }
        ]
    },
    {
        "remark": "产品信息",
        "modelName": "OS_ZB_SUPPLIER_PRODUCT_INFO",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": [ "agentFj" ] //产品信息（集成证明文件）
            },
            {
                "imgCount": 7,
                "imgSize": 0.5,
                "fileKeys": [ "cpwg", "main_image", "other_image" ] //产品信息（产品外观）、主图、副图
            }
        ]
    },
    {
        "remark": "产品部件/组部件",
        "modelName": "OS_ZB_SUPPLIER_PARTS_INFO",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 5,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "投标授权人",
        "modelName": "OS_ZB_SUPPLIER_AUTH_PERSON_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 5,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "试验设备信息",
        "modelName": "OS_ZB_SUPPLIER_LABEQP_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 15,
                "imgCount": 15,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "生产装备信息",
        "modelName": "OS_ZB_SUPPLIER_MEQP_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 15,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "认证证书信息",
        "modelName": "OS_ZB_SUPPLIER_CERT_INFO",
        "totalRecord": 50,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 15,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "产品产能信息",
        "modelName": "OS_ZB_SUPPLIER_CAPACITY_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "配送方案",
        "modelName": "OS_ZB_SUPPLIER_BIAO_ASERVICE_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "资信证明",
        "modelName": "OS_ZB_SUPPLIER_BIAO_ZIXIN_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "股权结构",
        "modelName": "OS_ZB_SUPPLIER_EQUITY_INFO",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "保证金主表",
        "modelName": "OS_ZB_SUPPLIER_DEPOSIT",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": [ "ATTACH" ]
            }
        ]
    },
    {
        "remark": "生产能力",
        "modelName": "OS_ZB_SUPPLIER_PRODUCT_PLACE",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "运行评价(引用表)",
        "modelName": "OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE",
        "totalRecord": 10,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "imgCount": 10,
                "imgSize": 0.5,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "其他相关内容",
        "modelName": "OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 10,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "企业类似项目业绩和实施经验【窗体1】",
        "modelName": "OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD",
        "totalRecord": 99,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 15,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "企业类似项目业绩和实施经验【窗体2】",
        "modelName": "OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE",
        "totalRecord": 99,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 15,
                "fileKeys": []
            }
        ]
    },
    {
        "remark": "附件（没有关联的表仅存放在附件表里的)",
        "modelName": "SYS_FILE_INFO",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 15,
                "fileKeys": [ "pm_org_file", "core_resume_file", "work_line_file" ] //项目管理机构组成表 、 主要人员简历表及证明文件、工作大纲 、 工作方案及服务承诺-
            }
        ]
    },
    {
        "remark": "勘察设计负责人（项目经理/设总）及主要设计人员情况表",
        "modelName": "OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL",
        "totalRecord": 99,
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 50,
                "fileKeys": [ "designer_info", "core_peopel_info", "software_info", "core_peopel_commit" ]
                //勘察设计负责人（项目经理/设总）情况表及相关证明材料 、 主要设计人员表及相关证明材料 、 拟投入设备软件情况表（仅三维设计标包适用）及相关证明材料
                //拟投入设备软件情况表（仅三维设计标包适用）及相关证明材料
            }
        ]
    },
    {
        "remark": "拟分包项目情况表",
        "modelName": "OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 50,
                "fileKeys": [ "propose_subpack", "worker_commit" ]
                //&勘察设计负责人（项目经理/设总）情况表及相关证明材料  、 勘察设计主要人员承诺函
            }
        ]
    },
    {
        "remark": "拟派总监及主要监理人员情况表",
        "modelName": "OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 50,
                "fileKeys": [ "propose_chiefStaffer", "core_listener_commit" ]
                //勘察设计负责人（项目经理/设总）情况表及相关证明材料  、 勘察设计主要人员承诺函
            }
        ]
    },
    {
        "remark": "安全、质量、进度等保障措施及方案",
        "modelName": "OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS",
        "uploads": [
            {
                "pdfCount": 1,
                "pdfSize": 50,
                "fileKeys": [ "safe_to_progress" ]
                //上传安全、质量、进度等保障措施及方案
            }
        ]
    },
    {
        "remark": "发票信息",
        "modelName": "OS_ZB_BILL_INFO",
        "totalRecord": 50,
        "uploads": [
            {
                "imgCount": 6,
                "imgSize": 1,
                "fileKeys": []
            }
        ]
    }
    ,
    {
        "remark": "其他模块",
        "modelName": "Other_Tab",
        "uploads": [
            {
                "imgCount": 15,
                "imgSize": 1,
                "pdfCount": 60,
                "pdfSize": 1,
                "fileKeys": []
            }
        ]
    }
]