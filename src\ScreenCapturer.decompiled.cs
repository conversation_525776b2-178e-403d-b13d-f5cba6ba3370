using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.Drawing.Text;
using System.Globalization;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Windows.Forms;
using ScreenCapturer.Properties;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints)]
[assembly: TargetFramework(".NETFramework,Version=v4.6.1", FrameworkDisplayName = ".NET Framework 4.6.1")]
[assembly: AssemblyCompany("raoyutian")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyDescription("A screenshot tool under. Net一个.Net下截图工具")]
[assembly: AssemblyFileVersion("1.3.0")]
[assembly: AssemblyInformationalVersion("1.3.0")]
[assembly: AssemblyProduct("ScreenCapturer")]
[assembly: AssemblyTitle("ScreenCapturer")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://gitee.com/raoyutian/screen-capturer")]
[assembly: AssemblyVersion("*******")]
namespace ScreenCapturer
{
	public class CaptureImageToolColorTable
	{
		private static readonly Color _borderColor = Color.FromArgb(65, 173, 236);

		private static readonly Color _backColorNormal = Color.FromArgb(229, 243, 251);

		private static readonly Color _backColorHover = Color.FromArgb(65, 173, 236);

		private static readonly Color _backColorPressed = Color.FromArgb(24, 142, 206);

		private static readonly Color _foreColor = Color.FromArgb(12, 83, 124);

		public virtual Color BorderColor => _borderColor;

		public virtual Color BackColorNormal => _backColorNormal;

		public virtual Color BackColorHover => _backColorHover;

		public virtual Color BackColorPressed => _backColorPressed;

		public virtual Color ForeColor => _foreColor;
	}
	public class ColorLabel : Control
	{
		private Color _borderColor = Color.FromArgb(65, 173, 236);

		[DefaultValue(typeof(Color), "65, 173, 236")]
		public Color BorderColor
		{
			get
			{
				return _borderColor;
			}
			set
			{
				_borderColor = value;
				Invalidate();
			}
		}

		protected override Size DefaultSize => new Size(16, 16);

		public ColorLabel()
		{
			SetStyles();
		}

		private void SetStyles()
		{
			SetStyle(ControlStyles.UserPaint | ControlStyles.ResizeRedraw | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
			UpdateStyles();
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Graphics graphics = e.Graphics;
			Rectangle clientRectangle = base.ClientRectangle;
			using (SolidBrush brush = new SolidBrush(base.BackColor))
			{
				graphics.FillRectangle(brush, clientRectangle);
			}
			ControlPaint.DrawBorder(graphics, clientRectangle, _borderColor, ButtonBorderStyle.Solid);
			clientRectangle.Inflate(-1, -1);
			ControlPaint.DrawBorder(graphics, clientRectangle, Color.White, ButtonBorderStyle.Solid);
		}
	}
	public class ColorSelector : UserControl
	{
		private CaptureImageToolColorTable _colorTable;

		private static readonly Color InnerBorderColor = Color.FromArgb(200, 255, 255, 255);

		private static readonly object EventColorChanged = new object();

		private static readonly object EventFontSizeChanged = new object();

		private IContainer components;

		private Panel panelLeft;

		private ComboBox comboBoxFontSize;

		private Label labelFont;

		private Panel panelFill;

		private ColorLabel colorLabel16;

		private ColorLabel colorLabel8;

		private ColorLabel colorLabel15;

		private ColorLabel colorLabel7;

		private ColorLabel colorLabel14;

		private ColorLabel colorLabel6;

		private ColorLabel colorLabel13;

		private ColorLabel colorLabel5;

		private ColorLabel colorLabel12;

		private ColorLabel colorLabel4;

		private ColorLabel colorLabel11;

		private ColorLabel colorLabel3;

		private ColorLabel colorLabel10;

		private ColorLabel colorLabel2;

		private ColorLabel colorLabel9;

		private ColorLabel colorLabelSelected;

		private ColorLabel colorLabel1;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public CaptureImageToolColorTable ColorTable
		{
			get
			{
				if (_colorTable == null)
				{
					_colorTable = new CaptureImageToolColorTable();
				}
				return _colorTable;
			}
			set
			{
				_colorTable = value;
				Invalidate();
				SetColorLabelBorderColor(ColorTable.BorderColor);
			}
		}

		[Browsable(false)]
		public Color SelectedColor => colorLabelSelected.BackColor;

		[Browsable(false)]
		public int FontSize => int.Parse(comboBoxFontSize.Text);

		public event EventHandler ColorChanged
		{
			add
			{
				base.Events.AddHandler(EventColorChanged, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventColorChanged, value);
			}
		}

		public event EventHandler FontSizeChanged
		{
			add
			{
				base.Events.AddHandler(EventFontSizeChanged, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventFontSizeChanged, value);
			}
		}

		public ColorSelector()
		{
			InitializeComponent();
			Init();
			DoubleBuffered = true;
			base.ResizeRedraw = true;
		}

		public void Reset()
		{
			colorLabelSelected.BackColor = Color.Red;
			comboBoxFontSize.Text = "12";
			panelLeft.Visible = false;
			base.Width = 189;
		}

		public void ChangeToFontStyle()
		{
			colorLabelSelected.BackColor = Color.Red;
			comboBoxFontSize.Text = "12";
			panelLeft.Visible = true;
			base.Width = 268;
		}

		protected virtual void OnColorChanged(EventArgs e)
		{
			if (base.Events[EventColorChanged] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected virtual void OnFontSizeChanged(EventArgs e)
		{
			if (base.Events[EventFontSizeChanged] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected override void OnCreateControl()
		{
			base.OnCreateControl();
			SetRegion();
		}

		protected override void OnSizeChanged(EventArgs e)
		{
			base.OnSizeChanged(e);
			SetRegion();
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Graphics graphics = e.Graphics;
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			RenderBackgroundInternal(graphics, base.ClientRectangle, ColorTable.BackColorHover, ColorTable.BorderColor, InnerBorderColor, RoundStyle.All, drawBorder: true, drawGlass: true, LinearGradientMode.Vertical);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			RenderBackgroundInternal(g, rect, baseColor, borderColor, innerBorderColor, style, 8, drawBorder, drawGlass, mode);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, int roundWidth, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			RenderBackgroundInternal(g, rect, baseColor, borderColor, innerBorderColor, style, 8, 0.45f, drawBorder, drawGlass, mode);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, int roundWidth, float basePosition, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			if (drawBorder)
			{
				rect.Width--;
				rect.Height--;
			}
			using LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, mode);
			Color[] colors = new Color[4]
			{
				GetColor(baseColor, 0, 35, 24, 9),
				GetColor(baseColor, 0, 13, 8, 3),
				baseColor,
				GetColor(baseColor, 0, 68, 69, 54)
			};
			ColorBlend colorBlend = new ColorBlend();
			colorBlend.Positions = new float[4]
			{
				0f,
				basePosition,
				basePosition + 0.05f,
				1f
			};
			colorBlend.Colors = colors;
			linearGradientBrush.InterpolationColors = colorBlend;
			if (style != 0)
			{
				using (GraphicsPath path = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
				{
					g.FillPath(linearGradientBrush, path);
				}
				if (baseColor.A > 80)
				{
					Rectangle rect2 = rect;
					if (mode == LinearGradientMode.Vertical)
					{
						rect2.Height = (int)((float)rect2.Height * basePosition);
					}
					else
					{
						rect2.Width = (int)((float)rect.Width * basePosition);
					}
					using GraphicsPath path2 = GraphicsPathHelper.CreatePath(rect2, roundWidth, RoundStyle.Top, correction: false);
					using SolidBrush brush = new SolidBrush(Color.FromArgb(80, 255, 255, 255));
					g.FillPath(brush, path2);
				}
				if (drawGlass)
				{
					RectangleF glassRect = rect;
					if (mode == LinearGradientMode.Vertical)
					{
						glassRect.Y = (float)rect.Y + (float)rect.Height * basePosition;
						glassRect.Height = ((float)rect.Height - (float)rect.Height * basePosition) * 2f;
					}
					else
					{
						glassRect.X = (float)rect.X + (float)rect.Width * basePosition;
						glassRect.Width = ((float)rect.Width - (float)rect.Width * basePosition) * 2f;
					}
					ControlPaintEx.DrawGlass(g, glassRect, 170, 0);
				}
				if (!drawBorder)
				{
					return;
				}
				using (GraphicsPath path3 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
				{
					using Pen pen = new Pen(borderColor);
					g.DrawPath(pen, path3);
				}
				rect.Inflate(-1, -1);
				using GraphicsPath path4 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false);
				using Pen pen2 = new Pen(innerBorderColor);
				g.DrawPath(pen2, path4);
				return;
			}
			g.FillRectangle(linearGradientBrush, rect);
			if (baseColor.A > 80)
			{
				Rectangle rect3 = rect;
				if (mode == LinearGradientMode.Vertical)
				{
					rect3.Height = (int)((float)rect3.Height * basePosition);
				}
				else
				{
					rect3.Width = (int)((float)rect.Width * basePosition);
				}
				using SolidBrush brush2 = new SolidBrush(Color.FromArgb(80, 255, 255, 255));
				g.FillRectangle(brush2, rect3);
			}
			if (drawGlass)
			{
				RectangleF glassRect2 = rect;
				if (mode == LinearGradientMode.Vertical)
				{
					glassRect2.Y = (float)rect.Y + (float)rect.Height * basePosition;
					glassRect2.Height = ((float)rect.Height - (float)rect.Height * basePosition) * 2f;
				}
				else
				{
					glassRect2.X = (float)rect.X + (float)rect.Width * basePosition;
					glassRect2.Width = ((float)rect.Width - (float)rect.Width * basePosition) * 2f;
				}
				ControlPaintEx.DrawGlass(g, glassRect2, 200, 0);
			}
			if (drawBorder)
			{
				using (Pen pen3 = new Pen(borderColor))
				{
					g.DrawRectangle(pen3, rect);
				}
				rect.Inflate(-1, -1);
				using Pen pen4 = new Pen(innerBorderColor);
				g.DrawRectangle(pen4, rect);
				return;
			}
		}

		private void SetRegion()
		{
			using GraphicsPath path = GraphicsPathHelper.CreatePath(base.ClientRectangle, 8, RoundStyle.All, correction: false);
			if (base.Region != null)
			{
				base.Region.Dispose();
			}
			base.Region = new Region(path);
		}

		private void Init()
		{
			panelLeft.Visible = false;
			base.Width = 189;
			comboBoxFontSize.Text = "12";
			colorLabelSelected.BackColor = Color.Red;
			colorLabel1.BackColor = Color.Black;
			colorLabel2.BackColor = Color.FromArgb(153, 153, 153);
			colorLabel3.BackColor = Color.FromArgb(128, 0, 0);
			colorLabel4.BackColor = Color.FromArgb(128, 128, 0);
			colorLabel5.BackColor = Color.FromArgb(0, 128, 0);
			colorLabel6.BackColor = Color.FromArgb(0, 0, 128);
			colorLabel7.BackColor = Color.FromArgb(128, 0, 128);
			colorLabel8.BackColor = Color.FromArgb(0, 128, 128);
			colorLabel9.BackColor = Color.White;
			colorLabel10.BackColor = Color.FromArgb(192, 192, 192);
			colorLabel11.BackColor = Color.FromArgb(255, 0, 0);
			colorLabel12.BackColor = Color.FromArgb(255, 255, 0);
			colorLabel13.BackColor = Color.FromArgb(0, 255, 0);
			colorLabel14.BackColor = Color.FromArgb(0, 0, 255);
			colorLabel15.BackColor = Color.FromArgb(255, 0, 255);
			colorLabel16.BackColor = Color.FromArgb(0, 255, 255);
			colorLabel1.Click += ColorLabelClick;
			colorLabel2.Click += ColorLabelClick;
			colorLabel3.Click += ColorLabelClick;
			colorLabel4.Click += ColorLabelClick;
			colorLabel5.Click += ColorLabelClick;
			colorLabel6.Click += ColorLabelClick;
			colorLabel7.Click += ColorLabelClick;
			colorLabel8.Click += ColorLabelClick;
			colorLabel9.Click += ColorLabelClick;
			colorLabel10.Click += ColorLabelClick;
			colorLabel11.Click += ColorLabelClick;
			colorLabel12.Click += ColorLabelClick;
			colorLabel13.Click += ColorLabelClick;
			colorLabel14.Click += ColorLabelClick;
			colorLabel15.Click += ColorLabelClick;
			colorLabel16.Click += ColorLabelClick;
			comboBoxFontSize.SelectedIndexChanged += ComboBoxFontSizeSelectedIndexChanged;
		}

		private void ComboBoxFontSizeSelectedIndexChanged(object sender, EventArgs e)
		{
			OnFontSizeChanged(e);
		}

		private void ColorLabelClick(object sender, EventArgs e)
		{
			Control control = sender as Control;
			colorLabelSelected.BackColor = control.BackColor;
			OnColorChanged(e);
		}

		private void SetColorLabelBorderColor(Color borderColor)
		{
			colorLabel1.BorderColor = borderColor;
			colorLabel2.BorderColor = borderColor;
			colorLabel3.BorderColor = borderColor;
			colorLabel4.BorderColor = borderColor;
			colorLabel5.BorderColor = borderColor;
			colorLabel6.BorderColor = borderColor;
			colorLabel7.BorderColor = borderColor;
			colorLabel8.BorderColor = borderColor;
			colorLabel9.BorderColor = borderColor;
			colorLabel10.BorderColor = borderColor;
			colorLabel11.BorderColor = borderColor;
			colorLabel12.BorderColor = borderColor;
			colorLabel13.BorderColor = borderColor;
			colorLabel14.BorderColor = borderColor;
			colorLabel15.BorderColor = borderColor;
			colorLabel16.BorderColor = borderColor;
		}

		private Color GetColor(Color colorBase, int a, int r, int g, int b)
		{
			int a2 = colorBase.A;
			int r2 = colorBase.R;
			int g2 = colorBase.G;
			int b2 = colorBase.B;
			a = ((a + a2 <= 255) ? Math.Max(0, a + a2) : 255);
			r = ((r + r2 <= 255) ? Math.Max(0, r + r2) : 255);
			g = ((g + g2 <= 255) ? Math.Max(0, g + g2) : 255);
			b = ((b + b2 <= 255) ? Math.Max(0, b + b2) : 255);
			return Color.FromArgb(a, r, g, b);
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.panelLeft = new System.Windows.Forms.Panel();
			this.comboBoxFontSize = new System.Windows.Forms.ComboBox();
			this.labelFont = new System.Windows.Forms.Label();
			this.panelFill = new System.Windows.Forms.Panel();
			this.colorLabel16 = new ScreenCapturer.ColorLabel();
			this.colorLabel8 = new ScreenCapturer.ColorLabel();
			this.colorLabel15 = new ScreenCapturer.ColorLabel();
			this.colorLabel7 = new ScreenCapturer.ColorLabel();
			this.colorLabel14 = new ScreenCapturer.ColorLabel();
			this.colorLabel6 = new ScreenCapturer.ColorLabel();
			this.colorLabel13 = new ScreenCapturer.ColorLabel();
			this.colorLabel5 = new ScreenCapturer.ColorLabel();
			this.colorLabel12 = new ScreenCapturer.ColorLabel();
			this.colorLabel4 = new ScreenCapturer.ColorLabel();
			this.colorLabel11 = new ScreenCapturer.ColorLabel();
			this.colorLabel3 = new ScreenCapturer.ColorLabel();
			this.colorLabel10 = new ScreenCapturer.ColorLabel();
			this.colorLabel2 = new ScreenCapturer.ColorLabel();
			this.colorLabel9 = new ScreenCapturer.ColorLabel();
			this.colorLabelSelected = new ScreenCapturer.ColorLabel();
			this.colorLabel1 = new ScreenCapturer.ColorLabel();
			this.panelLeft.SuspendLayout();
			this.panelFill.SuspendLayout();
			base.SuspendLayout();
			this.panelLeft.BackColor = System.Drawing.Color.Transparent;
			this.panelLeft.Controls.Add(this.comboBoxFontSize);
			this.panelLeft.Controls.Add(this.labelFont);
			this.panelLeft.Dock = System.Windows.Forms.DockStyle.Left;
			this.panelLeft.Location = new System.Drawing.Point(2, 2);
			this.panelLeft.Name = "panelLeft";
			this.panelLeft.Size = new System.Drawing.Size(79, 34);
			this.panelLeft.TabIndex = 0;
			this.panelLeft.Visible = false;
			this.comboBoxFontSize.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.comboBoxFontSize.FormattingEnabled = true;
			this.comboBoxFontSize.Items.AddRange(new object[9] { "8", "9", "10", "11", "12", "14", "16", "18", "20" });
			this.comboBoxFontSize.Location = new System.Drawing.Point(23, 6);
			this.comboBoxFontSize.Name = "comboBoxFontSize";
			this.comboBoxFontSize.Size = new System.Drawing.Size(53, 20);
			this.comboBoxFontSize.TabIndex = 2;
			this.labelFont.Dock = System.Windows.Forms.DockStyle.Left;
			this.labelFont.Image = ScreenCapturer.Properties.Resources.Text;
			this.labelFont.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.labelFont.Location = new System.Drawing.Point(0, 0);
			this.labelFont.Name = "labelFont";
			this.labelFont.Size = new System.Drawing.Size(20, 34);
			this.labelFont.TabIndex = 1;
			this.panelFill.BackColor = System.Drawing.Color.Transparent;
			this.panelFill.Controls.Add(this.colorLabel16);
			this.panelFill.Controls.Add(this.colorLabel8);
			this.panelFill.Controls.Add(this.colorLabel15);
			this.panelFill.Controls.Add(this.colorLabel7);
			this.panelFill.Controls.Add(this.colorLabel14);
			this.panelFill.Controls.Add(this.colorLabel6);
			this.panelFill.Controls.Add(this.colorLabel13);
			this.panelFill.Controls.Add(this.colorLabel5);
			this.panelFill.Controls.Add(this.colorLabel12);
			this.panelFill.Controls.Add(this.colorLabel4);
			this.panelFill.Controls.Add(this.colorLabel11);
			this.panelFill.Controls.Add(this.colorLabel3);
			this.panelFill.Controls.Add(this.colorLabel10);
			this.panelFill.Controls.Add(this.colorLabel2);
			this.panelFill.Controls.Add(this.colorLabel9);
			this.panelFill.Controls.Add(this.colorLabelSelected);
			this.panelFill.Controls.Add(this.colorLabel1);
			this.panelFill.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panelFill.Location = new System.Drawing.Point(81, 2);
			this.panelFill.Name = "panelFill";
			this.panelFill.Padding = new System.Windows.Forms.Padding(3, 0, 0, 0);
			this.panelFill.Size = new System.Drawing.Size(185, 34);
			this.panelFill.TabIndex = 1;
			this.colorLabel16.Location = new System.Drawing.Point(166, 18);
			this.colorLabel16.Name = "colorLabel16";
			this.colorLabel16.Size = new System.Drawing.Size(16, 16);
			this.colorLabel16.TabIndex = 34;
			this.colorLabel16.Text = "colorLabel10";
			this.colorLabel8.Location = new System.Drawing.Point(166, 0);
			this.colorLabel8.Name = "colorLabel8";
			this.colorLabel8.Size = new System.Drawing.Size(16, 16);
			this.colorLabel8.TabIndex = 33;
			this.colorLabel8.Text = "colorLabel11";
			this.colorLabel15.Location = new System.Drawing.Point(148, 18);
			this.colorLabel15.Name = "colorLabel15";
			this.colorLabel15.Size = new System.Drawing.Size(16, 16);
			this.colorLabel15.TabIndex = 32;
			this.colorLabel15.Text = "colorLabel12";
			this.colorLabel7.Location = new System.Drawing.Point(148, 0);
			this.colorLabel7.Name = "colorLabel7";
			this.colorLabel7.Size = new System.Drawing.Size(16, 16);
			this.colorLabel7.TabIndex = 31;
			this.colorLabel7.Text = "colorLabel13";
			this.colorLabel14.Location = new System.Drawing.Point(130, 18);
			this.colorLabel14.Name = "colorLabel14";
			this.colorLabel14.Size = new System.Drawing.Size(16, 16);
			this.colorLabel14.TabIndex = 30;
			this.colorLabel14.Text = "colorLabel14";
			this.colorLabel6.Location = new System.Drawing.Point(130, 0);
			this.colorLabel6.Name = "colorLabel6";
			this.colorLabel6.Size = new System.Drawing.Size(16, 16);
			this.colorLabel6.TabIndex = 29;
			this.colorLabel6.Text = "colorLabel15";
			this.colorLabel13.Location = new System.Drawing.Point(112, 18);
			this.colorLabel13.Name = "colorLabel13";
			this.colorLabel13.Size = new System.Drawing.Size(16, 16);
			this.colorLabel13.TabIndex = 28;
			this.colorLabel13.Text = "colorLabel16";
			this.colorLabel5.Location = new System.Drawing.Point(112, 0);
			this.colorLabel5.Name = "colorLabel5";
			this.colorLabel5.Size = new System.Drawing.Size(16, 16);
			this.colorLabel5.TabIndex = 27;
			this.colorLabel5.Text = "colorLabel17";
			this.colorLabel12.Location = new System.Drawing.Point(94, 18);
			this.colorLabel12.Name = "colorLabel12";
			this.colorLabel12.Size = new System.Drawing.Size(16, 16);
			this.colorLabel12.TabIndex = 26;
			this.colorLabel12.Text = "colorLabel6";
			this.colorLabel4.Location = new System.Drawing.Point(94, 0);
			this.colorLabel4.Name = "colorLabel4";
			this.colorLabel4.Size = new System.Drawing.Size(16, 16);
			this.colorLabel4.TabIndex = 25;
			this.colorLabel4.Text = "colorLabel7";
			this.colorLabel11.Location = new System.Drawing.Point(76, 18);
			this.colorLabel11.Name = "colorLabel11";
			this.colorLabel11.Size = new System.Drawing.Size(16, 16);
			this.colorLabel11.TabIndex = 24;
			this.colorLabel11.Text = "colorLabel8";
			this.colorLabel3.Location = new System.Drawing.Point(76, 0);
			this.colorLabel3.Name = "colorLabel3";
			this.colorLabel3.Size = new System.Drawing.Size(16, 16);
			this.colorLabel3.TabIndex = 23;
			this.colorLabel3.Text = "colorLabel9";
			this.colorLabel10.Location = new System.Drawing.Point(58, 18);
			this.colorLabel10.Name = "colorLabel10";
			this.colorLabel10.Size = new System.Drawing.Size(16, 16);
			this.colorLabel10.TabIndex = 22;
			this.colorLabel10.Text = "colorLabel4";
			this.colorLabel2.Location = new System.Drawing.Point(58, 0);
			this.colorLabel2.Name = "colorLabel2";
			this.colorLabel2.Size = new System.Drawing.Size(16, 16);
			this.colorLabel2.TabIndex = 21;
			this.colorLabel2.Text = "colorLabel5";
			this.colorLabel9.Location = new System.Drawing.Point(40, 18);
			this.colorLabel9.Name = "colorLabel9";
			this.colorLabel9.Size = new System.Drawing.Size(16, 16);
			this.colorLabel9.TabIndex = 20;
			this.colorLabel9.Text = "colorLabel3";
			this.colorLabelSelected.Dock = System.Windows.Forms.DockStyle.Left;
			this.colorLabelSelected.Location = new System.Drawing.Point(3, 0);
			this.colorLabelSelected.Name = "colorLabelSelected";
			this.colorLabelSelected.Size = new System.Drawing.Size(34, 34);
			this.colorLabelSelected.TabIndex = 19;
			this.colorLabelSelected.Text = "colorLabel2";
			this.colorLabel1.Location = new System.Drawing.Point(40, 0);
			this.colorLabel1.Name = "colorLabel1";
			this.colorLabel1.Size = new System.Drawing.Size(16, 16);
			this.colorLabel1.TabIndex = 18;
			this.colorLabel1.Text = "colorLabel1";
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.Controls.Add(this.panelFill);
			base.Controls.Add(this.panelLeft);
			base.Name = "ColorSelector";
			base.Padding = new System.Windows.Forms.Padding(2);
			base.Size = new System.Drawing.Size(268, 38);
			this.panelLeft.ResumeLayout(false);
			this.panelFill.ResumeLayout(false);
			base.ResumeLayout(false);
		}
	}
	public sealed class ControlPaintEx
	{
		public static void DrawCheckedFlag(Graphics graphics, Rectangle rect, Color color)
		{
			PointF[] points = new PointF[3]
			{
				new PointF((float)rect.X + (float)rect.Width / 4.5f, (float)rect.Y + (float)rect.Height / 2.5f),
				new PointF((float)rect.X + (float)rect.Width / 2.5f, (float)rect.Bottom - (float)rect.Height / 3f),
				new PointF((float)rect.Right - (float)rect.Width / 4f, (float)rect.Y + (float)rect.Height / 4.5f)
			};
			using Pen pen = new Pen(color, 2f);
			graphics.DrawLines(pen, points);
		}

		public static void DrawGlass(Graphics g, RectangleF glassRect, int alphaCenter, int alphaSurround)
		{
			DrawGlass(g, glassRect, Color.White, alphaCenter, alphaSurround);
		}

		public static void DrawGlass(Graphics g, RectangleF glassRect, Color glassColor, int alphaCenter, int alphaSurround)
		{
			using GraphicsPath graphicsPath = new GraphicsPath();
			graphicsPath.AddEllipse(glassRect);
			using PathGradientBrush pathGradientBrush = new PathGradientBrush(graphicsPath);
			pathGradientBrush.CenterColor = Color.FromArgb(alphaCenter, glassColor);
			pathGradientBrush.SurroundColors = new Color[1] { Color.FromArgb(alphaSurround, glassColor) };
			pathGradientBrush.CenterPoint = new PointF(glassRect.X + glassRect.Width / 2f, glassRect.Y + glassRect.Height / 2f);
			g.FillPath(pathGradientBrush, graphicsPath);
		}

		public static void DrawBackgroundImage(Graphics g, Image backgroundImage, Color backColor, ImageLayout backgroundImageLayout, Rectangle bounds, Rectangle clipRect)
		{
			DrawBackgroundImage(g, backgroundImage, backColor, backgroundImageLayout, bounds, clipRect, Point.Empty, RightToLeft.No);
		}

		public static void DrawBackgroundImage(Graphics g, Image backgroundImage, Color backColor, ImageLayout backgroundImageLayout, Rectangle bounds, Rectangle clipRect, Point scrollOffset)
		{
			DrawBackgroundImage(g, backgroundImage, backColor, backgroundImageLayout, bounds, clipRect, scrollOffset, RightToLeft.No);
		}

		public static void DrawBackgroundImage(Graphics g, Image backgroundImage, Color backColor, ImageLayout backgroundImageLayout, Rectangle bounds, Rectangle clipRect, Point scrollOffset, RightToLeft rightToLeft)
		{
			if (g == null)
			{
				throw new ArgumentNullException("g");
			}
			if (backgroundImageLayout == ImageLayout.Tile)
			{
				using (TextureBrush textureBrush = new TextureBrush(backgroundImage, WrapMode.Tile))
				{
					if (scrollOffset != Point.Empty)
					{
						Matrix transform = textureBrush.Transform;
						transform.Translate(scrollOffset.X, scrollOffset.Y);
						textureBrush.Transform = transform;
					}
					g.FillRectangle(textureBrush, clipRect);
					return;
				}
			}
			Rectangle rectangle = CalculateBackgroundImageRectangle(bounds, backgroundImage, backgroundImageLayout);
			if (rightToLeft == RightToLeft.Yes && backgroundImageLayout == ImageLayout.None)
			{
				rectangle.X += clipRect.Width - rectangle.Width;
			}
			using (SolidBrush brush = new SolidBrush(backColor))
			{
				g.FillRectangle(brush, clipRect);
			}
			if (!clipRect.Contains(rectangle))
			{
				switch (backgroundImageLayout)
				{
				case ImageLayout.Stretch:
				case ImageLayout.Zoom:
					rectangle.Intersect(clipRect);
					g.DrawImage(backgroundImage, rectangle);
					break;
				case ImageLayout.None:
				{
					rectangle.Offset(clipRect.Location);
					Rectangle destRect2 = rectangle;
					destRect2.Intersect(clipRect);
					Rectangle rectangle3 = new Rectangle(Point.Empty, destRect2.Size);
					g.DrawImage(backgroundImage, destRect2, rectangle3.X, rectangle3.Y, rectangle3.Width, rectangle3.Height, GraphicsUnit.Pixel);
					break;
				}
				default:
				{
					Rectangle destRect = rectangle;
					destRect.Intersect(clipRect);
					Rectangle rectangle2 = new Rectangle(new Point(destRect.X - rectangle.X, destRect.Y - rectangle.Y), destRect.Size);
					g.DrawImage(backgroundImage, destRect, rectangle2.X, rectangle2.Y, rectangle2.Width, rectangle2.Height, GraphicsUnit.Pixel);
					break;
				}
				}
			}
			else
			{
				ImageAttributes imageAttributes = new ImageAttributes();
				imageAttributes.SetWrapMode(WrapMode.TileFlipXY);
				g.DrawImage(backgroundImage, rectangle, 0, 0, backgroundImage.Width, backgroundImage.Height, GraphicsUnit.Pixel, imageAttributes);
				imageAttributes.Dispose();
			}
		}

		internal static Rectangle CalculateBackgroundImageRectangle(Rectangle bounds, Image backgroundImage, ImageLayout imageLayout)
		{
			Rectangle result = bounds;
			if (backgroundImage != null)
			{
				switch (imageLayout)
				{
				case ImageLayout.None:
					result.Size = backgroundImage.Size;
					return result;
				case ImageLayout.Tile:
					return result;
				case ImageLayout.Center:
				{
					result.Size = backgroundImage.Size;
					Size size2 = bounds.Size;
					if (size2.Width > result.Width)
					{
						result.X = (size2.Width - result.Width) / 2;
					}
					if (size2.Height > result.Height)
					{
						result.Y = (size2.Height - result.Height) / 2;
					}
					return result;
				}
				case ImageLayout.Stretch:
					result.Size = bounds.Size;
					return result;
				case ImageLayout.Zoom:
				{
					Size size = backgroundImage.Size;
					float num = (float)bounds.Width / (float)size.Width;
					float num2 = (float)bounds.Height / (float)size.Height;
					if (num >= num2)
					{
						result.Height = bounds.Height;
						result.Width = (int)((double)((float)size.Width * num2) + 0.5);
						if (bounds.X >= 0)
						{
							result.X = (bounds.Width - result.Width) / 2;
						}
						return result;
					}
					result.Width = bounds.Width;
					result.Height = (int)((double)((float)size.Height * num) + 0.5);
					if (bounds.Y >= 0)
					{
						result.Y = (bounds.Height - result.Height) / 2;
					}
					return result;
				}
				}
			}
			return result;
		}
	}
	public enum DrawStyle
	{
		None,
		Rectangle,
		Ellipse,
		Arrow,
		Text,
		Line
	}
	internal class DrawTextData
	{
		private string _text;

		private Font _font;

		private Rectangle _textRect;

		private bool _completed;

		public string Text
		{
			get
			{
				return _text;
			}
			set
			{
				_text = value;
			}
		}

		public Font Font
		{
			get
			{
				return _font;
			}
			set
			{
				_font = value;
			}
		}

		public Rectangle TextRect
		{
			get
			{
				return _textRect;
			}
			set
			{
				_textRect = value;
			}
		}

		public bool Completed
		{
			get
			{
				return _completed;
			}
			set
			{
				_completed = value;
			}
		}

		public DrawTextData()
		{
		}

		public DrawTextData(string text, Font font, Rectangle textRect)
		{
			_text = text;
			_font = font;
			_textRect = textRect;
		}
	}
	public class DrawToolsControl : UserControl
	{
		private CaptureImageToolColorTable _colorTable;

		private DrawStyle _drawStyle;

		private ToolStripButton _checkButton;

		private DrawToolsDockStyle _drawToolsDockStyle;

		private static readonly object EventButtonRedoClick = new object();

		private static readonly object EventButtonSaveClick = new object();

		private static readonly object EventButtonExitClick = new object();

		private static readonly object EventButtonAcceptClick = new object();

		private static readonly object EventButtonDrawStyleClick = new object();

		private IContainer components;

		private ToolStrip toolStrip;

		private ToolStripButton toolStripButtonRectangular;

		private ToolStripButton toolStripButtonEllipse;

		private ToolStripButton toolStripButtonText;

		private ToolStripButton toolStripButtonArrow;

		private ToolStripSeparator toolStripSeparator1;

		private ToolStripButton toolStripButtonLine;

		private ToolStripButton toolStripButtonRedo;

		private ToolStripSeparator toolStripSeparator2;

		private ToolStripButton toolStripButtonExit;

		private ToolStripButton toolStripButtonAccept;

		private ToolStripButton toolStripButtonSave;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public CaptureImageToolColorTable ColorTable
		{
			get
			{
				if (_colorTable == null)
				{
					_colorTable = new CaptureImageToolColorTable();
				}
				return _colorTable;
			}
			set
			{
				_colorTable = value;
				Invalidate();
				toolStrip.Renderer = new ToolStripRendererEx(value);
			}
		}

		[Browsable(false)]
		public DrawStyle DrawStyle => _drawStyle;

		[DefaultValue(typeof(DrawToolsDockStyle), "0")]
		[Browsable(false)]
		public DrawToolsDockStyle DrawToolsDockStyle
		{
			get
			{
				return _drawToolsDockStyle;
			}
			set
			{
				_drawToolsDockStyle = value;
			}
		}

		private ToolStripButton CheckButton
		{
			get
			{
				return _checkButton;
			}
			set
			{
				if (_checkButton != null && _checkButton != value)
				{
					_checkButton.Checked = false;
				}
				_checkButton = value;
				if (_checkButton != null)
				{
					_checkButton.Checked = true;
				}
			}
		}

		protected override Size DefaultSize => new Size(224, 29);

		public event EventHandler ButtonDrawStyleClick
		{
			add
			{
				base.Events.AddHandler(EventButtonDrawStyleClick, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventButtonDrawStyleClick, value);
			}
		}

		public event EventHandler ButtonRedoClick
		{
			add
			{
				base.Events.AddHandler(EventButtonRedoClick, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventButtonRedoClick, value);
			}
		}

		public event EventHandler ButtonSaveClick
		{
			add
			{
				base.Events.AddHandler(EventButtonSaveClick, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventButtonSaveClick, value);
			}
		}

		public event EventHandler ButtonExitClick
		{
			add
			{
				base.Events.AddHandler(EventButtonExitClick, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventButtonExitClick, value);
			}
		}

		public event EventHandler ButtonAcceptClick
		{
			add
			{
				base.Events.AddHandler(EventButtonAcceptClick, value);
			}
			remove
			{
				base.Events.RemoveHandler(EventButtonAcceptClick, value);
			}
		}

		public DrawToolsControl()
		{
			InitializeComponent();
			DoubleBuffered = true;
			base.ResizeRedraw = true;
			InitEvents();
			toolStrip.Renderer = new ToolStripRendererEx();
		}

		public void ResetItemState()
		{
			switch (_drawStyle)
			{
			case DrawStyle.Rectangle:
				toolStripButtonRectangular.Checked = false;
				break;
			case DrawStyle.Ellipse:
				toolStripButtonEllipse.Checked = false;
				break;
			case DrawStyle.Arrow:
				toolStripButtonArrow.Checked = false;
				break;
			case DrawStyle.Text:
				toolStripButtonText.Checked = false;
				break;
			case DrawStyle.Line:
				toolStripButtonLine.Checked = false;
				break;
			}
		}

		public void ResetDrawStyle()
		{
			ResetItemState();
			_drawStyle = DrawStyle.None;
		}

		protected virtual void OnButtonDrawStyleClick(EventArgs e)
		{
			if (base.Events[EventButtonDrawStyleClick] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected virtual void OnButtonRedoClick(EventArgs e)
		{
			if (base.Events[EventButtonRedoClick] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected virtual void OnButtonSaveClick(EventArgs e)
		{
			if (base.Events[EventButtonSaveClick] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected virtual void OnButtonExitClick(EventArgs e)
		{
			if (base.Events[EventButtonExitClick] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected virtual void OnButtonAcceptClick(EventArgs e)
		{
			if (base.Events[EventButtonAcceptClick] is EventHandler eventHandler)
			{
				eventHandler(this, e);
			}
		}

		protected override void OnCreateControl()
		{
			base.OnCreateControl();
			SetRegion();
		}

		protected override void OnSizeChanged(EventArgs e)
		{
			base.OnSizeChanged(e);
			SetRegion();
		}

		protected override void OnMouseEnter(EventArgs e)
		{
			base.OnMouseEnter(e);
			Cursor = Cursors.Default;
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Graphics graphics = e.Graphics;
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			using GraphicsPath path = GraphicsPathHelper.CreatePath(base.ClientRectangle, 8, RoundStyle.All, correction: false);
			using (SolidBrush brush = new SolidBrush(ColorTable.BackColorNormal))
			{
				graphics.FillPath(brush, path);
			}
			using Pen pen = new Pen(ColorTable.BorderColor);
			graphics.DrawPath(pen, path);
			using GraphicsPath path2 = GraphicsPathHelper.CreatePath(base.ClientRectangle, 8, RoundStyle.All, correction: true);
			graphics.DrawPath(pen, path2);
		}

		private void SetRegion()
		{
			using GraphicsPath path = GraphicsPathHelper.CreatePath(base.ClientRectangle, 8, RoundStyle.All, correction: false);
			if (base.Region != null)
			{
				base.Region.Dispose();
			}
			base.Region = new Region(path);
		}

		private void InitEvents()
		{
			toolStrip.ItemClicked += ToolStripItemClicked;
		}

		private void ToolStripItemClicked(object sender, ToolStripItemClickedEventArgs e)
		{
			switch (e.ClickedItem.Name)
			{
			case "toolStripButtonRectangular":
				if (_drawStyle != DrawStyle.Rectangle)
				{
					_drawStyle = DrawStyle.Rectangle;
					CheckButton = toolStripButtonRectangular;
				}
				else
				{
					_drawStyle = DrawStyle.None;
					CheckButton = null;
				}
				OnButtonDrawStyleClick(e);
				break;
			case "toolStripButtonEllipse":
				ResetItemState();
				if (_drawStyle != DrawStyle.Ellipse)
				{
					_drawStyle = DrawStyle.Ellipse;
					CheckButton = toolStripButtonEllipse;
				}
				else
				{
					_drawStyle = DrawStyle.None;
					CheckButton = null;
				}
				OnButtonDrawStyleClick(e);
				break;
			case "toolStripButtonText":
				ResetItemState();
				if (_drawStyle != DrawStyle.Text)
				{
					_drawStyle = DrawStyle.Text;
					CheckButton = toolStripButtonText;
				}
				else
				{
					_drawStyle = DrawStyle.None;
					CheckButton = null;
				}
				OnButtonDrawStyleClick(e);
				break;
			case "toolStripButtonArrow":
				ResetItemState();
				if (_drawStyle != DrawStyle.Arrow)
				{
					_drawStyle = DrawStyle.Arrow;
					CheckButton = toolStripButtonArrow;
				}
				else
				{
					_drawStyle = DrawStyle.None;
					CheckButton = null;
				}
				OnButtonDrawStyleClick(e);
				break;
			case "toolStripButtonLine":
				ResetItemState();
				if (_drawStyle != DrawStyle.Line)
				{
					_drawStyle = DrawStyle.Line;
					CheckButton = toolStripButtonLine;
				}
				else
				{
					_drawStyle = DrawStyle.None;
					CheckButton = null;
				}
				OnButtonDrawStyleClick(e);
				break;
			case "toolStripButtonRedo":
				OnButtonRedoClick(e);
				break;
			case "toolStripButtonSave":
				OnButtonSaveClick(e);
				break;
			case "toolStripButtonExit":
				OnButtonExitClick(e);
				break;
			case "toolStripButtonAccept":
				OnButtonAcceptClick(e);
				break;
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScreenCapturer.DrawToolsControl));
			this.toolStrip = new System.Windows.Forms.ToolStrip();
			this.toolStripButtonRectangular = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonEllipse = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonText = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonArrow = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonLine = new System.Windows.Forms.ToolStripButton();
			this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
			this.toolStripButtonRedo = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonSave = new System.Windows.Forms.ToolStripButton();
			this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
			this.toolStripButtonExit = new System.Windows.Forms.ToolStripButton();
			this.toolStripButtonAccept = new System.Windows.Forms.ToolStripButton();
			this.toolStrip.SuspendLayout();
			base.SuspendLayout();
			this.toolStrip.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStrip.GripStyle = System.Windows.Forms.ToolStripGripStyle.Hidden;
			this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[11]
			{
				this.toolStripButtonRectangular, this.toolStripButtonEllipse, this.toolStripButtonText, this.toolStripButtonArrow, this.toolStripButtonLine, this.toolStripSeparator1, this.toolStripButtonRedo, this.toolStripButtonSave, this.toolStripSeparator2, this.toolStripButtonExit,
				this.toolStripButtonAccept
			});
			this.toolStrip.Location = new System.Drawing.Point(2, 2);
			this.toolStrip.Name = "toolStrip";
			this.toolStrip.Size = new System.Drawing.Size(225, 25);
			this.toolStrip.TabIndex = 0;
			this.toolStrip.Text = "toolStrip1";
			this.toolStripButtonRectangular.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonRectangular.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonRectangular.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonRectangular.Image");
			this.toolStripButtonRectangular.ImageTransparentColor = System.Drawing.Color.Transparent;
			this.toolStripButtonRectangular.Name = "toolStripButtonRectangular";
			this.toolStripButtonRectangular.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonRectangular.Text = "添加矩形";
			this.toolStripButtonEllipse.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonEllipse.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonEllipse.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonEllipse.Image");
			this.toolStripButtonEllipse.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonEllipse.Name = "toolStripButtonEllipse";
			this.toolStripButtonEllipse.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonEllipse.Text = "添加椭圆";
			this.toolStripButtonText.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonText.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonText.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonText.Image");
			this.toolStripButtonText.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonText.Name = "toolStripButtonText";
			this.toolStripButtonText.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonText.Text = "添加文字";
			this.toolStripButtonArrow.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonArrow.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonArrow.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonArrow.Image");
			this.toolStripButtonArrow.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonArrow.Name = "toolStripButtonArrow";
			this.toolStripButtonArrow.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonArrow.Text = "添加箭头";
			this.toolStripButtonLine.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonLine.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonLine.Image = ScreenCapturer.Properties.Resources.Line;
			this.toolStripButtonLine.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonLine.Name = "toolStripButtonLine";
			this.toolStripButtonLine.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonLine.Text = "画笔";
			this.toolStripSeparator1.Name = "toolStripSeparator1";
			this.toolStripSeparator1.Size = new System.Drawing.Size(6, 25);
			this.toolStripButtonRedo.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonRedo.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonRedo.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonRedo.Image");
			this.toolStripButtonRedo.ImageTransparentColor = System.Drawing.Color.Transparent;
			this.toolStripButtonRedo.Name = "toolStripButtonRedo";
			this.toolStripButtonRedo.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonRedo.Text = "撤销";
			this.toolStripButtonSave.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonSave.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonSave.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonSave.Image");
			this.toolStripButtonSave.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonSave.Name = "toolStripButtonSave";
			this.toolStripButtonSave.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonSave.Text = "保存";
			this.toolStripSeparator2.Name = "toolStripSeparator2";
			this.toolStripSeparator2.Size = new System.Drawing.Size(6, 25);
			this.toolStripButtonExit.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.toolStripButtonExit.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonExit.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonExit.Image");
			this.toolStripButtonExit.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonExit.Name = "toolStripButtonExit";
			this.toolStripButtonExit.Size = new System.Drawing.Size(23, 22);
			this.toolStripButtonExit.Text = "退出截图";
			this.toolStripButtonAccept.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Image;
			this.toolStripButtonAccept.Image = (System.Drawing.Image)resources.GetObject("toolStripButtonAccept.Image");
			this.toolStripButtonAccept.ImageTransparentColor = System.Drawing.Color.Magenta;
			this.toolStripButtonAccept.Name = "toolStripButtonAccept";
			this.toolStripButtonAccept.Size = new System.Drawing.Size(23, 20);
			this.toolStripButtonAccept.Text = "完成截图";
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			base.Controls.Add(this.toolStrip);
			this.DoubleBuffered = true;
			base.Name = "DrawToolsControl";
			base.Padding = new System.Windows.Forms.Padding(2);
			base.Size = new System.Drawing.Size(229, 28);
			this.toolStrip.ResumeLayout(false);
			this.toolStrip.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	public enum DrawToolsDockStyle
	{
		None,
		Top,
		BottomUp,
		Bottom
	}
	public static class GraphicsPathHelper
	{
		public static GraphicsPath CreatePath(Rectangle rect, int radius, RoundStyle style, bool correction)
		{
			GraphicsPath graphicsPath = new GraphicsPath();
			int num = (correction ? 1 : 0);
			switch (style)
			{
			case RoundStyle.None:
				graphicsPath.AddRectangle(rect);
				break;
			case RoundStyle.All:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				break;
			case RoundStyle.Left:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddLine(rect.Right - num, rect.Y, rect.Right - num, rect.Bottom - num);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				break;
			case RoundStyle.Right:
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddLine(rect.X, rect.Bottom - num, rect.X, rect.Y);
				break;
			case RoundStyle.Top:
				graphicsPath.AddArc(rect.X, rect.Y, radius, radius, 180f, 90f);
				graphicsPath.AddArc(rect.Right - radius - num, rect.Y, radius, radius, 270f, 90f);
				graphicsPath.AddLine(rect.Right - num, rect.Bottom - num, rect.X, rect.Bottom - num);
				break;
			case RoundStyle.Bottom:
				graphicsPath.AddArc(rect.Right - radius - num, rect.Bottom - radius - num, radius, radius, 0f, 90f);
				graphicsPath.AddArc(rect.X, rect.Bottom - radius - num, radius, radius, 90f, 90f);
				graphicsPath.AddLine(rect.X, rect.Y, rect.Right - num, rect.Y);
				break;
			}
			graphicsPath.CloseFigure();
			return graphicsPath;
		}
	}
	public static class ImageHelper
	{
		public static Bitmap GetScreenCapture()
		{
			return GetScreenCapture(Screen.FromPoint(Cursor.Position));
		}

		public static Bitmap GetScreenCapture(Screen screen)
		{
			Rectangle rectangle = new Rectangle(screen.Bounds.Location.X, screen.Bounds.Location.Y, screen.Bounds.Width, screen.Bounds.Height);
			using Bitmap bitmap = new Bitmap(rectangle.Width, rectangle.Height, PixelFormat.Format24bppRgb);
			using Graphics graphics = Graphics.FromImage(bitmap);
			IntPtr hdc = graphics.GetHdc();
			IntPtr desktopWindow = NativeMethods.GetDesktopWindow();
			IntPtr dC = NativeMethods.GetDC(desktopWindow);
			NativeMethods.BitBlt(hdc, 0, 0, rectangle.Width, rectangle.Height, dC, rectangle.X, rectangle.Y, NativeMethods.TernaryRasterOperations.SRCCOPY);
			NativeMethods.ReleaseDC(desktopWindow, dC);
			graphics.ReleaseHdc(hdc);
			return bitmap.Clone() as Bitmap;
		}

		public static Bitmap GetRectBitmap(this Bitmap bitmap, Rectangle rectangle)
		{
			try
			{
				if (bitmap == null)
				{
					return null;
				}
				if (rectangle.Width == 0 || rectangle.Height == 0)
				{
					return null;
				}
				Bitmap bitmap2 = new Bitmap(rectangle.Width, rectangle.Height);
				using Graphics graphics = Graphics.FromImage(bitmap2);
				Rectangle destRect = new Rectangle(0, 0, rectangle.Width, rectangle.Height);
				graphics.DrawImage(bitmap, destRect, rectangle, GraphicsUnit.Pixel);
				return bitmap2;
			}
			catch (Exception)
			{
				return null;
			}
		}
	}
	internal class NativeMethods
	{
		public struct RECT
		{
			public int Left;

			public int Top;

			public int Right;

			public int Bottom;

			public Rectangle Rect => new Rectangle(Left, Top, Right - Left, Bottom - Top);

			public Size Size => new Size(Right - Left, Bottom - Top);

			public RECT(int left, int top, int right, int bottom)
			{
				Left = left;
				Top = top;
				Right = right;
				Bottom = bottom;
			}

			public RECT(Rectangle rect)
			{
				Left = rect.Left;
				Top = rect.Top;
				Right = rect.Right;
				Bottom = rect.Bottom;
			}

			public static RECT FromXYWH(int x, int y, int width, int height)
			{
				return new RECT(x, y, x + width, y + height);
			}

			public static RECT FromRectangle(Rectangle rect)
			{
				return new RECT(rect.Left, rect.Top, rect.Right, rect.Bottom);
			}
		}

		public enum TernaryRasterOperations
		{
			SRCCOPY = 13369376,
			SRCPAINT = 15597702,
			SRCAND = 8913094,
			SRCINVERT = 6684742,
			SRCERASE = 4457256,
			NOTSRCCOPY = 3342344,
			NOTSRCERASE = 1114278,
			MERGECOPY = 12583114,
			MERGEPAINT = 12255782,
			PATCOPY = 15728673,
			PATPAINT = 16452105,
			PATINVERT = 5898313,
			DSTINVERT = 5570569,
			BLACKNESS = 66,
			WHITENESS = 16711778
		}

		public const int WS_EX_TRANSPARENT = 32;

		[DllImport("user32.dll")]
		public static extern bool ClipCursor(ref RECT lpRect);

		[DllImport("user32.dll")]
		public static extern IntPtr GetDesktopWindow();

		[DllImport("user32.dll")]
		public static extern IntPtr GetDC(IntPtr ptr);

		[DllImport("user32.dll")]
		public static extern int ReleaseDC(IntPtr hwnd, IntPtr hDC);

		[DllImport("gdi32.dll")]
		public static extern bool BitBlt(IntPtr hObject, int nXDest, int nYDest, int nWidth, int nHeight, IntPtr hObjSource, int nXSrc, int nYSrc, TernaryRasterOperations dwRop);

		[DllImport("kernel32.dll", CharSet = CharSet.Auto)]
		public static extern IntPtr LoadLibrary(string lpFileName);
	}
	internal class OperateManager : IDisposable
	{
		private List<OperateObject> _operateList;

		private static readonly int MaxOperateCount = 1000;

		public List<OperateObject> OperateList
		{
			get
			{
				if (_operateList == null)
				{
					_operateList = new List<OperateObject>(100);
				}
				return _operateList;
			}
		}

		public int OperateCount => OperateList.Count;

		public void AddOperate(OperateType operateType, Color color, object data)
		{
			OperateObject item = new OperateObject(operateType, color, data);
			if (OperateList.Count > MaxOperateCount)
			{
				OperateList.RemoveAt(0);
			}
			OperateList.Add(item);
		}

		public bool RedoOperate()
		{
			if (OperateList.Count > 0)
			{
				OperateList.RemoveAt(OperateList.Count - 1);
				return true;
			}
			return false;
		}

		public void Clear()
		{
			OperateList.Clear();
		}

		public void Dispose()
		{
			if (_operateList != null)
			{
				_operateList.Clear();
				_operateList = null;
			}
		}
	}
	internal class OperateObject
	{
		private OperateType _operateType;

		private Color _color;

		private object _data;

		public OperateType OperateType
		{
			get
			{
				return _operateType;
			}
			set
			{
				_operateType = value;
			}
		}

		public Color Color
		{
			get
			{
				return _color;
			}
			set
			{
				_color = value;
			}
		}

		public object Data
		{
			get
			{
				return _data;
			}
			set
			{
				_data = value;
			}
		}

		public OperateObject()
		{
		}

		public OperateObject(OperateType operateType, Color color, object data)
		{
			_operateType = operateType;
			_color = color;
			_data = data;
		}
	}
	internal enum OperateType
	{
		None,
		DrawRectangle,
		DrawEllipse,
		DrawArrow,
		DrawLine,
		DrawText
	}
	internal static class RegionHelper
	{
		public static void CreateRegion(Control control, Rectangle rect)
		{
			using GraphicsPath path = GraphicsPathHelper.CreatePath(rect, 8, RoundStyle.All, correction: false);
			if (control.Region != null)
			{
				control.Region.Dispose();
			}
			control.Region = new Region(path);
		}
	}
	public enum RoundStyle
	{
		None,
		All,
		Left,
		Right,
		Top,
		Bottom
	}
	public class ScreenCapturerTool : Form
	{
		private Image _image;

		private CaptureImageToolColorTable _colorTable;

		private Cursor _selectCursor = Cursors.Default;

		private Cursor _drawCursor = Cursors.Cross;

		private Point _mouseDownPoint;

		private Point _endPoint;

		private bool _mouseDown;

		private bool _mouseUp;

		private Point _mousemovePoint;

		private Rectangle _selectImageRect;

		private Rectangle _selectImageBounds;

		private bool _selectedImage;

		private SizeGrip _sizeGrip;

		private Dictionary<SizeGrip, Rectangle> _sizeGripRectList;

		private OperateManager _operateManager;

		private List<Point> _linePointList;

		private static readonly Font TextFont = new Font("Times New Roman", 12f, FontStyle.Bold, GraphicsUnit.Point, 0);

		private static readonly string ToolTipStartCapture = "按住左键不放选择截图区域";

		private IContainer components;

		private ToolTip toolTip;

		private DrawToolsControl drawToolsControl;

		private SaveFileDialog saveFileDialog;

		private ColorSelector colorSelector;

		private TextBox textBox;

		private ContextMenuStrip contextMenuStrip;

		private ToolStripMenuItem menuItemRedo;

		private ToolStripMenuItem menuItemReselect;

		private ToolStripSeparator toolStripSeparator1;

		private ToolStripMenuItem menuItemAccept;

		private ToolStripMenuItem menuItemSave;

		private ToolStripSeparator toolStripSeparator2;

		private ToolStripMenuItem menuItemExit;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public CaptureImageToolColorTable ColorTable
		{
			get
			{
				if (_colorTable == null)
				{
					_colorTable = new CaptureImageToolColorTable();
				}
				return _colorTable;
			}
			set
			{
				_colorTable = value;
				Invalidate();
				SetControlColorTable();
			}
		}

		public Image Image => _image;

		public Cursor SelectCursor
		{
			get
			{
				return _selectCursor;
			}
			set
			{
				_selectCursor = value;
			}
		}

		public Cursor DrawCursor
		{
			get
			{
				return _drawCursor;
			}
			set
			{
				_drawCursor = value;
			}
		}

		internal bool SelectedImage
		{
			get
			{
				return _selectedImage;
			}
			set
			{
				_selectedImage = value;
			}
		}

		internal Rectangle SelectImageRect
		{
			get
			{
				return _selectImageRect;
			}
			set
			{
				_selectImageRect = value;
				if (!_selectImageRect.IsEmpty)
				{
					CalCulateSizeGripRect();
					Invalidate();
				}
			}
		}

		internal SizeGrip SizeGrip
		{
			get
			{
				return _sizeGrip;
			}
			set
			{
				_sizeGrip = value;
			}
		}

		internal Dictionary<SizeGrip, Rectangle> SizeGripRectList
		{
			get
			{
				if (_sizeGripRectList == null)
				{
					_sizeGripRectList = new Dictionary<SizeGrip, Rectangle>();
				}
				return _sizeGripRectList;
			}
		}

		internal OperateManager OperateManager
		{
			get
			{
				if (_operateManager == null)
				{
					_operateManager = new OperateManager();
				}
				return _operateManager;
			}
		}

		private DrawStyle DrawStyle => drawToolsControl.DrawStyle;

		private Color SelectedColor => colorSelector.SelectedColor;

		private int FontSize => colorSelector.FontSize;

		private List<Point> LinePointList
		{
			get
			{
				if (_linePointList == null)
				{
					_linePointList = new List<Point>(100);
				}
				return _linePointList;
			}
		}

		public ScreenCapturerTool()
		{
			InitializeComponent();
			Init();
		}

		private void SetControlColorTable()
		{
			CaptureImageToolColorTable colorTable = ColorTable;
			ToolStripRendererEx renderer = new ToolStripRendererEx(colorTable);
			contextMenuStrip.Renderer = renderer;
			drawToolsControl.ColorTable = colorTable;
			colorSelector.ColorTable = colorTable;
		}

		protected override void OnLoad(EventArgs e)
		{
			Rectangle bounds = Screen.FromPoint(Cursor.Position).Bounds;
			base.Location = new Point(bounds.Location.X, bounds.Location.Y);
			base.OnLoad(e);
			toolTip.SetToolTip(this, ToolTipStartCapture);
		}

		protected override void OnMouseEnter(EventArgs e)
		{
			base.OnMouseEnter(e);
			Cursor = SelectCursor;
		}

		protected override void OnMouseDown(MouseEventArgs e)
		{
			base.OnMouseDown(e);
			_mouseUp = false;
			if (textBox.Visible)
			{
				if (SelectImageRect.Contains(e.Location) || e.Button == MouseButtons.Left)
				{
					string value = textBox.Text;
					Font font = textBox.Font;
					Color foreColor = textBox.ForeColor;
					HideTextBox();
					if (OperateManager.OperateCount > 0)
					{
						OperateObject operateObject = OperateManager.OperateList[OperateManager.OperateCount - 1];
						if (operateObject.OperateType == OperateType.DrawText)
						{
							DrawTextData drawTextData = operateObject.Data as DrawTextData;
							if (!drawTextData.Completed)
							{
								if (string.IsNullOrEmpty(value))
								{
									OperateManager.RedoOperate();
								}
								else
								{
									operateObject.Color = foreColor;
									drawTextData.Font = font;
									drawTextData.Text = value;
									drawTextData.Completed = true;
								}
							}
						}
					}
				}
				Invalidate();
				return;
			}
			if (e.Button == MouseButtons.Left)
			{
				if (SelectedImage)
				{
					if (SizeGrip != 0)
					{
						_mouseDown = true;
						_mouseDownPoint = e.Location;
						HideDrawToolsControl();
						Invalidate();
					}
					if (DrawStyle != 0 && SelectImageRect.Contains(e.Location))
					{
						_mouseDown = true;
						_mouseDownPoint = e.Location;
						if (DrawStyle == DrawStyle.Line)
						{
							LinePointList.Add(_mouseDownPoint);
						}
						ClipCursor(reset: false);
					}
				}
				else
				{
					_mouseDown = true;
					_mouseDownPoint = e.Location;
				}
			}
			if (e.Button == MouseButtons.Right)
			{
				base.DialogResult = DialogResult.Cancel;
			}
		}

		protected override void OnMouseMove(MouseEventArgs e)
		{
			base.OnMouseMove(e);
			_mousemovePoint = e.Location;
			if (_mouseDown)
			{
				if (!SelectedImage)
				{
					SelectImageRect = GetSelectImageRect(e.Location);
				}
				else if (DrawStyle != 0)
				{
					_endPoint = e.Location;
					if (DrawStyle == DrawStyle.Line)
					{
						LinePointList.Add(_endPoint);
					}
					Invalidate();
				}
				else if (SizeGrip != 0)
				{
					ChangeSelctImageRect(e.Location);
				}
			}
			else if (!SelectedImage)
			{
				Refresh();
			}
			else if (DrawStyle == DrawStyle.None)
			{
				if (OperateManager.OperateCount == 0)
				{
					SetSizeGrip(e.Location);
				}
			}
			else if (SelectImageRect.Contains(e.Location))
			{
				Cursor = DrawCursor;
			}
			else
			{
				Cursor = SelectCursor;
			}
		}

		protected override void OnMouseUp(MouseEventArgs e)
		{
			base.OnMouseUp(e);
			if (e.Button == MouseButtons.Left)
			{
				_mouseUp = true;
				if (!SelectedImage)
				{
					SelectImageRect = GetSelectImageRect(e.Location);
					if (!SelectImageRect.IsEmpty)
					{
						SelectedImage = true;
						ShowDrawToolsControl();
					}
				}
				else
				{
					_endPoint = e.Location;
					Invalidate();
					if (DrawStyle != 0)
					{
						ClipCursor(reset: true);
						AddOperate(e.Location);
					}
					else if (SizeGrip != 0)
					{
						_selectImageBounds = SelectImageRect;
						ShowDrawToolsControl();
						SizeGrip = SizeGrip.None;
					}
				}
				_mouseDown = false;
				_mouseDownPoint = Point.Empty;
			}
			else
			{
				if (e.Button != MouseButtons.Right)
				{
					return;
				}
				if (SelectedImage)
				{
					if (SelectImageRect.Contains(e.Location))
					{
						contextMenuStrip.Show(this, e.Location);
					}
					else
					{
						ResetSelectImage();
					}
				}
				else
				{
					base.DialogResult = DialogResult.Cancel;
				}
			}
		}

		protected override void OnMouseDoubleClick(MouseEventArgs e)
		{
			base.OnMouseDoubleClick(e);
			bool flag = SelectImageRect.Contains(e.Location);
			if (e.Button == MouseButtons.Left)
			{
				if (flag)
				{
					DrawLastImage();
					base.DialogResult = DialogResult.OK;
				}
			}
			else if (e.Button == MouseButtons.Right && !flag)
			{
				base.DialogResult = DialogResult.Cancel;
			}
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			Graphics graphics = e.Graphics;
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			if (SelectImageRect.Width != 0 && SelectImageRect.Height != 0)
			{
				Rectangle selectImageRect = SelectImageRect;
				CaptureImageToolColorTable colorTable = ColorTable;
				if (_mouseDown && (!SelectedImage || SizeGrip != 0))
				{
					using (SolidBrush brush = new SolidBrush(Color.FromArgb(90, colorTable.BackColorNormal)))
					{
						graphics.FillRectangle(brush, selectImageRect);
					}
					DrawImageSizeInfo(graphics, selectImageRect);
				}
				using (Pen pen = new Pen(colorTable.BorderColor))
				{
					graphics.DrawRectangle(pen, selectImageRect);
					using SolidBrush brush2 = new SolidBrush(colorTable.BackColorPressed);
					foreach (Rectangle value in SizeGripRectList.Values)
					{
						graphics.FillRectangle(brush2, value);
					}
				}
				DrawOperate(graphics);
				if (DrawStyle != 0)
				{
					DrawTools(graphics, _endPoint);
				}
			}
			DrawPart(graphics);
		}

		protected override void OnClosing(CancelEventArgs e)
		{
			base.OnClosing(e);
			if (_sizeGripRectList != null)
			{
				_sizeGripRectList.Clear();
				_sizeGripRectList = null;
			}
			if (_operateManager != null)
			{
				_operateManager.Dispose();
				_operateManager = null;
			}
			if (_linePointList != null)
			{
				_linePointList.Clear();
				_linePointList = null;
			}
			_selectCursor = null;
			_drawCursor = null;
		}

		protected override void OnKeyUp(KeyEventArgs e)
		{
			base.OnKeyUp(e);
			if (e.KeyCode == Keys.Escape)
			{
				base.DialogResult = DialogResult.Cancel;
			}
		}

		private void DrawImageSizeInfo(Graphics g, Rectangle rect)
		{
			string text = $"{rect.Width}x{rect.Height}";
			Size size = TextRenderer.MeasureText(text, TextFont);
			Rectangle bounds = Screen.GetBounds(this);
			int num = 0;
			int num2 = 0;
			num = ((rect.X + size.Width <= bounds.Right - 3) ? (rect.X + 2) : (bounds.Right - size.Width - 3));
			num2 = ((rect.Y - size.Width >= bounds.Y + 3) ? (rect.Y - size.Height - 2) : (rect.Y + 2));
			Rectangle rectangle = new Rectangle(num, num2, size.Width, size.Height);
			g.FillRectangle(Brushes.Black, rectangle);
			TextRenderer.DrawText(g, text, TextFont, rectangle, Color.White);
		}

		private void DrawTools(Graphics g, Point point)
		{
			if (!SelectImageRect.Contains(_mouseDownPoint))
			{
				return;
			}
			Color selectedColor = SelectedColor;
			switch (DrawStyle)
			{
			case DrawStyle.Rectangle:
			{
				using Pen pen2 = new Pen(selectedColor);
				g.DrawRectangle(pen2, ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y)));
				break;
			}
			case DrawStyle.Ellipse:
			{
				using Pen pen5 = new Pen(selectedColor);
				g.DrawEllipse(pen5, ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y)));
				break;
			}
			case DrawStyle.Arrow:
			{
				using Pen pen4 = new Pen(selectedColor);
				pen4.EndCap = LineCap.ArrowAnchor;
				pen4.EndCap = LineCap.Custom;
				pen4.CustomEndCap = new AdjustableArrowCap(4f, 4f, isFilled: true);
				g.DrawLine(pen4, _mouseDownPoint, point);
				break;
			}
			case DrawStyle.Text:
			{
				using Pen pen3 = new Pen(selectedColor);
				pen3.DashStyle = DashStyle.DashDot;
				pen3.DashCap = DashCap.Round;
				pen3.DashPattern = new float[4] { 9f, 3f, 3f, 3f };
				g.DrawRectangle(pen3, ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y)));
				break;
			}
			case DrawStyle.Line:
			{
				if (LinePointList.Count < 2)
				{
					break;
				}
				Point[] points = LinePointList.ToArray();
				using Pen pen = new Pen(selectedColor);
				g.DrawLines(pen, points);
				break;
			}
			}
		}

		private void DrawOperate(Graphics g)
		{
			foreach (OperateObject operate in OperateManager.OperateList)
			{
				switch (operate.OperateType)
				{
				case OperateType.DrawRectangle:
				{
					using (Pen pen4 = new Pen(operate.Color))
					{
						g.DrawRectangle(pen4, (Rectangle)operate.Data);
					}
					break;
				}
				case OperateType.DrawEllipse:
				{
					using (Pen pen3 = new Pen(operate.Color))
					{
						g.DrawEllipse(pen3, (Rectangle)operate.Data);
					}
					break;
				}
				case OperateType.DrawArrow:
				{
					Point[] array = operate.Data as Point[];
					using (Pen pen5 = new Pen(operate.Color))
					{
						pen5.EndCap = LineCap.Custom;
						pen5.CustomEndCap = new AdjustableArrowCap(4f, 4f, isFilled: true);
						g.DrawLine(pen5, array[0], array[1]);
					}
					break;
				}
				case OperateType.DrawText:
				{
					DrawTextData drawTextData = operate.Data as DrawTextData;
					if (string.IsNullOrEmpty(drawTextData.Text))
					{
						using Pen pen2 = new Pen(operate.Color);
						pen2.DashStyle = DashStyle.DashDot;
						pen2.DashCap = DashCap.Round;
						pen2.DashPattern = new float[4] { 9f, 3f, 3f, 3f };
						g.DrawRectangle(pen2, drawTextData.TextRect);
					}
					else
					{
						using SolidBrush brush = new SolidBrush(operate.Color);
						g.DrawString(drawTextData.Text, drawTextData.Font, brush, drawTextData.TextRect);
					}
					break;
				}
				case OperateType.DrawLine:
				{
					using (Pen pen = new Pen(operate.Color))
					{
						g.DrawLines(pen, operate.Data as Point[]);
					}
					break;
				}
				}
			}
		}

		private void DrawPart(Graphics g)
		{
			if (!_mouseUp && !SelectedImage)
			{
				string s = $"POS:({_mousemovePoint.X},{_mousemovePoint.Y})";
				Color pixel = ((Bitmap)BackgroundImage).GetPixel(_mousemovePoint.X, _mousemovePoint.Y);
				string s2 = $"RGB:({pixel.R},{pixel.G},{pixel.B})";
				Point mousemovePoint = _mousemovePoint;
				mousemovePoint.Offset(20, 20);
				g.DrawImage(srcRect: new Rectangle(_mousemovePoint.X - 20, _mousemovePoint.Y - 20, 40, 40), destRect: new Rectangle(mousemovePoint.X, mousemovePoint.Y, 130, 130), image: BackgroundImage, srcUnit: GraphicsUnit.Pixel);
				g.FillRectangle(Brushes.Black, new Rectangle(mousemovePoint.X, mousemovePoint.Y + 130, 130, 40));
				g.DrawRectangle(new Pen(ColorTable.BorderColor), new Rectangle(mousemovePoint.X, mousemovePoint.Y, 130, 130));
				g.DrawLine(new Pen(ColorTable.BorderColor), mousemovePoint.X, mousemovePoint.Y + 65, mousemovePoint.X + 130, mousemovePoint.Y + 65);
				g.DrawLine(new Pen(ColorTable.BorderColor), mousemovePoint.X + 65, mousemovePoint.Y, mousemovePoint.X + 65, mousemovePoint.Y + 130);
				mousemovePoint.Offset(5, 135);
				Font font = new Font("宋体", 9f, FontStyle.Bold, GraphicsUnit.Point, 0);
				g.DrawString(s, font, Brushes.White, mousemovePoint);
				mousemovePoint.Offset(0, 15);
				g.DrawString(s2, font, Brushes.White, mousemovePoint);
			}
		}

		private void DrawLastImage()
		{
			using Bitmap image = new Bitmap(base.Width, base.Height, PixelFormat.Format32bppArgb);
			using Graphics graphics = Graphics.FromImage(image);
			graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			graphics.DrawImage(BackgroundImage, Point.Empty);
			DrawOperate(graphics);
			graphics.Flush();
			Bitmap image2 = new Bitmap(SelectImageRect.Width, SelectImageRect.Height, PixelFormat.Format32bppArgb);
			Graphics graphics2 = Graphics.FromImage(image2);
			graphics2.DrawImage(image, 0, 0, SelectImageRect, GraphicsUnit.Pixel);
			graphics2.Flush();
			graphics2.Dispose();
			_image = image2;
		}

		private void ColorSelectorColorChanged(object sender, EventArgs e)
		{
			if (DrawStyle == DrawStyle.Text && textBox.Visible)
			{
				textBox.ForeColor = SelectedColor;
			}
		}

		private void DrawToolsControlButtonDrawStyleClick(object sender, EventArgs e)
		{
			switch (DrawStyle)
			{
			case DrawStyle.Rectangle:
			case DrawStyle.Ellipse:
			case DrawStyle.Arrow:
			case DrawStyle.Line:
				colorSelector.Reset();
				ShowColorSelector();
				if (SizeGrip != 0)
				{
					SizeGrip = SizeGrip.None;
				}
				break;
			case DrawStyle.Text:
				colorSelector.ChangeToFontStyle();
				ShowColorSelector();
				if (SizeGrip != 0)
				{
					SizeGrip = SizeGrip.None;
				}
				break;
			case DrawStyle.None:
				HideColorSelector();
				break;
			}
		}

		private void DrawToolsControlButtonRedoClick(object sender, EventArgs e)
		{
			if (OperateManager.OperateCount > 0)
			{
				OperateManager.RedoOperate();
				Invalidate();
			}
			else if (SelectedImage)
			{
				ResetSelectImage();
				Invalidate();
			}
		}

		private void DrawToolsControlButtonSaveClick(object sender, EventArgs e)
		{
			if (SelectedImage)
			{
				if (saveFileDialog.ShowDialog() == DialogResult.OK)
				{
					DrawLastImage();
					string fileName = saveFileDialog.FileName;
					int num = fileName.LastIndexOf('.');
					string text = fileName.Substring(num + 1, fileName.Length - num - 1);
					text = text.ToLower();
					ImageFormat format = ImageFormat.Bmp;
					switch (text)
					{
					case "jpg":
					case "jpeg":
						format = ImageFormat.Jpeg;
						break;
					case "png":
						format = ImageFormat.Png;
						break;
					case "gif":
						format = ImageFormat.Gif;
						break;
					}
					Image.Save(saveFileDialog.FileName, format);
				}
			}
			else
			{
				MessageBox.Show("请先选择图像。", "截图", MessageBoxButtons.OK);
			}
		}

		private void DrawToolsControlButtonAcceptClick(object sender, EventArgs e)
		{
			if (SelectedImage)
			{
				DrawLastImage();
				base.DialogResult = DialogResult.OK;
			}
			else
			{
				base.DialogResult = DialogResult.Cancel;
			}
		}

		private void DrawToolsControlButtonExitClick(object sender, EventArgs e)
		{
			base.DialogResult = DialogResult.Cancel;
		}

		private void MenuItemReselectClick(object sender, EventArgs e)
		{
			if (SelectedImage)
			{
				ResetSelectImage();
			}
		}

		private void TextBoxExLostFocus(object sender, EventArgs e)
		{
			if (!textBox.Visible)
			{
				return;
			}
			string value = textBox.Text;
			Font font = textBox.Font;
			Color foreColor = textBox.ForeColor;
			HideTextBox();
			if (OperateManager.OperateCount > 0)
			{
				OperateObject operateObject = OperateManager.OperateList[OperateManager.OperateCount - 1];
				if (operateObject.OperateType == OperateType.DrawText)
				{
					DrawTextData drawTextData = operateObject.Data as DrawTextData;
					if (!drawTextData.Completed)
					{
						if (string.IsNullOrEmpty(value))
						{
							OperateManager.RedoOperate();
						}
						else
						{
							operateObject.Color = foreColor;
							drawTextData.Font = font;
							drawTextData.Text = value;
							drawTextData.Completed = true;
						}
					}
				}
			}
			Invalidate();
		}

		private void Init()
		{
			SetStyle(ControlStyles.UserPaint | ControlStyles.AllPaintingInWmPaint | ControlStyles.OptimizedDoubleBuffer, value: true);
			UpdateStyles();
			drawToolsControl.Visible = false;
			colorSelector.Visible = false;
			textBox.Visible = false;
			base.TopMost = true;
			base.ShowInTaskbar = false;
			base.FormBorderStyle = FormBorderStyle.None;
			base.Bounds = Screen.FromPoint(Cursor.Position).Bounds;
			BackgroundImage = GetDestopImage();
			Cursor = SelectCursor;
			contextMenuStrip.Renderer = new ToolStripRendererEx();
			textBox.LostFocus += TextBoxExLostFocus;
			colorSelector.ColorChanged += ColorSelectorColorChanged;
			drawToolsControl.ButtonExitClick += DrawToolsControlButtonExitClick;
			drawToolsControl.ButtonAcceptClick += DrawToolsControlButtonAcceptClick;
			drawToolsControl.ButtonSaveClick += DrawToolsControlButtonSaveClick;
			drawToolsControl.ButtonRedoClick += DrawToolsControlButtonRedoClick;
			drawToolsControl.ButtonDrawStyleClick += DrawToolsControlButtonDrawStyleClick;
			menuItemExit.Click += DrawToolsControlButtonExitClick;
			menuItemAccept.Click += DrawToolsControlButtonAcceptClick;
			menuItemSave.Click += DrawToolsControlButtonSaveClick;
			menuItemRedo.Click += DrawToolsControlButtonRedoClick;
			menuItemReselect.Click += MenuItemReselectClick;
		}

		private Image GetDestopImage()
		{
			Rectangle bounds = Screen.FromPoint(Cursor.Position).Bounds;
			Bitmap bitmap = new Bitmap(bounds.Width, bounds.Height, PixelFormat.Format32bppArgb);
			Graphics graphics = Graphics.FromImage(bitmap);
			IntPtr hdc = graphics.GetHdc();
			IntPtr desktopWindow = NativeMethods.GetDesktopWindow();
			IntPtr dC = NativeMethods.GetDC(desktopWindow);
			NativeMethods.BitBlt(hdc, 0, 0, base.Width, base.Height, dC, bounds.X, bounds.Y, NativeMethods.TernaryRasterOperations.SRCCOPY);
			NativeMethods.ReleaseDC(desktopWindow, dC);
			graphics.ReleaseHdc(hdc);
			return bitmap;
		}

		private Rectangle GetSelectImageRect(Point endPoint)
		{
			_selectImageBounds = Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, endPoint.X, endPoint.Y);
			return ImageBoundsToRect(_selectImageBounds);
		}

		private void CalCulateSizeGripRect()
		{
			Rectangle selectImageRect = SelectImageRect;
			int num = selectImageRect.X;
			int num2 = selectImageRect.Y;
			int num3 = num + selectImageRect.Width / 2;
			int num4 = num2 + selectImageRect.Height / 2;
			Dictionary<SizeGrip, Rectangle> sizeGripRectList = SizeGripRectList;
			sizeGripRectList.Clear();
			sizeGripRectList.Add(SizeGrip.TopLeft, new Rectangle(num - 2, num2 - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.TopRight, new Rectangle(selectImageRect.Right - 2, num2 - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.BottomLeft, new Rectangle(num - 2, selectImageRect.Bottom - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.BottomRight, new Rectangle(selectImageRect.Right - 2, selectImageRect.Bottom - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.Top, new Rectangle(num3 - 2, num2 - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.Bottom, new Rectangle(num3 - 2, selectImageRect.Bottom - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.Left, new Rectangle(num - 2, num4 - 2, 5, 5));
			sizeGripRectList.Add(SizeGrip.Right, new Rectangle(selectImageRect.Right - 2, num4 - 2, 5, 5));
		}

		private void SetSizeGrip(Point point)
		{
			SizeGrip = SizeGrip.None;
			foreach (SizeGrip key in SizeGripRectList.Keys)
			{
				if (SizeGripRectList[key].Contains(point))
				{
					SizeGrip = key;
					break;
				}
			}
			if (SizeGrip == SizeGrip.None && SelectImageRect.Contains(point))
			{
				SizeGrip = SizeGrip.All;
			}
			switch (SizeGrip)
			{
			case SizeGrip.TopLeft:
			case SizeGrip.BottomRight:
				Cursor = Cursors.SizeNWSE;
				break;
			case SizeGrip.TopRight:
			case SizeGrip.BottomLeft:
				Cursor = Cursors.SizeNESW;
				break;
			case SizeGrip.Top:
			case SizeGrip.Bottom:
				Cursor = Cursors.SizeNS;
				break;
			case SizeGrip.Left:
			case SizeGrip.Right:
				Cursor = Cursors.SizeWE;
				break;
			case SizeGrip.All:
				Cursor = Cursors.SizeAll;
				break;
			default:
				Cursor = SelectCursor;
				break;
			}
		}

		private void ChangeSelctImageRect(Point point)
		{
			Rectangle selectImageBounds = _selectImageBounds;
			int left = selectImageBounds.Left;
			int top = selectImageBounds.Top;
			int right = selectImageBounds.Right;
			int bottom = selectImageBounds.Bottom;
			bool flag = false;
			switch (SizeGrip)
			{
			case SizeGrip.All:
				selectImageBounds.Offset(point.X - _mouseDownPoint.X, point.Y - _mouseDownPoint.Y);
				flag = true;
				break;
			case SizeGrip.TopLeft:
				left = point.X;
				top = point.Y;
				break;
			case SizeGrip.TopRight:
				right = point.X;
				top = point.Y;
				break;
			case SizeGrip.BottomLeft:
				left = point.X;
				bottom = point.Y;
				break;
			case SizeGrip.BottomRight:
				right = point.X;
				bottom = point.Y;
				break;
			case SizeGrip.Top:
				top = point.Y;
				break;
			case SizeGrip.Bottom:
				bottom = point.Y;
				break;
			case SizeGrip.Left:
				left = point.X;
				break;
			case SizeGrip.Right:
				right = point.X;
				break;
			}
			if (!flag)
			{
				selectImageBounds.X = left;
				selectImageBounds.Y = top;
				selectImageBounds.Width = right - left;
				selectImageBounds.Height = bottom - top;
			}
			_mouseDownPoint = point;
			_selectImageBounds = selectImageBounds;
			SelectImageRect = ImageBoundsToRect(selectImageBounds);
		}

		private Rectangle ImageBoundsToRect(Rectangle bounds)
		{
			Rectangle result = bounds;
			int num = 0;
			int num2 = 0;
			num = Math.Min(result.X, result.Right);
			num2 = Math.Min(result.Y, result.Bottom);
			result.X = num;
			result.Y = num2;
			result.Width = Math.Max(1, Math.Abs(result.Width));
			result.Height = Math.Max(1, Math.Abs(result.Height));
			return result;
		}

		private void ResetSelectImage()
		{
			SelectedImage = false;
			_mouseUp = false;
			_selectImageBounds = Rectangle.Empty;
			SelectImageRect = Rectangle.Empty;
			SizeGrip = SizeGrip.None;
			HideDrawToolsControl();
			if (textBox.Visible)
			{
				HideTextBox();
			}
			OperateManager.Clear();
			Invalidate();
		}

		private void ShowDrawToolsControl()
		{
			Rectangle selectImageRect = SelectImageRect;
			Rectangle bounds = Screen.GetBounds(this);
			int num = selectImageRect.Right - drawToolsControl.Width - 2;
			int num2 = 0;
			DrawToolsDockStyle drawToolsDockStyle = DrawToolsDockStyle.None;
			if (selectImageRect.Bottom + drawToolsControl.Height + 2 <= bounds.Bottom)
			{
				num2 = selectImageRect.Bottom + 2;
				drawToolsDockStyle = DrawToolsDockStyle.Bottom;
			}
			else if (selectImageRect.Y - drawToolsControl.Height - 2 >= bounds.Top)
			{
				num2 = selectImageRect.Y - drawToolsControl.Height - 2;
				drawToolsDockStyle = DrawToolsDockStyle.Top;
			}
			else
			{
				num2 = selectImageRect.Bottom - drawToolsControl.Height - 2;
				drawToolsDockStyle = DrawToolsDockStyle.BottomUp;
			}
			drawToolsControl.DrawToolsDockStyle = drawToolsDockStyle;
			drawToolsControl.Location = new Point(num, num2);
			drawToolsControl.Visible = true;
		}

		private void HideDrawToolsControl()
		{
			drawToolsControl.Visible = false;
			drawToolsControl.ResetDrawStyle();
			HideColorSelector();
		}

		private void ShowColorSelector()
		{
			int num = 0;
			int num2 = 0;
			Rectangle bounds = drawToolsControl.Bounds;
			Screen.GetBounds(this);
			switch (drawToolsControl.DrawToolsDockStyle)
			{
			case DrawToolsDockStyle.Top:
			case DrawToolsDockStyle.BottomUp:
				num = bounds.X;
				num2 = bounds.Y - colorSelector.Height - 2;
				break;
			case DrawToolsDockStyle.Bottom:
				num = bounds.X;
				num2 = bounds.Bottom + 2;
				break;
			}
			colorSelector.Location = new Point(num, num2);
			colorSelector.Visible = true;
		}

		private void HideColorSelector()
		{
			if (colorSelector.Visible)
			{
				colorSelector.Visible = false;
				colorSelector.Reset();
			}
		}

		private void ShowTextBox()
		{
			if (SelectImageRect.Contains(_mouseDownPoint))
			{
				Rectangle bounds = ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, _endPoint.X, _endPoint.Y));
				bounds.Inflate(-1, -1);
				textBox.Bounds = bounds;
				textBox.Text = "";
				textBox.ForeColor = SelectedColor;
				textBox.Font = new Font(textBox.Font.FontFamily, FontSize);
				textBox.Visible = true;
				textBox.Focus();
			}
		}

		private void HideTextBox()
		{
			textBox.Visible = false;
			textBox.Text = string.Empty;
		}

		private void AddOperate(Point point)
		{
			if (!SelectImageRect.Contains(_mouseDownPoint))
			{
				return;
			}
			Color selectedColor = SelectedColor;
			switch (DrawStyle)
			{
			case DrawStyle.Rectangle:
				OperateManager.AddOperate(OperateType.DrawRectangle, selectedColor, ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y)));
				break;
			case DrawStyle.Ellipse:
				OperateManager.AddOperate(OperateType.DrawEllipse, selectedColor, ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y)));
				break;
			case DrawStyle.Arrow:
			{
				Point[] data2 = new Point[2] { _mouseDownPoint, point };
				OperateManager.AddOperate(OperateType.DrawArrow, selectedColor, data2);
				break;
			}
			case DrawStyle.Text:
			{
				ShowTextBox();
				Rectangle textRect = ImageBoundsToRect(Rectangle.FromLTRB(_mouseDownPoint.X, _mouseDownPoint.Y, point.X, point.Y));
				DrawTextData data = new DrawTextData(string.Empty, base.Font, textRect);
				OperateManager.AddOperate(OperateType.DrawText, selectedColor, data);
				break;
			}
			case DrawStyle.Line:
				if (LinePointList.Count >= 2)
				{
					OperateManager.AddOperate(OperateType.DrawLine, selectedColor, LinePointList.ToArray());
					LinePointList.Clear();
				}
				break;
			}
		}

		private void ClipCursor(bool reset)
		{
			Rectangle rect = ((!reset) ? SelectImageRect : Screen.GetBounds(this));
			NativeMethods.RECT lpRect = new NativeMethods.RECT(rect);
			NativeMethods.ClipCursor(ref lpRect);
		}

		private void ImageCapturer_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			if (SelectedImage)
			{
				DrawLastImage();
				base.DialogResult = DialogResult.OK;
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			this.toolTip = new System.Windows.Forms.ToolTip(this.components);
			this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
			this.textBox = new System.Windows.Forms.TextBox();
			this.contextMenuStrip = new System.Windows.Forms.ContextMenuStrip(this.components);
			this.menuItemRedo = new System.Windows.Forms.ToolStripMenuItem();
			this.menuItemReselect = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
			this.menuItemAccept = new System.Windows.Forms.ToolStripMenuItem();
			this.menuItemSave = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
			this.menuItemExit = new System.Windows.Forms.ToolStripMenuItem();
			this.colorSelector = new ScreenCapturer.ColorSelector();
			this.drawToolsControl = new ScreenCapturer.DrawToolsControl();
			this.contextMenuStrip.SuspendLayout();
			base.SuspendLayout();
			this.saveFileDialog.DefaultExt = "bmp";
			this.saveFileDialog.Filter = "BMP 文件(*.bmp)|*.bmp|JPEG 文件(*.jpg,*.jpeg)|*.jpg,*.jpeg|PNG 文件(*.png)|*.png|GIF 文件(*.gif)|*.gif";
			this.textBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.textBox.ImeMode = System.Windows.Forms.ImeMode.On;
			this.textBox.Location = new System.Drawing.Point(12, 91);
			this.textBox.Multiline = true;
			this.textBox.Name = "textBox";
			this.textBox.Size = new System.Drawing.Size(100, 21);
			this.textBox.TabIndex = 4;
			this.contextMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[7] { this.menuItemRedo, this.menuItemReselect, this.toolStripSeparator1, this.menuItemAccept, this.menuItemSave, this.toolStripSeparator2, this.menuItemExit });
			this.contextMenuStrip.Name = "contextMenuStrip";
			this.contextMenuStrip.Size = new System.Drawing.Size(173, 126);
			this.menuItemRedo.Image = ScreenCapturer.Properties.Resources.Redo;
			this.menuItemRedo.Name = "menuItemRedo";
			this.menuItemRedo.Size = new System.Drawing.Size(172, 22);
			this.menuItemRedo.Text = "撤销编辑";
			this.menuItemReselect.Name = "menuItemReselect";
			this.menuItemReselect.Size = new System.Drawing.Size(172, 22);
			this.menuItemReselect.Text = "重新选择截图区域";
			this.toolStripSeparator1.Name = "toolStripSeparator1";
			this.toolStripSeparator1.Size = new System.Drawing.Size(169, 6);
			this.menuItemAccept.Image = ScreenCapturer.Properties.Resources.Accept;
			this.menuItemAccept.Name = "menuItemAccept";
			this.menuItemAccept.Size = new System.Drawing.Size(172, 22);
			this.menuItemAccept.Text = "复制并退出截图";
			this.menuItemSave.Image = ScreenCapturer.Properties.Resources.Save;
			this.menuItemSave.Name = "menuItemSave";
			this.menuItemSave.Size = new System.Drawing.Size(172, 22);
			this.menuItemSave.Text = "另存为...";
			this.toolStripSeparator2.Name = "toolStripSeparator2";
			this.toolStripSeparator2.Size = new System.Drawing.Size(169, 6);
			this.menuItemExit.Image = ScreenCapturer.Properties.Resources.Exit;
			this.menuItemExit.Name = "menuItemExit";
			this.menuItemExit.Size = new System.Drawing.Size(172, 22);
			this.menuItemExit.Text = "退出截图";
			this.colorSelector.Location = new System.Drawing.Point(12, 47);
			this.colorSelector.Name = "colorSelector";
			this.colorSelector.Padding = new System.Windows.Forms.Padding(2);
			this.colorSelector.Size = new System.Drawing.Size(189, 38);
			this.colorSelector.TabIndex = 3;
			this.drawToolsControl.BackColor = System.Drawing.Color.FromArgb(0, 122, 204);
			this.drawToolsControl.Location = new System.Drawing.Point(12, 12);
			this.drawToolsControl.Name = "drawToolsControl";
			this.drawToolsControl.Padding = new System.Windows.Forms.Padding(2);
			this.drawToolsControl.Size = new System.Drawing.Size(224, 29);
			this.drawToolsControl.TabIndex = 0;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(601, 313);
			base.Controls.Add(this.textBox);
			base.Controls.Add(this.colorSelector);
			base.Controls.Add(this.drawToolsControl);
			this.DoubleBuffered = true;
			base.Name = "ScreenCapturerTool";
			this.Text = "CaptureImageTool";
			base.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(ImageCapturer_MouseDoubleClick);
			this.contextMenuStrip.ResumeLayout(false);
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	internal enum SizeGrip
	{
		None,
		Top,
		Bottom,
		Left,
		Right,
		TopLeft,
		TopRight,
		BottomLeft,
		BottomRight,
		All
	}
	public class ToolStripRendererEx : ToolStripRenderer
	{
		private CaptureImageToolColorTable _colorTable;

		private static readonly int OffsetMargin = 24;

		private const string MenuLogoString = "csharpwin.com";

		protected virtual CaptureImageToolColorTable ColorTable
		{
			get
			{
				if (_colorTable == null)
				{
					_colorTable = new CaptureImageToolColorTable();
				}
				return _colorTable;
			}
		}

		public ToolStripRendererEx()
		{
		}

		public ToolStripRendererEx(CaptureImageToolColorTable colorTable)
		{
			_colorTable = colorTable;
		}

		protected override void OnRenderToolStripBackground(ToolStripRenderEventArgs e)
		{
			_ = ColorTable.BackColorNormal;
			ToolStrip toolStrip = e.ToolStrip;
			Graphics graphics = e.Graphics;
			graphics.SmoothingMode = SmoothingMode.AntiAlias;
			if (toolStrip is ToolStripDropDown)
			{
				RegionHelper.CreateRegion(e.ToolStrip, e.AffectedBounds);
				Rectangle affectedBounds = e.AffectedBounds;
				using GraphicsPath path = GraphicsPathHelper.CreatePath(affectedBounds, 8, RoundStyle.All, correction: false);
				using (SolidBrush brush = new SolidBrush(ColorTable.BackColorNormal))
				{
					graphics.FillPath(brush, path);
				}
				using Pen pen = new Pen(ColorTable.BorderColor);
				graphics.DrawPath(pen, path);
				using GraphicsPath path2 = GraphicsPathHelper.CreatePath(affectedBounds, 8, RoundStyle.All, correction: true);
				graphics.DrawPath(pen, path2);
				return;
			}
			LinearGradientMode mode = ((e.ToolStrip.Orientation == Orientation.Horizontal) ? LinearGradientMode.Vertical : LinearGradientMode.Horizontal);
			RenderBackgroundInternal(graphics, e.AffectedBounds, ColorTable.BackColorHover, ColorTable.BorderColor, ColorTable.BackColorNormal, RoundStyle.All, drawBorder: false, drawGlass: true, mode);
		}

		protected override void OnRenderButtonBackground(ToolStripItemRenderEventArgs e)
		{
			if (e.Item is ToolStripButton toolStripButton)
			{
				LinearGradientMode mode = ((e.ToolStrip.Orientation == Orientation.Horizontal) ? LinearGradientMode.Vertical : LinearGradientMode.Horizontal);
				Graphics graphics = e.Graphics;
				graphics.SmoothingMode = SmoothingMode.AntiAlias;
				Rectangle rectangle = new Rectangle(Point.Empty, toolStripButton.Size);
				if (toolStripButton.BackgroundImage != null)
				{
					Rectangle clipRect = (toolStripButton.Selected ? toolStripButton.ContentRectangle : rectangle);
					ControlPaintEx.DrawBackgroundImage(graphics, toolStripButton.BackgroundImage, ColorTable.BackColorNormal, toolStripButton.BackgroundImageLayout, rectangle, clipRect);
				}
				if (toolStripButton.CheckState != 0)
				{
					Color baseColor = ControlPaint.Light(ColorTable.BackColorHover);
					if (toolStripButton.Selected)
					{
						baseColor = ColorTable.BackColorHover;
					}
					if (toolStripButton.Pressed)
					{
						baseColor = ColorTable.BackColorPressed;
					}
					RenderBackgroundInternal(e.Graphics, rectangle, baseColor, ColorTable.BorderColor, ColorTable.BackColorNormal, RoundStyle.All, drawBorder: true, drawGlass: true, mode);
					return;
				}
				if (toolStripButton.Selected)
				{
					Color baseColor2 = ColorTable.BackColorHover;
					if (toolStripButton.Pressed)
					{
						baseColor2 = ColorTable.BackColorPressed;
					}
					RenderBackgroundInternal(graphics, rectangle, baseColor2, ColorTable.BorderColor, ColorTable.BackColorNormal, RoundStyle.All, drawBorder: true, drawGlass: true, mode);
					return;
				}
				if (e.ToolStrip is ToolStripOverflow)
				{
					using (Brush brush = new SolidBrush(ColorTable.BackColorNormal))
					{
						graphics.FillRectangle(brush, rectangle);
						return;
					}
				}
			}
			base.OnRenderButtonBackground(e);
		}

		protected override void OnRenderSeparator(ToolStripSeparatorRenderEventArgs e)
		{
			Rectangle contentRectangle = e.Item.ContentRectangle;
			if (e.ToolStrip is ToolStripDropDown)
			{
				if (e.Item.RightToLeft != RightToLeft.Yes)
				{
					contentRectangle.X += OffsetMargin + 4;
				}
				contentRectangle.Width -= OffsetMargin + 8;
			}
			RenderSeparatorLine(e.Graphics, contentRectangle, ColorTable.BackColorPressed, ColorTable.BackColorNormal, SystemColors.ControlLightLight, e.Vertical);
		}

		protected override void OnRenderMenuItemBackground(ToolStripItemRenderEventArgs e)
		{
			if (e.Item.Enabled)
			{
				Graphics graphics = e.Graphics;
				Rectangle rect = new Rectangle(Point.Empty, e.Item.Size);
				graphics.SmoothingMode = SmoothingMode.AntiAlias;
				if (e.Item.RightToLeft == RightToLeft.Yes)
				{
					rect.X += 4;
				}
				else
				{
					rect.X += OffsetMargin + 4;
				}
				rect.Width -= OffsetMargin + 8;
				rect.Height--;
				if (e.Item.Selected)
				{
					RenderBackgroundInternal(graphics, rect, ColorTable.BackColorHover, ColorTable.BorderColor, ColorTable.BackColorNormal, RoundStyle.All, drawBorder: true, drawGlass: true, LinearGradientMode.Vertical);
				}
				else
				{
					base.OnRenderMenuItemBackground(e);
				}
			}
		}

		protected override void OnRenderImageMargin(ToolStripRenderEventArgs e)
		{
			if (e.ToolStrip is ToolStripDropDownMenu)
			{
				Rectangle affectedBounds = e.AffectedBounds;
				Graphics graphics = e.Graphics;
				affectedBounds.Width = OffsetMargin;
				if (e.ToolStrip.RightToLeft == RightToLeft.Yes)
				{
					affectedBounds.X -= 2;
				}
				else
				{
					affectedBounds.X += 2;
				}
				affectedBounds.Y++;
				affectedBounds.Height -= 2;
				graphics.SmoothingMode = SmoothingMode.AntiAlias;
				using (LinearGradientBrush linearGradientBrush = new LinearGradientBrush(affectedBounds, ColorTable.BackColorHover, Color.White, 90f))
				{
					Blend blend = new Blend();
					blend.Positions = new float[3] { 0f, 0.2f, 1f };
					blend.Factors = new float[3] { 0f, 0.1f, 0.9f };
					linearGradientBrush.Blend = blend;
					affectedBounds.Y++;
					affectedBounds.Height -= 2;
					using GraphicsPath path = GraphicsPathHelper.CreatePath(affectedBounds, 8, RoundStyle.All, correction: false);
					graphics.FillPath(linearGradientBrush, path);
				}
				graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
				StringFormat stringFormat = new StringFormat(StringFormatFlags.NoWrap);
				Font font = new Font(e.ToolStrip.Font.FontFamily, 11f, FontStyle.Bold);
				stringFormat.Alignment = StringAlignment.Near;
				stringFormat.LineAlignment = StringAlignment.Center;
				stringFormat.Trimming = StringTrimming.EllipsisCharacter;
				graphics.TranslateTransform(affectedBounds.X, affectedBounds.Bottom);
				graphics.RotateTransform(270f);
				if (!string.IsNullOrEmpty("csharpwin.com"))
				{
					Rectangle rectangle = new Rectangle(affectedBounds.X, affectedBounds.Y, affectedBounds.Height, affectedBounds.Width);
					using Brush brush = new SolidBrush(ColorTable.ForeColor);
					graphics.DrawString("csharpwin.com", font, brush, rectangle, stringFormat);
				}
				graphics.ResetTransform();
			}
			else
			{
				base.OnRenderImageMargin(e);
			}
		}

		protected override void OnRenderItemImage(ToolStripItemImageRenderEventArgs e)
		{
			e.Graphics.InterpolationMode = InterpolationMode.HighQualityBilinear;
			if (e.Item is ToolStripMenuItem)
			{
				if (!((ToolStripMenuItem)e.Item).Checked)
				{
					Rectangle imageRectangle = e.ImageRectangle;
					if (e.Item.RightToLeft == RightToLeft.Yes)
					{
						imageRectangle.X -= OffsetMargin + 2;
					}
					else
					{
						imageRectangle.X += OffsetMargin + 2;
					}
					ToolStripItemImageRenderEventArgs e2 = new ToolStripItemImageRenderEventArgs(e.Graphics, e.Item, e.Image, imageRectangle);
					base.OnRenderItemImage(e2);
				}
			}
			else
			{
				base.OnRenderItemImage(e);
			}
		}

		protected override void OnRenderItemText(ToolStripItemTextRenderEventArgs e)
		{
			e.TextColor = ColorTable.ForeColor;
			if (!(e.ToolStrip is MenuStrip) && e.Item is ToolStripMenuItem)
			{
				Rectangle textRectangle = e.TextRectangle;
				if (e.Item.RightToLeft == RightToLeft.Yes)
				{
					textRectangle.X -= 16;
				}
				else
				{
					textRectangle.X += 16;
				}
				e.TextRectangle = textRectangle;
			}
			base.OnRenderItemText(e);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			RenderBackgroundInternal(g, rect, baseColor, borderColor, innerBorderColor, style, 8, drawBorder, drawGlass, mode);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, int roundWidth, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			RenderBackgroundInternal(g, rect, baseColor, borderColor, innerBorderColor, style, 8, 0.45f, drawBorder, drawGlass, mode);
		}

		internal void RenderBackgroundInternal(Graphics g, Rectangle rect, Color baseColor, Color borderColor, Color innerBorderColor, RoundStyle style, int roundWidth, float basePosition, bool drawBorder, bool drawGlass, LinearGradientMode mode)
		{
			if (drawBorder)
			{
				rect.Width--;
				rect.Height--;
			}
			using LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rect, Color.Transparent, Color.Transparent, mode);
			Color[] colors = new Color[4]
			{
				GetColor(baseColor, 0, 35, 24, 9),
				GetColor(baseColor, 0, 13, 8, 3),
				baseColor,
				GetColor(baseColor, 0, 68, 69, 54)
			};
			ColorBlend colorBlend = new ColorBlend();
			colorBlend.Positions = new float[4]
			{
				0f,
				basePosition,
				basePosition + 0.05f,
				1f
			};
			colorBlend.Colors = colors;
			linearGradientBrush.InterpolationColors = colorBlend;
			if (style != 0)
			{
				using (GraphicsPath path = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
				{
					g.FillPath(linearGradientBrush, path);
				}
				if (baseColor.A > 80)
				{
					Rectangle rect2 = rect;
					if (mode == LinearGradientMode.Vertical)
					{
						rect2.Height = (int)((float)rect2.Height * basePosition);
					}
					else
					{
						rect2.Width = (int)((float)rect.Width * basePosition);
					}
					using GraphicsPath path2 = GraphicsPathHelper.CreatePath(rect2, roundWidth, RoundStyle.Top, correction: false);
					using SolidBrush brush = new SolidBrush(Color.FromArgb(80, 255, 255, 255));
					g.FillPath(brush, path2);
				}
				if (drawGlass)
				{
					RectangleF glassRect = rect;
					if (mode == LinearGradientMode.Vertical)
					{
						glassRect.Y = (float)rect.Y + (float)rect.Height * basePosition;
						glassRect.Height = ((float)rect.Height - (float)rect.Height * basePosition) * 2f;
					}
					else
					{
						glassRect.X = (float)rect.X + (float)rect.Width * basePosition;
						glassRect.Width = ((float)rect.Width - (float)rect.Width * basePosition) * 2f;
					}
					ControlPaintEx.DrawGlass(g, glassRect, 170, 0);
				}
				if (!drawBorder)
				{
					return;
				}
				using (GraphicsPath path3 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false))
				{
					using Pen pen = new Pen(borderColor);
					g.DrawPath(pen, path3);
				}
				rect.Inflate(-1, -1);
				using GraphicsPath path4 = GraphicsPathHelper.CreatePath(rect, roundWidth, style, correction: false);
				using Pen pen2 = new Pen(innerBorderColor);
				g.DrawPath(pen2, path4);
				return;
			}
			g.FillRectangle(linearGradientBrush, rect);
			if (baseColor.A > 80)
			{
				Rectangle rect3 = rect;
				if (mode == LinearGradientMode.Vertical)
				{
					rect3.Height = (int)((float)rect3.Height * basePosition);
				}
				else
				{
					rect3.Width = (int)((float)rect.Width * basePosition);
				}
				using SolidBrush brush2 = new SolidBrush(Color.FromArgb(80, 255, 255, 255));
				g.FillRectangle(brush2, rect3);
			}
			if (drawGlass)
			{
				RectangleF glassRect2 = rect;
				if (mode == LinearGradientMode.Vertical)
				{
					glassRect2.Y = (float)rect.Y + (float)rect.Height * basePosition;
					glassRect2.Height = ((float)rect.Height - (float)rect.Height * basePosition) * 2f;
				}
				else
				{
					glassRect2.X = (float)rect.X + (float)rect.Width * basePosition;
					glassRect2.Width = ((float)rect.Width - (float)rect.Width * basePosition) * 2f;
				}
				ControlPaintEx.DrawGlass(g, glassRect2, 200, 0);
			}
			if (drawBorder)
			{
				using (Pen pen3 = new Pen(borderColor))
				{
					g.DrawRectangle(pen3, rect);
				}
				rect.Inflate(-1, -1);
				using Pen pen4 = new Pen(innerBorderColor);
				g.DrawRectangle(pen4, rect);
				return;
			}
		}

		internal void RenderSeparatorLine(Graphics g, Rectangle rect, Color baseColor, Color backColor, Color shadowColor, bool vertical)
		{
			float angle = ((!vertical) ? 180f : 90f);
			using LinearGradientBrush linearGradientBrush = new LinearGradientBrush(rect, baseColor, backColor, angle);
			Blend blend = new Blend();
			blend.Positions = new float[5] { 0f, 0.3f, 0.5f, 0.7f, 1f };
			blend.Factors = new float[5] { 1f, 0.3f, 0f, 0.3f, 1f };
			linearGradientBrush.Blend = blend;
			using Pen pen = new Pen(linearGradientBrush);
			if (vertical)
			{
				g.DrawLine(pen, rect.X, rect.Y, rect.X, rect.Bottom);
			}
			else
			{
				g.DrawLine(pen, rect.X, rect.Y, rect.Right, rect.Y);
			}
			linearGradientBrush.LinearColors = new Color[2] { shadowColor, backColor };
			pen.Brush = linearGradientBrush;
			if (vertical)
			{
				g.DrawLine(pen, rect.X + 1, rect.Y, rect.X + 1, rect.Bottom);
			}
			else
			{
				g.DrawLine(pen, rect.X, rect.Y + 1, rect.Right, rect.Y + 1);
			}
		}

		private Color GetColor(Color colorBase, int a, int r, int g, int b)
		{
			int a2 = colorBase.A;
			int r2 = colorBase.R;
			int g2 = colorBase.G;
			int b2 = colorBase.B;
			a = ((a + a2 <= 255) ? Math.Max(0, a + a2) : 255);
			r = ((r + r2 <= 255) ? Math.Max(0, r + r2) : 255);
			g = ((g + g2 <= 255) ? Math.Max(0, g + g2) : 255);
			b = ((b + b2 <= 255) ? Math.Max(0, b + b2) : 255);
			return Color.FromArgb(a, r, g, b);
		}
	}
	public static class Wait
	{
		public static void Delay(uint milliSecond = 1000u)
		{
			if (milliSecond == 0)
			{
				milliSecond = 0u;
			}
			int tickCount = Environment.TickCount;
			while (Math.Abs(Environment.TickCount - tickCount) < milliSecond)
			{
				Application.DoEvents();
			}
		}
	}
}
namespace ScreenCapturer.Properties
{
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		private static ResourceManager resourceMan;

		private static CultureInfo resourceCulture;

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (resourceMan == null)
				{
					resourceMan = new ResourceManager("ScreenCapturer.Properties.Resources", typeof(Resources).Assembly);
				}
				return resourceMan;
			}
		}

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return resourceCulture;
			}
			set
			{
				resourceCulture = value;
			}
		}

		internal static Bitmap Accept => (Bitmap)ResourceManager.GetObject("Accept", resourceCulture);

		internal static Bitmap Arrow => (Bitmap)ResourceManager.GetObject("Arrow", resourceCulture);

		internal static Bitmap Brush => (Bitmap)ResourceManager.GetObject("Brush", resourceCulture);

		internal static Bitmap Circle => (Bitmap)ResourceManager.GetObject("Circle", resourceCulture);

		internal static Bitmap Exit => (Bitmap)ResourceManager.GetObject("Exit", resourceCulture);

		internal static Bitmap Finish => (Bitmap)ResourceManager.GetObject("Finish", resourceCulture);

		internal static Bitmap Line => (Bitmap)ResourceManager.GetObject("Line", resourceCulture);

		internal static Bitmap Rectangle => (Bitmap)ResourceManager.GetObject("Rectangle", resourceCulture);

		internal static Bitmap Redo => (Bitmap)ResourceManager.GetObject("Redo", resourceCulture);

		internal static Bitmap Save => (Bitmap)ResourceManager.GetObject("Save", resourceCulture);

		internal static Bitmap SinglePoint => (Bitmap)ResourceManager.GetObject("SinglePoint", resourceCulture);

		internal static Bitmap Text => (Bitmap)ResourceManager.GetObject("Text", resourceCulture);

		internal static Bitmap Undo => (Bitmap)ResourceManager.GetObject("Undo", resourceCulture);

		internal Resources()
		{
		}
	}
}
