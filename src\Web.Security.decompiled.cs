using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Text;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.IO;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Utilities.Encoders;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("Web.Security")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("Web.Security")]
[assembly: AssemblyCopyright("Copyright ©  2023")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("8e83679c-63c0-482b-85c6-461af8f0181d")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace Web.Security.Util;

public static class SM4Util
{
	private static Encoding DefaultEncoding = Encoding.UTF8;

	public const string ALGORITHM_NAME = "SM4";

	public const string ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";

	public const string ALGORITHM_NAME_CBC_PADDING = "SM4/CBC/PKCS5Padding";

	public static string DecryptEcb(string key, string passInput, Encoding encoding, string algorithmMode = "SM4/ECB/PKCS5Padding")
	{
		encoding = encoding ?? Encoding.UTF8;
		byte[] keyBytes = Hex.Decode(key);
		byte[] passInput2 = Hex.Decode(passInput);
		return encoding.GetString(DecryptEcb(keyBytes, passInput2, algorithmMode));
	}

	public static string DecryptEcb(string key, string passInput, string algorithmMode = "SM4/ECB/PKCS5Padding")
	{
		return DecryptEcb(key, passInput, DefaultEncoding, algorithmMode);
	}

	public static byte[] DecryptEcb(byte[] keyBytes, byte[] passInput, string algorithmMode)
	{
		KeyParameter parameters = ParameterUtilities.CreateKeyParameter("SM4", keyBytes);
		IBufferedCipher cipher = CipherUtilities.GetCipher(algorithmMode);
		cipher.Init(forEncryption: false, parameters);
		MemoryStream stream = new MemoryStream(passInput, writable: false);
		CipherStream input = new CipherStream(stream, cipher, null);
		byte[] array = new byte[passInput.Length];
		try
		{
			BinaryReader binaryReader = new BinaryReader(input);
			for (int i = 0; i != passInput.Length / 2; i++)
			{
				array[i] = binaryReader.ReadByte();
			}
			int num = array.Length - passInput.Length / 2;
			byte[] array2 = binaryReader.ReadBytes(num);
			if (array2.Length < num)
			{
				int num2 = passInput.Length / 2 + array2.Length;
				byte[] array3 = new byte[num2];
				Array.Copy(array, 0, array3, 0, passInput.Length / 2);
				array2.CopyTo(array3, passInput.Length / 2);
				return array3;
			}
			array2.CopyTo(array, passInput.Length / 2);
		}
		catch (Exception ex)
		{
			throw new Exception("SM4 failed encryption - " + ex, ex);
		}
		return array;
	}

	public static string EncryptEcb(string key, string text, Encoding encoding, string algorithmMode = "SM4/ECB/PKCS5Padding")
	{
		encoding = encoding ?? Encoding.UTF8;
		byte[] keyBytes = Hex.Decode(key);
		byte[] bytes = encoding.GetBytes(text);
		return Hex.ToHexString(EncryptEcb(keyBytes, bytes, algorithmMode));
	}

	public static string EncryptEcb(string key, string text, string algorithmMode = "SM4/ECB/PKCS5Padding")
	{
		return EncryptEcb(key, text, DefaultEncoding, algorithmMode);
	}

	public static byte[] EncryptEcb(byte[] keyBytes, byte[] input, string algorithmMode)
	{
		KeyParameter parameters = ParameterUtilities.CreateKeyParameter("SM4", keyBytes);
		IBufferedCipher cipher = CipherUtilities.GetCipher(algorithmMode);
		cipher.Init(forEncryption: true, parameters);
		MemoryStream memoryStream = new MemoryStream();
		CipherStream cipherStream = new CipherStream(memoryStream, null, cipher);
		try
		{
			for (int i = 0; i != input.Length / 2; i++)
			{
				cipherStream.WriteByte(input[i]);
			}
			cipherStream.Write(input, input.Length / 2, input.Length - input.Length / 2);
			cipherStream.Close();
		}
		catch (IOException ex)
		{
			throw new Exception("SM4 failed encryption - " + ex, ex);
		}
		return memoryStream.ToArray();
	}
}
