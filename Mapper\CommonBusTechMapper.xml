﻿<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	
	<!--展示上传列表-->
	<select  id="uploadFileList">
		select * from
		(select distinct OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO as mark_no,OS_ZB_PURCHASE_PROJECT_INFO.MARK_NAME as  mark_name,(case when BUSINESS_FILE_WAY='MARK'  then group_concat(distinct PACK_NAME) else PACK_NAME end)as pack_name,
		(case when BUSINESS_FILE_WAY='MARK' then MARK_NAME else MARK_NAME||'_'||PACK_NAME end) ||'_商务_{PublicVo.EntName}' as fileAliasName ,
		(case when BUSINESS_FILE_WAY='MARK' then MARK_NAME else MARK_NAME||'_'||PACK_NAME end) ||'_商务_{PublicVo.EntName}.sqlite' as file_orgin_name,'上传' as ext2,'未上传' as is_delete,PROJECT_NO as project_no,
		'sw' as file_type,(case when BUSINESS_FILE_WAY='MARK' then null else PACK_NAME end) absolute_packname
		from  OS_ZB_PURCHASE_PROJECT_INFO where SELECTED='1' and PROJECT_NO ='{projectNo}'
		group by case when BUSINESS_FILE_WAY='MARK'  then MARK_NO else MARK_NO||PACK_NAME end
		--order by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO,CAST(replace(OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME,'包','') AS INTEGER)
		)
		UNION
		select * from
		(select distinct OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO as mark_no,OS_ZB_PURCHASE_PROJECT_INFO.MARK_NAME as  mark_name,(case when SKILL_FILE_WAY='MARK'  then group_concat(distinct PACK_NAME) else PACK_NAME end)as pack_name,
		(case when SKILL_FILE_WAY='MARK' then MARK_NAME else MARK_NAME||'_'||PACK_NAME end) ||'_技术_{PublicVo.EntName}' as fileAliasName ,
		(case when SKILL_FILE_WAY='MARK' then MARK_NAME else MARK_NAME||'_'||PACK_NAME end) ||'_技术_{PublicVo.EntName}.sqlite' as file_orgin_name,'上传' as ext2,'未上传' as is_delete,PROJECT_NO as project_no ,
		'js' as file_type,(case when SKILL_FILE_WAY='MARK' then null else PACK_NAME end) absolute_packname
		from  OS_ZB_PURCHASE_PROJECT_INFO where SELECTED='1' and PROJECT_NO ='{projectNo}'
		group by case when SKILL_FILE_WAY='MARK'  then MARK_NO else MARK_NO||PACK_NAME end
		--order by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO,CAST(replace(OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME,'包','') AS INTEGER)
		)
	</select>

	<!--展示上传列表(针对资格预审)-->
	<select  id="uploadPreFileList">
		SELECT DISTINCT OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO AS mark_no,
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NAME AS mark_name,
		PACK_NAME  AS pack_name,
		MARK_NAME || '_' || PACK_NAME || '_{PublicVo.EntName}' AS fileAliasName,
		MARK_NAME || '_' || PACK_NAME || '_{PublicVo.EntName}.sqlite' AS file_orgin_name,
		'上传' AS ext2,
		'未上传' AS is_delete,
		PROJECT_NO AS project_no,
		'' AS file_type,
		(CASE WHEN BUSINESS_FILE_WAY = 'MARK' THEN NULL ELSE PACK_NAME END) absolute_packname
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		WHERE SELECTED = '1'  and PROJECT_NO ='{projectNo}'
		GROUP BY MARK_NO || PACK_NAME
	</select>


	<!--校验相关商务标段上传信息-->
	<select  id="checkBusinessFileExport">
		---商务专项投标文件
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BUSINESS_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) || ' 商务专项投标文件未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE = '1' AND  OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE = 'business'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.ID IS NULL <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		---商务文件压缩包
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BUSINESS_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 商务投标压缩文件未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		SYS_FILE_INFO.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		---保证金信息
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BZJ_WD = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 保证金信息未填写') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL
		on  OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		<if test="packName!=null" value=" AND OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME "></if>
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_DEPOSIT='是'  AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID is null  <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BZJ_WD = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end;

		---保证金信息附件信息
		select distinct '保证金附件未上传，请检查' AS WARN_INO from  OS_ZB_SUPPLIER_DEPOSIT
		left join SYS_FILE_INFO on OS_ZB_SUPPLIER_DEPOSIT.ID =SYS_FILE_INFO.RELATED_ID
		where (select count(0) from OS_ZB_PURCHASE_PROJECT_INFO where NEED_DEPOSIT in ('是','YES') AND PURCHASE_TYPE='物资招标')&gt;0  and SYS_FILE_INFO.ID is NULL
		AND (select count(0) from OS_ZB_SUPPLIER_DEPOSIT_DETAIL where MARK_NO = '{markNo}' AND PAY_MODE!='无')&gt;0;

		---物资招标的投标授权人
		select distinct "投标授权人中授权委托书未上传" AS WARN_INO
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO LEFT JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID
		where (select count(0) from OS_ZB_PURCHASE_PROJECT_INFO where NEED_AUTH_PEOPEL in ('是','YES') AND PURCHASE_TYPE='物资招标')&gt;0
		AND SYS_FILE_INFO.ID IS NULL  AND OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID is null;

		-- 物资类商务响应情况信息（废弃 updateDate 20250228）
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BUSINESS_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 商务响应信息未填写') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_RESPONSE_INFO_BUS
		on  OS_ZB_SUPPLIER_RESPONSE_INFO_BUS.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND (case when BUSINESS_FILE_WAY = 'PACK'  then OS_ZB_SUPPLIER_RESPONSE_INFO_BUS.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME else true end)
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE='是' AND PURCHASE_TYPE='物资招标' AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_SUPPLIER_RESPONSE_INFO_BUS.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		-- 服务类商务响应情况信息
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BUSINESS_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 商务响应信息未填写') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SBUSINESS_RESPONSE_INFO
		on  OS_ZB_SBUSINESS_RESPONSE_INFO.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND (case when BUSINESS_FILE_WAY = 'PACK'  then OS_ZB_SBUSINESS_RESPONSE_INFO.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME else true end)
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE='是' AND PURCHASE_TYPE='服务招标' AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_SBUSINESS_RESPONSE_INFO.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;
	</select>


	<!--校验相关技术标段上传信息-->
	<select  id="checkTechFileExport">
		---技术专项投标文件
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) || ' 技术专项投标文件未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE = '1' AND  OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE = 'skill'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.ID IS NULL <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		---技术文件压缩包
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 技术投标压缩文件未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND SYS_FILE_INFO.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		--价格投标文件压缩包
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 价格投标文件压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND SYS_FILE_INFO.ID is null and PURCHASE_TYPE='服务招标' <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		--上架商品
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND SYS_FILE_INFO.ID is null and NEED_ON_GOODS in('是','YES','Y') <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		-- 生产成本参数特性表
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 技术专项投标文件缺少生产成本参数特性表') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS
		on  OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND (case when SKILL_FILE_WAY = 'PACK'  then OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME else true end)
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS='是'  AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		-- 物资类技术响应情况信息（废弃 updateDate 20250228）
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 技术响应信息未填写') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_RESPONSE_INFO_TECH
		on  OS_ZB_SUPPLIER_RESPONSE_INFO_TECH.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND (case when SKILL_FILE_WAY = 'PACK'  then OS_ZB_SUPPLIER_RESPONSE_INFO_TECH.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME else true end)
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE='是'  AND PURCHASE_TYPE='物资招标' AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_SUPPLIER_RESPONSE_INFO_TECH.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;

		-- 服务类技术响应情况信息
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when SKILL_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 技术响应信息未填写完全') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_STECH_RESPONSE_INFO
		on  OS_ZB_STECH_RESPONSE_INFO.MARK_NO = OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO
		AND (case when SKILL_FILE_WAY = 'PACK'  then OS_ZB_STECH_RESPONSE_INFO.PACK_NAME =OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AND  OS_ZB_STECH_RESPONSE_INFO.SUB_PACK = OS_ZB_PURCHASE_PROJECT_INFO.SUB_PACK else true end)
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_RESPONSE='是' AND PURCHASE_TYPE='服务招标' AND OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND OS_ZB_STECH_RESPONSE_INFO.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;
	</select>



	<!--校验资格预审批次相关文件上传信息-->
	<select  id="checkPreQualificationFileExport">
		--资格预审压缩包
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||(case when BUSINESS_FILE_WAY = 'PACK' then ','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  else '' end) ||' 资格预审压缩文件未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='ZgysZip'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.SELECTED = '1' AND
		SYS_FILE_INFO.ID is null <if test="packName!=null" value=" AND OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME = '{packName}' "></if>
		GROUP BY case when OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY = 'MARK'  then  OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO else OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  end ;		
	</select>
	
</mapper>