<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--
		附件信息+  保证金主表信息
	-->
	<select id="insertAttachInfoTemp">
		--保证金主表信息
		insert into db1.OS_ZB_SUPPLIER_BID_ATTACHMENT SELECT * FROM OS_ZB_SUPPLIER_BID_ATTACHMENT WHERE MARK_NO ='{markNo}';
		--保证金主表信息
		insert into db1.OS_ZB_SUPPLIER_DEPOSIT SELECT * FROM OS_ZB_SUPPLIER_DEPOSIT WHERE PROJECT_NO ='{projectNo}';
		--保证金详情
		insert into db1.OS_ZB_SUPPLIER_DEPOSIT_DETAIL SELECT * FROM OS_ZB_SUPPLIER_DEPOSIT_DETAIL WHERE MARK_NO ='{markNo}';
		--授权人信息
		insert into db1.OS_ZB_SUPPLIER_AUTH_PERSON_INFO SELECT * FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO;
		--授权人关联信息
		insert into db1.OS_ZB_AUTH_PERSON_RELATE SELECT * FROM OS_ZB_AUTH_PERSON_RELATE;
		--字典信息
		insert into db1.CM_C_DICTIONARY_DETAILS SELECT * FROM CM_C_DICTIONARY_DETAILS;
		--响应情况信息
		insert into db1.OS_ZB_SUPPLIER_RESPONSE_INFO SELECT * FROM OS_ZB_SUPPLIER_RESPONSE_INFO WHERE MARK_NO ='{markNo}';

		--更新供应商id信息
		--标书信息
		update db1.OS_ZB_SUPPLIER_BID_ATTACHMENT SET SUPPLIER_ID ='{supplierId}';
		--保证金
		update db1.OS_ZB_SUPPLIER_DEPOSIT SET SUPPLIER_ID ='{supplierId}';
		--保证金详情
		update db1.OS_ZB_SUPPLIER_DEPOSIT_DETAIL SET SUPPLIER_ID ='{supplierId}';
		--授权人信息
		update db1.OS_ZB_SUPPLIER_AUTH_PERSON_INFO SET SUPPLIER_ID ='{supplierId}';
		--商务、技术响应信息
		update db1.OS_ZB_SUPPLIER_RESPONSE_INFO SET SUPPLIER_ID ='{supplierId}';
		--股权结构信息
		update db1.OS_ZB_SUPPLIER_BIAO_EQUITY_INFO SET SUPPLIER_ID ='{supplierId}';
		--试验报告
		update db1.OS_ZB_SUPPLIER_BIAO_REPORT_INFO SET SUPPLIER_ID ='{supplierId}';
		--产品信息
		update db1.OS_ZB_SUPPLIER_PRODUCT_INFO SET SUPPLIER_ID ='{supplierId}';
		--产品部件
		update db1.OS_ZB_SUPPLIER_PARTS_INFO SET SUPPLIER_ID ='{supplierId}';
		--供应商信息
		update db1.OS_ZB_SUPPLIER_INFO SET ID ='{supplierId}';
		--项目信息
		update db1.OS_ZB_PURCHASE_PROJECT_INFO SET SUPPLIER_ID ='{supplierId}';
		--认证证书
		update db1.OS_ZB_SUPPLIER_BIAO_CERT_INFO SET SUPPLIER_ID ='{supplierId}';
		--销售信息
		update db1.OS_ZB_SUPPLIER_BIAO_SALES_INFO SET SUPPLIER_ID ='{supplierId}';
		--财务信息
		update db1.OS_ZB_SUPPLIER_BIAO_FINANCE_INFO SET SUPPLIER_ID ='{supplierId}';
		--试验设备
		update db1.OS_ZB_SUPPLIER_BIAO_LABEQP_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产装备
		update db1.OS_ZB_SUPPLIER_BIAO_MEQP_INFO SET SUPPLIER_ID ='{supplierId}';
		--项目信息
		update db1.OS_ZB_PURCHASE_PROJECT_INFO SET SUPPLIER_ID ='{supplierId}';
		--资质业绩核实
		update db1.OS_ZB_SUPPLIER_QUALIFY_INFO SET SUPPLIER_ID ='{supplierId}';
		--信息信用
		update db1.OS_ZB_SUPPLIER_BIAO_CREDIT_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产能力
		update db1.OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE SET SUPPLIER_ID ='{supplierId}';
		--运行评价
		update db1.OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE SET SUPPLIER_ID ='{supplierId}';

		--基础数据更新供应商id
		--财务数据
		update db1.OS_ZB_SUPPLIER_FINANCE_INFO SET SUPPLIER_ID ='{supplierId}';
		--信息信用
		update db1.OS_ZB_SUPPLIER_CREDIT_INFO SET SUPPLIER_ID ='{supplierId}';
		--股权结构
		update db1.OS_ZB_SUPPLIER_EQUITY_INFO SET SUPPLIER_ID ='{supplierId}';
		--生产产地
		update db1.OS_ZB_SUPPLIER_PRODUCT_PLACE SET SUPPLIER_ID ='{supplierId}';

		--更新供应商id信息
		--附件信息
		insert into db1.SYS_FILE_INFO
		--pdf文件/word文件(商务+技术的支持文件)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		INNER JOIN
		OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.ID = SYS_FILE_INFO.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BID_ATTACHMENT.PROJECT_NO = '{projectNo}' AND
		OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = '{markNo}'
		union
		--ecp压缩文件+价格压缩文件+上架商品压缩包
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '{projectNo}&amp;&amp;{markNo}&amp;&amp;%'
		union
		--保证金明细附件(按项目)
		SELECT SYS_FILE_INFO.*
		FROM SYS_FILE_INFO
		where RELATED_PAGE ='OS_ZB_SUPPLIER_DEPOSIT_ATTACH' and RELATED_ID like '{projectNo}'
		union
		--授权人信息
		select SYS_FILE_INFO.*
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO inner JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID
		union
		--保证金附件部分
		select  SYS_FILE_INFO.*  from SYS_FILE_INFO
		inner join OS_ZB_SUPPLIER_DEPOSIT_DETAIL on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID =SYS_FILE_INFO.RELATED_ID
		where SYS_FILE_INFO.RELATED_KEY in('accountBaseInfoAttach','purchaseAccountInfoAttach','bidDetailsInfoAttach') and OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO='{markNo}'

		union
		--发票图片
		select distinct SYS_FILE_INFO.*  from SYS_FILE_INFO
		inner join OS_ZB_BILL_INFO on SYS_FILE_INFO.RELATED_ID = iif(OS_ZB_BILL_INFO.BILL_CODE is not null,OS_ZB_BILL_INFO.BILL_CODE,'') ||'_'||OS_ZB_BILL_INFO.BILL_NUMBER
		inner join OS_ZB_SUPPLIER_BIAO_SALES_INFO on OS_ZB_SUPPLIER_BIAO_SALES_INFO.ID =OS_ZB_BILL_INFO.PARENT_ID
		where OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO='{markNo}';
	</select>


	<select id="selectRelateInfo">
		SELECT {mainTable}.*,
		'已上传数('||(select count(id) from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID = {mainTable}.ID and SYS_FILE_INFO.RELATED_KEY = '{relatedKey1}')||')' as file1CountStr, --附件1个数
		'已上传数('||(select count(id) from SYS_FILE_INFO where SYS_FILE_INFO.RELATED_ID = {mainTable}.ID and SYS_FILE_INFO.RELATED_KEY = '{relatedKey2}')||')' as file2CountStr --附件2个数
		FROM   {mainTable} where {mainTable}.PROJECT_NO ='{projectNo}' and {mainTable}.MARK_NO ='{markNo}'
		-- and ({mainTable}.PACK_NAME ='{packName}' or {mainTable}.PACK_NAME is null)
	</select>


	<!--技术模块查询-->
	<select id="selectTechModel">
		--业绩文件
		SELECT OS_ZB_SUPPLIER_BIAO_SALES_INFO.ID,OS_ZB_SUPPLIER_BIAO_SALES_INFO.PROJECT_NO,OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO, OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME,OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.PRODUCT_NAME,OS_ZB_SUPPLIER_BIAO_SALES_INFO.UNIT,OS_ZB_SUPPLIER_BIAO_SALES_INFO.SALE_COUNT,OS_ZB_SUPPLIER_BIAO_SALES_INFO.NAME_BUYER,OS_ZB_SUPPLIER_BIAO_SALES_INFO.TEL_CONTACT,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.CONTACT_INFO,OS_ZB_SUPPLIER_BIAO_SALES_INFO.REMARK,OS_ZB_SUPPLIER_BIAO_SALES_INFO.TYPE,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.SALE_MONEY, OS_ZB_SUPPLIER_BIAO_SALES_INFO.SUB_PACK,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.PRODUCT_MODEL, OS_ZB_SUPPLIER_BIAO_SALES_INFO.ENGINEERING_NAME,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.COMMISSIONING_DATE,OS_ZB_SUPPLIER_BIAO_SALES_INFO.CAL_WAY
		,(case  when CAL_WAY='销售数量' then SALE_COUNT||UNIT else  SALE_MONEY||'万元' end) saleInfo,
		contract_file.ID as  _file_id,
		contract_file.FILE_FORMAT as _file_format,
		contract_file.FILE_DISP_NAME as _file_disp_name,
		contract_file.FILE_PATH as _file_path,
		bill_file.ID as _file_id1,
		bill_file.FILE_FORMAT as _file_format1,
		bill_file.FILE_DISP_NAME as _file_disp_name1,
		bill_file.FILE_PATH as _file_path1,
		bill_file.RELATED_KEY as _file_key1,
		bill_file.RELATED_ID as _file_related_id1,
		OS_ZB_SUPPLIER_BIAO_SALES_INFO.RELATE_ID
		FROM OS_ZB_SUPPLIER_BIAO_SALES_INFO
		left join sys_file_info contract_file on contract_file.RELATED_KEY='contract' and OS_ZB_SUPPLIER_BIAO_SALES_INFO.RELATE_ID=contract_file.RELATED_ID
		left join  (
		select sys_file_info.ID,FILE_FORMAT,FILE_DISP_NAME,FILE_PATH,SORT_VALUE,RELATED_KEY,RELATED_ID,sys_file_info.UPLOAD_TIME,OS_ZB_BILL_INFO.PARENT_ID from OS_ZB_BILL_INFO
		inner join sys_file_info  on sys_file_info.RELATED_ID = iif(OS_ZB_BILL_INFO.BILL_CODE is not null,OS_ZB_BILL_INFO.BILL_CODE,'')||'_'||OS_ZB_BILL_INFO.BILL_NUMBER and RELATED_KEY in ('bill','result')  group by sys_file_info.ID,OS_ZB_BILL_INFO.PARENT_ID  ORDER BY OS_ZB_BILL_INFO.CREATE_TIME
		) as bill_file on  OS_ZB_SUPPLIER_BIAO_SALES_INFO.RELATE_ID = bill_file.PARENT_ID
		where OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_SALES_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_SALES_INFO.CREATE_TIME ASC,contract_file.SORT_VALUE,bill_file.UPLOAD_TIME;

		--生产能力
		select OS_ZB_SUPPLIER_PRODUCT_PLACE.*,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.PROJECT_NO,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.MARK_NO,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.PACK_NAME,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE
		inner join OS_ZB_SUPPLIER_PRODUCT_PLACE  on  OS_ZB_SUPPLIER_PRODUCT_PLACE.ID = OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.RELATE_ID
		left join sys_file_info  on sys_file_info.RELATED_KEY='zmcl' and OS_ZB_SUPPLIER_PRODUCT_PLACE.ID = sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME)&lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE.CREATE_TIME desc,sys_file_info.SORT_VALUE;

		--生产装备
		select OS_ZB_SUPPLIER_BIAO_MEQP_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_MEQP_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='zhizaofj' and OS_ZB_SUPPLIER_BIAO_MEQP_INFO.RELATE_ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_MEQP_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_MEQP_INFO.SUPPLIER_ID ='{supplierId}'      and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_MEQP_INFO.CREATE_TIME desc;

		--试验设备
		select OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_LABEQP_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='shebeifj' and OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.RELATE_ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.SUPPLIER_ID ='{supplierId}' and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_LABEQP_INFO.CREATE_TIME desc,sys_file_info.SORT_VALUE;

		--检测检验报告(试验报告)
		select OS_ZB_SUPPLIER_BIAO_REPORT_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_REPORT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='sybgImg' and OS_ZB_SUPPLIER_BIAO_REPORT_INFO.RELATE_ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_REPORT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_REPORT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_REPORT_INFO.CREATE_TIME desc,sys_file_info.SORT_VALUE;

		--制造工艺
		select OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.ID,OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.PROCESS_NAME,OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.ACTION_KEY,
		OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.SCOPE,OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.REMARK
		FROM OS_ZB_SUPPLIER_BIAO_PROCESS_INFO
		where OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_PROCESS_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}');

		--认证证书
		select OS_ZB_SUPPLIER_BIAO_CERT_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_BIAO_CERT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='zzzsImg'
		and OS_ZB_SUPPLIER_BIAO_CERT_INFO.RELATE_ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_BIAO_CERT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_BIAO_CERT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_BIAO_CERT_INFO.CREATE_TIME desc,sys_file_info.SORT_VALUE;

		--主要技术人员
		select OS_ZB_BIAO_CORE_STAFF.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_BIAO_CORE_STAFF
		left join sys_file_info  on sys_file_info.RELATED_KEY='attach'
		and OS_ZB_BIAO_CORE_STAFF.RELATE_ID= sys_file_info.RELATED_ID
		where OS_ZB_BIAO_CORE_STAFF.MARK_NO ='{markNo}'  and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_BIAO_CORE_STAFF.CREATE_TIME desc,sys_file_info.SORT_VALUE;


		--产品信息
		select OS_ZB_SUPPLIER_PRODUCT_INFO.*,(case when AGENT ='NO' or AGENT ='否'  then '否' else '是' end) as agent_name,
		--集成证明文件
		sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path ,
		--产品外观
		sys_file_2.ID as  _file_id1,
		sys_file_2.FILE_FORMAT as _file_format1,
		sys_file_2.FILE_DISP_NAME as _file_disp_name1,
		sys_file_2.FILE_PATH as _file_path1,
		--主图
		sys_file_3.ID AS _file_id2,
		sys_file_3.FILE_FORMAT AS _file_format2,
		sys_file_3.FILE_DISP_NAME AS _file_disp_name2,
		sys_file_3.FILE_PATH AS _file_path2,
		--副图
		sys_file_4.ID AS _file_id3,
		sys_file_4.FILE_FORMAT AS _file_format3,
		sys_file_4.FILE_DISP_NAME AS _file_disp_name3,
		sys_file_4.FILE_PATH AS _file_path3

		from OS_ZB_SUPPLIER_PRODUCT_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='agentFj' and OS_ZB_SUPPLIER_PRODUCT_INFO.ID=sys_file_info.RELATED_ID
		left join sys_file_info sys_file_2  on sys_file_2.RELATED_KEY='cpwg' and OS_ZB_SUPPLIER_PRODUCT_INFO.ID=sys_file_2.RELATED_ID
		LEFT JOIN sys_file_info sys_file_3 ON  sys_file_3.RELATED_KEY = 'main_image' AND OS_ZB_SUPPLIER_PRODUCT_INFO.ID = sys_file_3.RELATED_ID
		LEFT JOIN sys_file_info sys_file_4 ON sys_file_4.RELATED_KEY = 'other_image' AND OS_ZB_SUPPLIER_PRODUCT_INFO.ID = sys_file_4.RELATED_ID
		where OS_ZB_SUPPLIER_PRODUCT_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_PRODUCT_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by sys_file_info.FILE_DISP_NAME asc;

		--部件信息(无附件)
		select OS_ZB_SUPPLIER_PARTS_INFO.*,sys_file_info.ID as  _file_id,
		sys_file_info.FILE_FORMAT as _file_format,
		sys_file_info.FILE_DISP_NAME as _file_disp_name,
		sys_file_info.FILE_PATH as _file_path   from OS_ZB_SUPPLIER_PARTS_INFO
		left join sys_file_info  on sys_file_info.RELATED_KEY='fujian' and OS_ZB_SUPPLIER_PARTS_INFO.ID=sys_file_info.RELATED_ID
		where OS_ZB_SUPPLIER_PARTS_INFO.MARK_NO ='{markNo}' and OS_ZB_SUPPLIER_PARTS_INFO.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		order by OS_ZB_SUPPLIER_PARTS_INFO.CREATE_TIME desc;

		--资格预审
		select OS_ZB_BIAO_PRE_QUALIFICATION.ID, ISSUING_UNIT,PRE_Q_BATCH,PRE_Q_RESULT_DATE,PRE_Q_MARK_NAME,PRE_Q_RESULT_OK,PRE_Q_RESULT_END_DATE,TYPE,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_BIAO_PRE_QUALIFICATION
		left join sys_file_info  on sys_file_info.RELATED_KEY='tag_notice_inform_files' and OS_ZB_BIAO_PRE_QUALIFICATION.RELATE_ID = sys_file_info.RELATED_ID
		where OS_ZB_BIAO_PRE_QUALIFICATION.MARK_NO ='{markNo}' and OS_ZB_BIAO_PRE_QUALIFICATION.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		and TYPE='jishu'  order by OS_ZB_BIAO_PRE_QUALIFICATION.CREATE_TIME DESC,sys_file_info.SORT_VALUE;

		--业绩中的发票信息
		SELECT OS_ZB_BILL_INFO.ID,OS_ZB_BILL_INFO.BILL_CODE,OS_ZB_BILL_INFO.BILL_NUMBER,OS_ZB_BILL_INFO.MARK_NO,OS_ZB_BILL_INFO.PARENT_ID,
		file_table1.ID AS _file_id,file_table1.FILE_FORMAT AS _file_format,
		file_table1.FILE_DISP_NAME AS _file_disp_name,file_table1.FILE_PATH AS _file_path
		,file_table2.ID AS _file_id,file_table2.FILE_FORMAT AS _file_format,
		file_table2.FILE_DISP_NAME AS _file_disp_name,file_table2.FILE_PATH AS _file_path
		FROM OS_ZB_BILL_INFO
		INNER JOIN OS_ZB_SUPPLIER_SALES_INFO ON OS_ZB_SUPPLIER_SALES_INFO.ID = OS_ZB_BILL_INFO.PARENT_ID
		INNER JOIN OS_ZB_SUPPLIER_BIAO_SALES_INFO ON OS_ZB_SUPPLIER_BIAO_SALES_INFO.RELATE_ID = OS_ZB_SUPPLIER_SALES_INFO.ID
		--发票
		LEFT JOIN SYS_FILE_INFO file_table1 ON file_table1.RELATED_ID = iif(OS_ZB_BILL_INFO.BILL_CODE is not null,OS_ZB_BILL_INFO.BILL_CODE,'') || '_' || BILL_NUMBER AND file_table1.RELATED_KEY='bill'
		--发票结果截图
		LEFT JOIN SYS_FILE_INFO file_table2 ON file_table2.RELATED_ID = iif(OS_ZB_BILL_INFO.BILL_CODE is not null,OS_ZB_BILL_INFO.BILL_CODE,'')  || '_' || BILL_NUMBER AND file_table2.RELATED_KEY='result'
		where OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO = '{markNo}'
		order by OS_ZB_SUPPLIER_BIAO_SALES_INFO.DATE_ASSIGN,file_table1.SORT_VALUE,file_table2.SORT_VALUE;


		--企业变更证明材料
		SELECT OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.*,
		file_t1.ID AS _file_id,
		file_t1.FILE_FORMAT AS _file_format,
		file_t1.RELATED_KEY AS _file_key,
		file_t1.FILE_DISP_NAME AS _file_disp_name,
		file_t1.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_CHANGE_FILE
		LEFT JOIN
		SYS_FILE_INFO file_t1 ON file_t1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.RELATE_ID AND
		file_t1.RELATED_KEY = 'attach' AND file_t1.RELATED_PAGE = 'OS_ZB_SUPPLIER_CHANGE_FILE'
		where OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.SUPPLIER_ID ='{supplierId}'
		<if test="packName!=null" value=" AND PACK_NAME='{packName}' "> </if> AND OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.TYPE='jishu'
		group by file_t1.ID order by file_t1.SORT_VALUE;

		--关键设备
		SELECT OS_ZB_SUPPLIER_BIAO_CRUX_EQUIP.*
		FROM OS_ZB_SUPPLIER_BIAO_CRUX_EQUIP
		where OS_ZB_SUPPLIER_BIAO_CRUX_EQUIP.MARK_NO = '{markNo}' and OS_ZB_SUPPLIER_BIAO_CRUX_EQUIP.SUPPLIER_ID ='{supplierId}'
		<if test="packName!=null" value=" AND PACK_NAME='{packName}' "> </if>;
		
	</select>


	<!--商务模块查询-->
	<select id="selectBusinessModel">
		--股权信息(高管信息不生成)
		SELECT OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_EQUITY_INFO
		LEFT JOIN
		sys_file_info ON sys_file_info.RELATED_KEY in('gdyyzzFj','idcard') AND
		OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.MARK_NO = '{markNo}' and OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		and OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.PEOPEL_TYPE !='gaoguan'
		ORDER BY OS_ZB_SUPPLIER_BIAO_EQUITY_INFO.CREATE_TIME DESC,sys_file_info.SORT_VALUE;

		--股权信息证明文件
		SELECT
		sys_file_info.ID,
		sys_file_info.FILE_FORMAT,
		sys_file_info.FILE_DISP_NAME,
		sys_file_info.FILE_PATH
		FROM sys_file_info
		WHERE sys_file_info.RELATED_KEY = 'gqjgFj' AND sys_file_info.RELATED_ID like  '%{markNo}{packName}%'
		ORDER BY sys_file_info.SORT_VALUE;

		--财务信息
		SELECT OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.*,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_FINANCE_INFO
		LEFT JOIN
		sys_file_info ON  sys_file_info.RELATED_KEY = 'cwsjbg' and
		OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.RELATE_ID = sys_file_info.RELATED_ID
		WHERE OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		--AND OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.YEAR_SJ in({years})
		ORDER BY OS_ZB_SUPPLIER_BIAO_FINANCE_INFO.YEAR_SJ asc,sys_file_info.SORT_VALUE;

		--信用信息
		SELECT  FILE_DISP_NAME, FILE_PATH, FILE_FORMAT,ATTACH_TYPE
		FROM OS_ZB_SUPPLIER_BIAO_CREDIT_INFO
		WHERE OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.SUPPLIER_ID ='{supplierId}'    and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		-- updateTime 2022-12-13  企业资质等级证书原件影印件、投标人概况表与信用信息 共用一张表 （qyxycxbg=>企业信用查询报告  xyzghmd=》信用中国）
		and  OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.ATTACH_TYPE in ('country_public_credit_info','xyzghmd')
		ORDER BY OS_ZB_SUPPLIER_BIAO_CREDIT_INFO.UPDATE_TIME DESC;

		--供应商基本信息
		SELECT OS_ZB_SUPPLIER_INFO.*,
		file_t1.ID AS _file_id,
		file_t1.FILE_FORMAT AS _file_format,
		file_t1.FILE_DISP_NAME AS _file_disp_name,
		file_t1.FILE_PATH AS _file_path,
		file_t2.ID AS _file_id1,
		file_t2.FILE_FORMAT AS _file_format1,
		file_t2.FILE_DISP_NAME AS _file_disp_name1,
		file_t2.FILE_PATH AS _file_path1,
		file_t3.ID AS _file_id2,
		file_t3.FILE_FORMAT AS _file_format2,
		file_t3.FILE_DISP_NAME AS _file_disp_name2,
		file_t3.FILE_PATH AS _file_path2,
		file_njzm.ID AS _file_idNjZm,
		file_njzm.FILE_FORMAT AS _file_formatNjZm,
		file_njzm.FILE_DISP_NAME AS _file_disp_nameNjZm,
		file_njzm.FILE_PATH AS _file_pathNjZm
		FROM OS_ZB_SUPPLIER_INFO
		LEFT JOIN
		--szhyyyzz 营业执照
		sys_file_info file_t1 ON file_t1.RELATED_KEY='szhyyyzz' and OS_ZB_SUPPLIER_INFO.ID=file_t1.RELATED_ID
		--orgjg  组织结构
		LEFT JOIN sys_file_info file_t2 ON file_t2.RELATED_KEY='orgjg' and OS_ZB_SUPPLIER_INFO.ID=file_t2.RELATED_ID
		--yxswdj 有效税务登记证明
		LEFT JOIN sys_file_info file_t3 ON file_t3.RELATED_KEY='yxswdj' and OS_ZB_SUPPLIER_INFO.ID=file_t3.RELATED_ID
		--njzm 年检证明
		LEFT JOIN sys_file_info file_njzm ON file_njzm.RELATED_KEY='njzm' and OS_ZB_SUPPLIER_INFO.ID=file_njzm.RELATED_ID
		WHERE OS_ZB_SUPPLIER_INFO.ID ='{supplierId}'  order by file_t1.SORT_VALUE,file_t2.SORT_VALUE,file_t3.SORT_VALUE;


		--资格预审
		select OS_ZB_BIAO_PRE_QUALIFICATION.ID, ISSUING_UNIT,PRE_Q_BATCH,PRE_Q_RESULT_DATE,PRE_Q_MARK_NAME,PRE_Q_RESULT_OK,PRE_Q_RESULT_END_DATE,TYPE,
		sys_file_info.ID AS _file_id,
		sys_file_info.FILE_FORMAT AS _file_format,
		sys_file_info.FILE_DISP_NAME AS _file_disp_name,
		sys_file_info.FILE_PATH AS _file_path
		FROM OS_ZB_BIAO_PRE_QUALIFICATION
		left join sys_file_info  on sys_file_info.RELATED_KEY='tag_notice_inform_files' and OS_ZB_BIAO_PRE_QUALIFICATION.RELATE_ID = sys_file_info.RELATED_ID
		where OS_ZB_BIAO_PRE_QUALIFICATION.MARK_NO ='{markNo}' and OS_ZB_BIAO_PRE_QUALIFICATION.SUPPLIER_ID ='{supplierId}'   and (LENGTH(PACK_NAME) &lt;=0 or PACK_NAME='{packName}')
		and TYPE='shangwu'  order by OS_ZB_BIAO_PRE_QUALIFICATION.CREATE_TIME DESC,sys_file_info.SORT_VALUE;


		--保证金详情
		SELECT
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ID,
		OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NO,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.DEPOSIT_MONEY,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.DEPOSIT_INSURE,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.TOTAL_MONEY,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PAY_TIME,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.REMARK,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.VOUCHER_CATEGORY,
		case when OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NAME is null or length(OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NAME)&lt;=0 then '/' else OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PACK_NAME end PACK_NAME,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.LSHBDH,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PAY_MODE,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.SUPPLIER_ID,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ACCOUNT_BASE_INFO,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PURCHASE_ACCOUNT_INFO,
		OS_ZB_SUPPLIER_DEPOSIT_DETAIL.ISSUING_BANK,
		ROUND(CAST(IFNULL(OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PAID_OUT_AMOUNT,0) AS REAL)/10000,6) PAID_OUT_AMOUNT
		FROM OS_ZB_SUPPLIER_DEPOSIT_DETAIL
		inner join OS_ZB_SUPPLIER_DEPOSIT on OS_ZB_SUPPLIER_DEPOSIT_DETAIL.PARENT_ID =OS_ZB_SUPPLIER_DEPOSIT.ID
		WHERE OS_ZB_SUPPLIER_DEPOSIT_DETAIL.MARK_NO = '{markNo}';

		--保证金主表数据及附件信息
		SELECT OS_ZB_SUPPLIER_DEPOSIT.*,
		file_t1.ID AS _file_id,
		file_t1.FILE_FORMAT AS _file_format,
		file_t1.RELATED_KEY AS _file_key,
		file_t1.FILE_DISP_NAME AS _file_disp_name,
		file_t1.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_DEPOSIT
		LEFT JOIN
		SYS_FILE_INFO file_t1 ON file_t1.RELATED_ID = OS_ZB_SUPPLIER_DEPOSIT.ID AND
		file_t1.RELATED_KEY IN ('bidDetailsInfoAttach', 'accountBaseInfoAttach', 'purchaseAccountInfoAttach')
		group by file_t1.ID order by file_t1.SORT_VALUE;

		--企业变更证明材料
		SELECT OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.*,
		file_t1.ID AS _file_id,
		file_t1.FILE_FORMAT AS _file_format,
		file_t1.RELATED_KEY AS _file_key,
		file_t1.FILE_DISP_NAME AS _file_disp_name,
		file_t1.FILE_PATH AS _file_path
		FROM OS_ZB_SUPPLIER_BIAO_CHANGE_FILE
		LEFT JOIN
		SYS_FILE_INFO file_t1 ON file_t1.RELATED_ID = OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.RELATE_ID AND
		file_t1.RELATED_KEY = 'attach' AND file_t1.RELATED_PAGE = 'OS_ZB_SUPPLIER_CHANGE_FILE'
		where OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.MARK_NO = '{markNo}'  and OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.SUPPLIER_ID ='{supplierId}'
		<if test="packName!=null" value=" AND PACK_NAME='{packName}' "> </if> AND OS_ZB_SUPPLIER_BIAO_CHANGE_FILE.TYPE ='shangwu'
		group by file_t1.ID order by file_t1.SORT_VALUE;


	</select>


	<!--校验相关文件上传信息-->
	<select  id="checkFileExport">

		--商务支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务支持文件未导入') as WARN_INO  from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null

		union
		--技术支持文件（到标）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术支持文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is null

		union

		---到标的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		
		---到包的商务压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null

		union

		---到标的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术投标文件压缩文件未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null

		union

		---到标的技术报价文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 报价文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术报价文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 报价文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术价格投标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 价格投标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格投标文件包

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 价格投标文件压缩包未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 商务-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 商务-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术响应情况一览表
		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||' 技术-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select ('分标编号:'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||' 技术-供应商投标响应情况一览表未导入') as WARN_INO from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的上架商品
		
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  || ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;%' AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND OS_ZB_PURCHASE_PROJECT_INFO.NEED_ON_GOODS='是'

		union
		---到包的上架商品
		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || ',' ||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME|| ' 上架商品压缩包未导入') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		SYS_FILE_INFO ON RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' AND
		RELATED_ID LIKE '%' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO || '&amp;&amp;' || OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME AND
		RELATED_KEY = 'jishuGoods'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'PACK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND
		SYS_FILE_INFO.ID IS NULL AND  '{goodsImpBtn}'='YES'

		union

		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  || ' 技术支持文件缺少生产成本参数特性表') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS
		on OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PROJECT_NO = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO and OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.MARK_NO
		=OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO --and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY='MARK'
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS='是' and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'MARK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.ID is null

		union

		SELECT distinct ('分标编号:' || OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO ||','||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME || ' 技术支持文件缺少生产成本参数特性表') AS WARN_INO
		FROM OS_ZB_PURCHASE_PROJECT_INFO
		LEFT JOIN
		OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS
		on OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PROJECT_NO = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO and OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.MARK_NO
		=OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME =OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.PACK_NAME
		WHERE OS_ZB_PURCHASE_PROJECT_INFO.NEED_PRODUCT_COST_PARAMS='是' and OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY = 'PACK' AND
		OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO = '{markNo}' AND
		OS_ZB_PURCHASE_PROJECT_INFO.selected = '1' AND OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS.ID is null
	</select>


	<!--获取签章模块导入的信息-->
	<select  id="getUploadFiles_old">

		--商务支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||"\支持文件" as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		--技术支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\支持文件' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union

		---到标的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\商务\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union

		---到标的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\投标压缩文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union

		---到标的技术报价文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\报价文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术报价文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\报价文件' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO


		union

		---到包的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'


		union

		---到标的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\响应情况一览表' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union
		---到包的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME||'\上架商品' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  '{goodsImpBtn}'='YES'

		union

		---到标的投标授权人
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\投标授权人信息' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO INNER JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID

		union
		---到标的保证金附件
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金信息' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM SYS_FILE_INFO where RELATED_ID='{projectNo}';
	</select>


	<!--获取签章模块导入的信息-->
	<select  id="getUploadFiles">

		--商务支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		--商务支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='business'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union
		--技术支持文件（到标）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术' as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO  and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		--技术支持文件（到包）
		select OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_NAME as FILE_DISP_NAME,OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,(select BINARY_FILE from sys_file_info where id =OS_ZB_SUPPLIER_BID_ATTACHMENT.ATTACH_ID) as BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		OS_ZB_SUPPLIER_BID_ATTACHMENT  on OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO =OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO and OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME=OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and OS_ZB_SUPPLIER_BID_ATTACHMENT.IS_DELETE='1' and OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='skill'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and OS_ZB_SUPPLIER_BID_ATTACHMENT.ID is not null

		union

		---到标的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union
		---到包的商务压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union

		---到标的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术压缩包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null

		union

		---到标的技术报价文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术报价文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\价格\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuPriceZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的技术价格投标文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\开标' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术价格投标文件包

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\开标\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuBidOpenZip'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'

		union

		---到标的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO


		union

		---到包的商务响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\商务\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='shangwuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.BUSINESS_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'


		union

		---到标的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%' and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		and OS_ZB_PURCHASE_PROJECT_INFO.PURCHASE_TYPE='服务招标'
		group by OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO

		union

		---到包的技术响应情况一览表

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID like '%'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME and  RELATED_KEY ='jishuResponse'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='PACK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null


		union
		---到标的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID LIKE '%'||OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'%'  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  OS_ZB_PURCHASE_PROJECT_INFO.NEED_ON_GOODS in('是','YES')
		group by SYS_FILE_INFO.ID

		union
		---到包的上架商品

		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\技术\'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_PURCHASE_PROJECT_INFO
		left join
		SYS_FILE_INFO  on RELATED_PAGE = 'OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = OS_ZB_PURCHASE_PROJECT_INFO.PROJECT_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO||'&amp;&amp;'||OS_ZB_PURCHASE_PROJECT_INFO.PACK_NAME  AND   RELATED_KEY ='jishuGoods'
		where OS_ZB_PURCHASE_PROJECT_INFO.SKILL_FILE_WAY ='MARK' and OS_ZB_PURCHASE_PROJECT_INFO.MARK_NO='{markNo}' and OS_ZB_PURCHASE_PROJECT_INFO.selected='1'  and SYS_FILE_INFO.ID is not null
		AND  OS_ZB_PURCHASE_PROJECT_INFO.NEED_ON_GOODS in('是','YES')

		union

		---到标的投标授权人
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\投标授权人' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO INNER JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID

		union
		---到标的保证金附件 （目前没文件上传）
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金' as ROOT,SYS_FILE_INFO.BINARY_FILE
		FROM SYS_FILE_INFO where RELATED_ID='{projectNo}'

		---所有的的保证金附件(废弃 移入头部)

		union
		select SYS_FILE_INFO.FILE_DISP_NAME,SYS_FILE_INFO.FILE_PATH,'{markNo}\保证金' as ROOT,SYS_FILE_INFO.BINARY_FILE from OS_ZB_SUPPLIER_DEPOSIT
		inner join  SYS_FILE_INFO on SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_DEPOSIT.ID
		where SYS_FILE_INFO.RELATED_KEY in('bidDetailsInfoAttach','accountBaseInfoAttach','purchaseAccountInfoAttach');
	</select>


	<!--查看是否需要投标文件制作-->
	<select id="selectNeedMakeBidBook" >
		select count(ID) as count from OS_ZB_PURCHASE_PROJECT_INFO  WHERE SELECTED = '1' AND (
		NEED_PRODUCT IN ('是', 'YES') OR
		NEED_SALE IN ('是', 'YES') OR
		NEED_REPORT IN ('是', 'YES') OR
		NEED_CERT IN ('是', 'YES') OR
		NEED_LABEQP IN ('是', 'YES') OR
		NEED_MEQP IN ('是', 'YES') OR
		NEED_AUTH_PEOPEL IN ('是', 'YES') OR
		NEED_DEPOSIT IN ('是', 'YES') OR
		NEED_RESPONSE IN ('是', 'YES') OR
		NEED_XYXX IN ('是', 'YES') OR
		NEED_ZZYJ IN ('是', 'YES') OR
		NEED_CWSJ IN ('是', 'YES') OR
		NEED_TEC_ZGYS IN ('是', 'YES') OR
		NEED_BUS_ZGYS IN ('是', 'YES') OR
		NEED_SCCD IN ('是', 'YES') OR
		NEED_YXPJ IN ('是', 'YES') OR
		NEED_PRODUCT_COST_PARAMS IN ('是', 'YES') OR
		NEED_UNION_BOOK IN ('是', 'YES') OR
		NEED_BIDDER_PROFILE_JS IN ('是', 'YES') OR
		NEED_PM_ORG IN ('是', 'YES') OR
		NEED_WORK_LINE IN ('是', 'YES') OR
		NEED_COREDESIGNER IN ('是', 'YES') OR
		NEED_PROPOSE_SUBPACK IN ('是', 'YES') OR
		NEED_CHIEF_STAFFER IN ('是', 'YES') OR
		NEED_SAFETO_PROGRESS IN ('是', 'YES') OR
		NEED_DURATION_RESPONSE IN ('是', 'YES') OR
		NEED_OTHER_CONTENT_SW IN ('是', 'YES') OR
		NEED_OTHER_CONTENT_JS IN ('是', 'YES') OR
		NEED_QUALIFY_CERT IN ('是', 'YES') OR
		NEED_PERFORMANCE_CREDIT IN ('是', 'YES') OR
		NEED_BIDDER_PROFILE_SW IN ('是', 'YES') OR
		NEED_ACHIEVE_IMPL IN ('是', 'YES') OR
		NEED_CORE_RESUME IN ('是', 'YES') OR
		NEED_BUSINESS_OFFSET IN ('是', 'YES') OR
		NEED_TECH_OFFSET IN ('是', 'YES')) limit 0,1
	</select>



	<!--查看是否需要商务+技术 投标文件制作-->
	<select id="selectNeedMakeBidModel" >
		select count(ID) as count from OS_ZB_PURCHASE_PROJECT_INFO  WHERE SELECTED = '1' AND (
		NEED_PRODUCT IN ('是', 'YES') OR
		NEED_SALE IN ('是', 'YES') OR
		NEED_REPORT IN ('是', 'YES') OR
		NEED_CERT IN ('是', 'YES') OR
		NEED_LABEQP IN ('是', 'YES') OR
		NEED_MEQP IN ('是', 'YES') OR
		NEED_AUTH_PEOPEL IN ('是', 'YES') OR
		NEED_XYXX IN ('是', 'YES') OR
		NEED_ZZYJ IN ('是', 'YES') OR
		NEED_CWSJ IN ('是', 'YES') OR
		NEED_TEC_ZGYS IN ('是', 'YES') OR
		NEED_BUS_ZGYS IN ('是', 'YES') OR
		NEED_SCCD IN ('是', 'YES') OR
		NEED_YXPJ IN ('是', 'YES') OR
		NEED_PRODUCT_COST_PARAMS IN ('是', 'YES') OR
		NEED_UNION_BOOK IN ('是', 'YES') OR
		NEED_BIDDER_PROFILE_JS IN ('是', 'YES') OR
		NEED_PM_ORG IN ('是', 'YES') OR
		NEED_WORK_LINE IN ('是', 'YES') OR
		NEED_COREDESIGNER IN ('是', 'YES') OR
		NEED_PROPOSE_SUBPACK IN ('是', 'YES') OR
		NEED_CHIEF_STAFFER IN ('是', 'YES') OR
		NEED_SAFETO_PROGRESS IN ('是', 'YES') OR
		NEED_DURATION_RESPONSE IN ('是', 'YES') OR
		NEED_OTHER_CONTENT_SW IN ('是', 'YES') OR
		NEED_OTHER_CONTENT_JS IN ('是', 'YES') OR
		NEED_QUALIFY_CERT IN ('是', 'YES') OR
		NEED_PERFORMANCE_CREDIT IN ('是', 'YES') OR
		NEED_BIDDER_PROFILE_SW IN ('是', 'YES') OR
		NEED_ACHIEVE_IMPL IN ('是', 'YES') OR
		NEED_CORE_RESUME IN ('是', 'YES') OR
		NEED_BUSINESS_OFFSET IN ('是', 'YES') OR
		NEED_TECH_OFFSET IN ('是', 'YES')) limit 0,1
	</select>
	
</mapper>