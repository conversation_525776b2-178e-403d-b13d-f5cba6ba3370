"""
Word文档处理核心模块

提供表格填充和文档拆分功能
"""

from docx import Document
from docx.shared import RGBColor
import os
import re
import logging
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Word文档处理器类"""
    
    def __init__(self, source_dir: str):
        self.source_dir = source_dir
        self.processed_files = []
        
    def extract_project_id(self, folder_path: str) -> Optional[str]:
        folder_name = os.path.basename(folder_path)
        match = re.search(r'G00E-\d+-\d+', folder_name)
        if match:
            return match.group()
        
        parent_folder = os.path.basename(os.path.dirname(folder_path))
        match = re.search(r'G00E-\d+-\d+', parent_folder)
        if match:
            return match.group()
            
        return None
    
    def process_table_content(self, doc: Document) -> Dict[str, str]:
        mapping_dict = self._build_table1_mapping(doc)
        
        if not doc.tables:
            return mapping_dict

        # Process tables 1 and 2
        for table_idx, table in enumerate(doc.tables[:2]):
            logger.info(f"处理表{table_idx + 1}")
            self._fill_table_cells(table)
        
        # Process table 3
        if len(doc.tables) > 2:
            table3 = doc.tables[2]
            logger.info(f"处理表三，根据映射填充")
            self._fill_table_from_mapping(table3, mapping_dict)
            
        return mapping_dict

    def _build_table1_mapping(self, doc: Document) -> Dict[str, str]:
        """从表一构建映射字典"""
        mapping_dict = {}
        if not doc.tables:
            logger.warning("文档中没有找到表格")
            return mapping_dict
            
        table1 = doc.tables[0]
        logger.info(f"表一有 {len(table1.rows)} 行, {len(table1.columns)} 列")
        
        for row in table1.rows[1:]: # Skip header row
            cells = row.cells
            if len(cells) > 1:
                key = cells[1].text.strip()
                # The value is in the cell before the one to be filled by the bidder
                value_cell_index = -1
                for i, cell in enumerate(cells):
                    if "(投标人填写)" in cell.text or "（投标人填写）" in cell.text:
                        value_cell_index = i - 1
                        break
                
                if value_cell_index != -1:
                    value = cells[value_cell_index].text.strip()
                    if key and value:
                        mapping_dict[key] = value
        
        logger.info(f"构建了 {len(mapping_dict)} 个映射项")
        return mapping_dict

    def _fill_table_cells(self, table):
        """改进的表格填充逻辑"""
        for row in table.rows:
            cells = row.cells
            # Iterate backwards to find the cell to fill
            for i in range(len(cells) - 1, 0, -1):
                cell_text = cells[i].text.strip()
                if any(phrase in cell_text for phrase in ["(投标人填写)", "（投标人填写）"]):
                    # The value is in the cell to the left
                    source_value = cells[i-1].text.strip()
                    if source_value and source_value != cell_text:
                        self._clear_cell(cells[i])
                        cells[i].text = source_value
                        logger.info(f"填充单元格: '{cell_text}' -> '{source_value}'")

    def _fill_table_from_mapping(self, table, mapping_dict: Dict[str, str]):
        """根据映射字典填充表三"""
        for row_idx, row in enumerate(table.rows):
            cells = row.cells
            if len(cells) < 2 or row_idx == 0: # Skip header
                continue

            keyword = cells[1].text.strip()
            target_cell = cells[-1]
            
            if keyword and "见表1" in cells[-2].text.strip():
                fill_value = mapping_dict.get(keyword)
                if fill_value:
                    self._clear_cell(target_cell)
                    target_cell.text = fill_value
                    logger.info(f"根据‘见表1’填充: {keyword} -> {fill_value}")
                else:
                    logger.warning(f"未找到映射值: {keyword}, 检查映射字典或源数据")

    def _clear_cell(self, cell):
        """清空单元格内容但保留格式"""
        cell.text = ""
        # This is the python-docx way to clear a cell
        for p in cell.paragraphs:
            for r in p.runs:
                r.clear()

    def split_document(self, doc: Document, output_base: str) -> Tuple[str, str]:
        """改进的文档拆分逻辑"""
        # 1. 技术参数特征表
        tech_params_doc = Document()
        if doc.tables:
            tech_params_doc.add_paragraph("表1 标准技术参数表", style='Heading 1')
            self._copy_table(doc.tables[0], tech_params_doc)
        
        if len(doc.tables) > 1:
            tech_params_doc.add_paragraph() # Add space
            tech_params_doc.add_paragraph("表2 使用环境条件表", style='Heading 1')
            self._copy_table(doc.tables[1], tech_params_doc)

        tech_params_path = f"{output_base}-技术参数特征表.docx"
        tech_params_doc.save(tech_params_path)

        # 2. 组件材料配置表
        component_config_doc = Document()
        if len(doc.tables) > 2:
            component_config_doc.add_paragraph("表3 组件材料配置表（单套）", style='Heading 1')
            self._copy_table(doc.tables[2], component_config_doc)
        
        component_config_path = f"{output_base}-组件材料配置表.docx"
        component_config_doc.save(component_config_path)
        
        return tech_params_path, component_config_path

    def _copy_table(self, source_table, target_doc):
        """改进的表格复制逻辑，通过复制XML元素来保留所有格式"""
        from copy import deepcopy
        tbl = source_table._tbl
        new_tbl = deepcopy(tbl)
        target_doc.element.body.append(new_tbl)

    def process_single_file(self, file_path: str) -> Dict[str, str]:
        import shutil
        import tempfile
        import time
        
        try:
            project_id = self.extract_project_id(os.path.dirname(file_path))
            if not project_id:
                logger.error(f"无法从路径中提取项目ID: {file_path}")
                return {"error": "无法提取项目ID"}
            
            temp_path = ""
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_path = temp_file.name
            shutil.copy2(file_path, temp_path)
            
            doc = Document(temp_path)
            
            mapping_dict = self.process_table_content(doc)
            
            output_base = os.path.join(os.path.dirname(file_path), project_id)
            main_doc_path = f"{output_base}.docx"
            
            try:
                doc.save(main_doc_path)
            except PermissionError:
                timestamp = int(time.time())
                main_doc_path = f"{output_base}_{timestamp}.docx"
                doc.save(main_doc_path)
            
            tech_params_path, component_config_path = self.split_document(doc, output_base)
            
            os.unlink(temp_path)
            
            result = {
                "project_id": project_id,
                "main_doc": main_doc_path,
                "tech_params": tech_params_path,
                "component_config": component_config_path,
                "mapping_count": len(mapping_dict)
            }
            
            self.processed_files.append(result)
            logger.info(f"成功处理文件: {file_path}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}", exc_info=True)
            if temp_path and os.path.exists(temp_path):
                os.unlink(temp_path)
            return {"error": str(e)}
    
    def process_all_files(self, file_pattern: str = "办公桌") -> List[Dict[str, str]]:
        results = []
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file.endswith(".docx") and file_pattern in file and not file.startswith("~"):
                    if any(keyword in file for keyword in ["技术参数", "组件材料", "国网安徽省电力有限公司"]):
                        continue
                        
                    file_path = os.path.join(root, file)
                    result = self.process_single_file(file_path)
                    results.append(result)
        return results
    
    def get_processing_summary(self) -> Dict[str, int]:
        successful = len([f for f in self.processed_files if "error" not in f])
        failed = len([f for f in self.processed_files if "error" in f])
        
        return {
            "total_processed": len(self.processed_files),
            "successful": successful,
            "failed": failed
        }