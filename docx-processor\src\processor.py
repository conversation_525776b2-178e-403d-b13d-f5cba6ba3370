"""
Word文档处理核心模块

提供表格填充和文档拆分功能
"""

from docx import Document
from docx.shared import RGBColor
import os
import re
import logging
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Word文档处理器类"""
    
    def __init__(self, source_dir: str):
        self.source_dir = source_dir
        self.processed_files = []
        
    def extract_project_id(self, folder_path: str) -> Optional[str]:
        folder_name = os.path.basename(folder_path)
        match = re.search(r'G00E-\d+-\d+', folder_name)
        if match:
            return match.group()
        
        parent_folder = os.path.basename(os.path.dirname(folder_path))
        match = re.search(r'G00E-\d+-\d+', parent_folder)
        if match:
            return match.group()
            
        return None
    
    def process_table_content(self, doc: Document) -> Dict[str, str]:
        mapping_dict = self._build_table1_mapping(doc)
        
        if not doc.tables:
            return mapping_dict

        # Process tables 1 and 2
        for table_idx, table in enumerate(doc.tables[:2]):
            logger.info(f"处理表{table_idx + 1}")
            self._fill_table_cells(table)
        
        # Process table 3
        if len(doc.tables) > 2:
            table3 = doc.tables[2]
            logger.info(f"处理表三，根据映射填充")
            self._fill_table_from_mapping_improved(table3, mapping_dict)
            
        return mapping_dict

    def _build_table1_mapping(self, doc: Document) -> Dict[str, str]:
        """从表一构建映射字典"""
        mapping_dict = {}
        if not doc.tables:
            logger.warning("文档中没有找到表格")
            return mapping_dict

        table1 = doc.tables[0]
        logger.info(f"表一有 {len(table1.rows)} 行, {len(table1.columns)} 列")

        for row_idx, row in enumerate(table1.rows[1:], 1): # Skip header row
            cells = row.cells
            if len(cells) > 1:
                key = cells[1].text.strip()  # 第二列作为键

                # 查找包含"(投标人填写)"的单元格，并获取其左侧单元格的值
                value = ""
                for i, cell in enumerate(cells):
                    cell_text = cell.text.strip()
                    if any(phrase in cell_text for phrase in ["(投标人填写)", "（投标人填写）", "投标人填写"]):
                        # 获取左侧单元格的值作为映射值
                        if i > 0:
                            value = cells[i-1].text.strip()
                        break

                # 如果没有找到"(投标人填写)"，则使用最后一列的值
                if not value and len(cells) > 2:
                    last_cell_text = cells[-1].text.strip()
                    # 确保不是"(投标人填写)"类型的文本
                    if not any(phrase in last_cell_text for phrase in [
                        "(投标人填写)", "（投标人填写）", "投标人填写"
                    ]):
                        value = last_cell_text

                # 只添加有效的映射项
                if key and value and not any(phrase in value for phrase in [
                    "(投标人填写)", "（投标人填写）", "投标人填写"
                ]):
                    mapping_dict[key] = value
                    logger.debug(f"添加映射: '{key}' -> '{value}'")

        logger.info(f"构建了 {len(mapping_dict)} 个映射项")
        if mapping_dict:
            logger.info("映射字典内容:")
            for k, v in list(mapping_dict.items())[:5]:  # 只显示前5个
                logger.info(f"  '{k}' -> '{v}'")
            if len(mapping_dict) > 5:
                logger.info(f"  ... 还有 {len(mapping_dict) - 5} 个映射项")

        return mapping_dict

    def _fill_table_cells(self, table):
        """改进的表格填充逻辑"""
        for row in table.rows:
            cells = row.cells
            # Iterate backwards to find the cell to fill
            for i in range(len(cells) - 1, 0, -1):
                cell_text = cells[i].text.strip()
                if any(phrase in cell_text for phrase in ["(投标人填写)", "（投标人填写）"]):
                    # The value is in the cell to the left
                    source_value = cells[i-1].text.strip()
                    if source_value and source_value != cell_text:
                        self._clear_cell(cells[i])
                        cells[i].text = source_value
                        logger.info(f"填充单元格: '{cell_text}' -> '{source_value}'")

    def _fill_table_from_mapping(self, table, mapping_dict: Dict[str, str]):
        """根据映射字典填充表三"""
        logger.info(f"开始填充表三，映射字典有 {len(mapping_dict)} 个项目")

        for row_idx, row in enumerate(table.rows):
            cells = row.cells
            if len(cells) < 2 or row_idx == 0: # Skip header
                continue

            keyword = cells[1].text.strip()
            target_cell = cells[-1]
            
            if keyword and "见表1" in cells[-2].text.strip():
                fill_value = mapping_dict.get(keyword)
                if fill_value:
                    self._clear_cell(target_cell)
                    target_cell.text = fill_value
                    logger.info(f"根据‘见表1’填充: {keyword} -> {fill_value}")
                else:
                    logger.warning(f"未找到映射值: {keyword}, 检查映射字典或源数据")

    def _fill_table_from_mapping_improved(self, table, mapping_dict: Dict[str, str]):
        """改进的表三填充逻辑"""
        logger.info(f"开始填充表三，映射字典有 {len(mapping_dict)} 个项目")

        for row_idx, row in enumerate(table.rows):
            cells = row.cells
            if len(cells) < 2 or row_idx == 0: # Skip header
                continue

            keyword = cells[1].text.strip()  # 第二列的关键字

            # 查找需要填充的单元格（包含"投标人填写"或"见表1"的单元格）
            target_cell = None
            reference_found = False

            # 检查倒数第二列是否包含"见表1"
            if len(cells) >= 2:
                second_last_text = cells[-2].text.strip()
                if "见表1" in second_last_text or "见表一" in second_last_text:
                    target_cell = cells[-1]  # 最后一列
                    reference_found = True
                    logger.debug(f"行{row_idx}: 发现'见表1'引用，关键字='{keyword}'")

            # 如果没有找到"见表1"，检查是否有"投标人填写"
            if not reference_found:
                for cell_idx, cell in enumerate(cells):
                    cell_text = cell.text.strip()
                    if any(phrase in cell_text for phrase in [
                        "(投标人填写)", "（投标人填写）", "投标人填写"
                    ]):
                        target_cell = cell
                        reference_found = True
                        logger.debug(f"行{row_idx}: 发现'投标人填写'，关键字='{keyword}'")
                        break

            # 如果找到了需要填充的单元格和关键字
            if target_cell and keyword and reference_found:
                fill_value = mapping_dict.get(keyword)
                if fill_value:
                    self._clear_cell(target_cell)
                    target_cell.text = fill_value
                    logger.info(f"成功填充: '{keyword}' -> '{fill_value}'")
                else:
                    logger.warning(f"未找到映射值: '{keyword}'")
                    logger.debug(f"可用的映射键: {list(mapping_dict.keys())[:10]}")  # 显示前10个键
            elif keyword:
                logger.debug(f"行{row_idx}: 关键字'{keyword}'未找到填充目标或引用")

    def _clear_cell(self, cell):
        """清空单元格内容但保留格式"""
        cell.text = ""
        # This is the python-docx way to clear a cell
        for p in cell.paragraphs:
            for r in p.runs:
                r.clear()

    def split_document(self, doc: Document, output_base: str) -> Tuple[str, str]:
        """优化的文档拆分逻辑，确保生成简洁的文档结构"""
        logger.info("开始拆分文档...")

        # 1. 技术参数特征表 - 包含表1和表2
        tech_params_doc = Document()

        # 删除默认段落，避免空白段落
        if tech_params_doc.paragraphs:
            for p in tech_params_doc.paragraphs:
                p._element.getparent().remove(p._element)

        if doc.tables and len(doc.tables) >= 1:
            # 添加表1标题和表格
            title1 = tech_params_doc.add_paragraph("表1 标准技术参数表")
            title1.style = 'Heading 1'
            self._copy_table(doc.tables[0], tech_params_doc)
            logger.info("已复制表1到技术参数文档")

        if len(doc.tables) >= 2:
            # 添加适当的间距
            tech_params_doc.add_paragraph()

            # 添加表2标题和表格
            title2 = tech_params_doc.add_paragraph("表2 使用环境条件表")
            title2.style = 'Heading 1'
            self._copy_table(doc.tables[1], tech_params_doc)
            logger.info("已复制表2到技术参数文档")

        tech_params_path = f"{output_base}-技术参数特征表.docx"
        tech_params_doc.save(tech_params_path)
        logger.info(f"技术参数文档已保存: {tech_params_path}")

        # 2. 组件材料配置表 - 包含表3
        component_config_doc = Document()

        # 删除默认段落，避免空白段落
        if component_config_doc.paragraphs:
            for p in component_config_doc.paragraphs:
                p._element.getparent().remove(p._element)

        if len(doc.tables) >= 3:
            # 添加表3标题和表格
            title3 = component_config_doc.add_paragraph("表3 组件材料配置表（单套）")
            title3.style = 'Heading 1'
            self._copy_table(doc.tables[2], component_config_doc)
            logger.info("已复制表3到组件配置文档")
        else:
            # 如果没有表3，添加提示信息
            component_config_doc.add_paragraph("未找到表3数据")

        component_config_path = f"{output_base}-组件材料配置表.docx"
        component_config_doc.save(component_config_path)
        logger.info(f"组件配置文档已保存: {component_config_path}")

        # 验证生成的文档结构
        self._validate_generated_documents(tech_params_path, component_config_path)

        return tech_params_path, component_config_path

    def _validate_generated_documents(self, tech_params_path: str, component_config_path: str):
        """验证生成的文档结构"""
        try:
            # 验证技术参数文档
            tech_doc = Document(tech_params_path)
            logger.info(f"技术参数文档结构: {len(tech_doc.paragraphs)}个段落, {len(tech_doc.tables)}个表格")

            # 验证组件配置文档
            config_doc = Document(component_config_path)
            logger.info(f"组件配置文档结构: {len(config_doc.paragraphs)}个段落, {len(config_doc.tables)}个表格")

            # 检查是否有异常的段落数量
            if len(tech_doc.paragraphs) > 10:
                logger.warning(f"技术参数文档段落数量异常: {len(tech_doc.paragraphs)}")
            if len(config_doc.paragraphs) > 5:
                logger.warning(f"组件配置文档段落数量异常: {len(config_doc.paragraphs)}")

        except Exception as e:
            logger.error(f"验证生成文档时出错: {e}")

    def _copy_table(self, source_table, target_doc):
        """改进的表格复制逻辑，保留完整的格式信息"""
        from copy import deepcopy

        # 创建新表格，先获取源表格的行列数
        rows_count = len(source_table.rows)
        cols_count = len(source_table.columns) if source_table.rows else 0

        if rows_count == 0 or cols_count == 0:
            logger.warning("源表格为空，跳过复制")
            return

        # 创建新表格
        new_table = target_doc.add_table(rows=1, cols=cols_count)

        # 复制表格样式
        try:
            if hasattr(source_table, 'style') and source_table.style:
                new_table.style = source_table.style
        except Exception as e:
            logger.warning(f"复制表格样式失败: {e}")

        # 删除默认行，准备逐行复制
        if new_table.rows:
            new_table._tbl.remove(new_table.rows[0]._tr)

        # 逐行复制内容和格式
        for row_idx, source_row in enumerate(source_table.rows):
            # 添加新行
            new_row = new_table.add_row()

            # 复制行属性
            try:
                if hasattr(source_row, '_tr') and source_row._tr.trPr is not None:
                    new_row._tr.trPr = deepcopy(source_row._tr.trPr)
            except Exception as e:
                logger.warning(f"复制行属性失败 (行{row_idx}): {e}")

            # 复制每个单元格
            for cell_idx, source_cell in enumerate(source_row.cells):
                if cell_idx < len(new_row.cells):
                    target_cell = new_row.cells[cell_idx]
                    self._copy_cell_content_and_format(source_cell, target_cell)

    def _copy_cell_content_and_format(self, source_cell, target_cell):
        """复制单元格内容和格式"""
        from copy import deepcopy

        try:
            # 清空目标单元格
            target_cell.text = ""

            # 复制单元格属性
            if hasattr(source_cell, '_tc') and source_cell._tc.tcPr is not None:
                target_cell._tc.tcPr = deepcopy(source_cell._tc.tcPr)

            # 复制段落内容和格式
            # 先清空目标单元格的段落
            for p in target_cell.paragraphs:
                p.clear()

            # 如果目标单元格有段落，删除多余的
            while len(target_cell.paragraphs) > 1:
                target_cell._tc.remove(target_cell.paragraphs[-1]._p)

            # 复制源单元格的所有段落
            for para_idx, source_para in enumerate(source_cell.paragraphs):
                if para_idx == 0:
                    # 使用第一个现有段落
                    target_para = target_cell.paragraphs[0]
                else:
                    # 添加新段落
                    target_para = target_cell.add_paragraph()

                # 复制段落格式
                try:
                    if hasattr(source_para, '_p') and source_para._p.pPr is not None:
                        target_para._p.pPr = deepcopy(source_para._p.pPr)
                except Exception as e:
                    logger.warning(f"复制段落格式失败: {e}")

                # 复制文本运行
                for run in source_para.runs:
                    new_run = target_para.add_run(run.text)

                    # 复制文本格式
                    try:
                        new_run.bold = run.bold
                        new_run.italic = run.italic
                        new_run.underline = run.underline

                        if run.font.size:
                            new_run.font.size = run.font.size
                        if run.font.name:
                            new_run.font.name = run.font.name
                        if run.font.color.rgb:
                            new_run.font.color.rgb = run.font.color.rgb

                        # 复制其他格式属性
                        if hasattr(run, '_r') and run._r.rPr is not None:
                            new_run._r.rPr = deepcopy(run._r.rPr)

                    except Exception as e:
                        logger.warning(f"复制文本格式失败: {e}")

        except Exception as e:
            logger.error(f"复制单元格内容失败: {e}")
            # 如果复制失败，至少保留文本内容
            target_cell.text = source_cell.text

    def process_single_file(self, file_path: str) -> Dict[str, str]:
        import shutil
        import tempfile
        import time
        
        try:
            project_id = self.extract_project_id(os.path.dirname(file_path))
            if not project_id:
                logger.error(f"无法从路径中提取项目ID: {file_path}")
                return {"error": "无法提取项目ID"}
            
            temp_path = ""
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_path = temp_file.name
            shutil.copy2(file_path, temp_path)
            
            doc = Document(temp_path)
            
            mapping_dict = self.process_table_content(doc)
            
            output_base = os.path.join(os.path.dirname(file_path), project_id)
            main_doc_path = f"{output_base}.docx"
            
            try:
                doc.save(main_doc_path)
            except PermissionError:
                timestamp = int(time.time())
                main_doc_path = f"{output_base}_{timestamp}.docx"
                doc.save(main_doc_path)
            
            tech_params_path, component_config_path = self.split_document(doc, output_base)
            
            os.unlink(temp_path)
            
            result = {
                "project_id": project_id,
                "main_doc": main_doc_path,
                "tech_params": tech_params_path,
                "component_config": component_config_path,
                "mapping_count": len(mapping_dict)
            }
            
            self.processed_files.append(result)
            logger.info(f"成功处理文件: {file_path}")
            
            return result
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}", exc_info=True)
            if temp_path and os.path.exists(temp_path):
                os.unlink(temp_path)
            return {"error": str(e)}
    
    def process_all_files(self, file_pattern: str = "办公桌") -> List[Dict[str, str]]:
        results = []
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                if file.endswith(".docx") and file_pattern in file and not file.startswith("~"):
                    if any(keyword in file for keyword in ["技术参数", "组件材料", "国网安徽省电力有限公司"]):
                        continue
                        
                    file_path = os.path.join(root, file)
                    result = self.process_single_file(file_path)
                    results.append(result)
        return results
    
    def get_processing_summary(self) -> Dict[str, int]:
        successful = len([f for f in self.processed_files if "error" not in f])
        failed = len([f for f in self.processed_files if "error" in f])
        
        return {
            "total_processed": len(self.processed_files),
            "successful": successful,
            "failed": failed
        }