<?xml version="1.0"?>
<!--
 *
 * System.Data.SQLite.dll.config -
 *
 * Written by <PERSON>.
 * Released to the public domain, use at your own risk!
 *
-->
<configuration>
  <appSettings>
    <!--
    ****************************************************************************
    **                       Special Replacement Tokens                       **
    ****************************************************************************
    -->

    <!--
        NOTE: %PreLoadSQLite_AssemblyDirectory% - If this token (which is
              case-sensitive and must include the percent characters) is
              present within a setting value being returned, it will be
              replaced with the qualified name of the directory containing the
              System.Data.SQLite assembly.  If the name of the directory is not
              available, the token will not be replaced.

        NOTE: %PreLoadSQLite_TargetFramework% - If this token (which is
              case-sensitive and must include the percent characters) is
              present within a setting value being returned, it will be
              replaced with an abbreviation of the target framework attribute
              value for the System.Data.SQLite assembly.  If the target
              framework attribute value is not available, the token will not be
              replaced.

        NOTE: %PreLoadSQLite_XmlConfigDirectory% - If this token (which is
              case-sensitive and must include the percent characters) is
              present within a setting value being returned, it will be
              replaced with the qualified name of the directory containing the
              XML configuration file.  If the name of the directory is not
              available, the token will not be replaced.  Generally, this token
              may only be used within the XML configuration file itself.
    -->

    <!--
    ****************************************************************************
    **                         Configuration Settings                         **
    ****************************************************************************
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.Linq.SQLiteProviderManifest class
              (and the System.Data.SQLite.EF6.SQLiteProviderManifest class) to
              modify future provider manifest tokens by appending the value of
              the environment variable to the existing provider manifest token,
              if any.  Typically, in order for the constructed provider
              manifest token to be syntactically correct, the environment
              variable value [to be appended] must begin with a semicolon.
    -->
    <!--
    <add key="AppendManifestToken_SQLiteProviderManifest" value="" />
    -->

    <!--
          NOTE: If this environment variable is set [to anything], it will be
                used by the System.Data.SQLite.SQLiteConnection class as the
                default flags for all opened connections (i.e. when they are
                not present in the connection string).
    -->
    <!--
    <add key="DefaultFlags_SQLiteConnection" value="" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the SQLite
              logging subsystem will be initially disabled when initialized.
    -->
    <!--
    <add key="Disable_SQLiteLog" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the SQLite
              logging subsystem may be initialized in a non-default application
              domain.  By default, this is not allowed due to the potential
              for application domain unloading issues.
    -->
    <!--
    <add key="Force_SQLiteLog" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], the SQLite
              logging subsystem will attempt to perform initialization even
              when an attempt was previously made.
    -->
    <!--
    <add key="Initialize_SQLiteLog" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the native
              library pre-loading functionality will be disabled.  By default,
              the native library pre-loading will attempt to load the native
              SQLite library from architecture-specific (e.g. "x86", "amd64",
              "x64") or platform-specific (e.g. "Win32") directories that
              reside underneath the application base directory.
    -->
    <!--
    <add key="No_PreLoadSQLite" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the new
              connection string parsing algorithm will not be used.  This
              environment variable is intended for use with legacy code only.
    -->
    <!--
    <add key="No_SQLiteConnectionNewParser" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the initial
              search for types in all loaded assemblies that are tagged with
              the SQLiteFunction attribute will be skipped.  Normally, this
              search is conducted only once per application domain by the
              static constructor of the SQLiteFunction class; however, these
              implementation details are subject to change.
    -->
    <!--
    <add key="No_SQLiteFunctions" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], all calls to
              the GetSettingValue method will return the default value.  This
              will effectively prevent all other setting values from having any
              effect, including those specified via other supported environment
              variables or in the associated XML configuration file.

        SPECIAL NOTE: This setting is included here only for completeness; it
                      does not make any sense to use it here as it will never
                      be consulted.  For this setting to work properly, it must
                      be set via an environment variable.
    -->
    <!--
    <add key="No_SQLiteGetSettingValue" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], the SQLite
              logging subsystem will not be initialized by the SQLiteLog class;
              however, it may still be initialized by external components (i.e.
              something other than System.Data.SQLite).
    -->
    <!--
    <add key="No_SQLiteLog" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], calls to the
              GetSettingValue method will never result in the XML configuration
              file being read; instead, the default value will be returned.
              This will effectively prevent any setting values specified via
              the XML configuration file from having any effect.

        SPECIAL NOTE: This setting is included here only for completeness; it
                      does not make any sense to use it here as it will never
                      be consulted.  For this setting to work properly, it must
                      be set via an environment variable.
    -->
    <!--
    <add key="No_SQLiteXmlConfigFile" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the base
              directory itself is considered valid for pre-loading the native
              SQLite library.
    -->
    <!--
    <add key="PreLoadSQLite_AllowBaseDirectoryOnly" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], it will be
              used instead of the application base directory by the native
              library pre-loader.  This environment variable can be especially
              useful in ASP.NET and other hosted environments where direct
              control of the location of the managed assemblies is not under
              the control of the application.
    -->
    <!--
    <add key="PreLoadSQLite_BaseDirectory" value="" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the native
              library pre-loader subsystem will attempt to give the interactive
              user an opportunity to attach a debugger to the current process.
    -->
    <!--
    <add key="PreLoadSQLite_BreakIntoDebugger" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], it will be
              used as the base file name (without directory information) for
              the native SQLite library to be pre-loaded (e.g. "sqlite3.dll"
              or "libsqlite3.so.0").
    -->
    <!--
    <add key="PreLoadSQLite_LibraryFileNameOnly" value="" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], it will be
              used instead of the processor architecture value contained in the
              PROCESSOR_ARCHITECTURE environment variable to help build the
              path of the native library to pre-load.
    -->
    <!--
    <add key="PreLoadSQLite_ProcessorArchitecture" value="x86" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], the native
              library pre-loading code will skip conducting a search for the
              native library to pre-load.  By default, the search starts in the
              location of the currently executing assembly (i.e. the assembly
              containing all the managed components for System.Data.SQLite) and
              then falls back to the application domain base directory.
    -->
    <!--
    <add key="PreLoadSQLite_NoSearchForDirectory" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the location
              of the currently executing assembly (i.e. the one containing all
              the managed components for System.Data.SQLite) will be used as
              the basis for locating the the native library to pre-load (i.e.
              instead of using the application domain base directory).
    -->
    <!--
    <add key="PreLoadSQLite_UseAssemblyDirectory" value="1" />
    -->

    <!--
        NOTE: This configuration variable is normally set by the operating
              system itself and should reflect the native processor
              architecture of the current process (e.g. a 32-bit x86
              application running on a 64-bit x64 operating system should have
              the value "x86").
    -->
    <!--
    <add key="PROCESSOR_ARCHITECTURE" value="%PROCESSOR_ARCHITECTURE%" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], page #1
              of database files encrypted using the (unsupported) legacy
              CryptoAPI codec will be encrypted.  This was the default
              behavior prior to release 1.0.112.3; however, it is now
              disabled (by default) as it can cause corruption and/or other
              malfunctions in some circumstances.  Please do not use this
              environment variable unless it is absolutely necessary for your
              specific use case.

        NOTE: Since this environment variable is (only) read from the native
              interop assembly, it cannot be set via this configuration file.
              It can only be set within the process environment.
    -->
    <!--
    <add key="SQLite_LegacyEncryptPage1" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], all calls to
              prepare a SQL query will be logged, regardless of the flags for
              the associated connection.
    -->
    <!--
    <add key="SQLite_ForceLogPrepare" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], calls into
              key members pertaining to the lifecycle of connections and their
              associated classes (e.g. LINQ, EF6, etc) will be logged.
    -->
    <!--
    <add key="SQLite_ForceLogLifecycle" value="1" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteFactory class as the type
              name containing the System.Data.Common.DbProviderServices
              implementation that should be used.
    -->
    <!--
    <add key="TypeName_SQLiteProviderServices" value="" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteConvert class as the default
              DbType value that should be used when a per-connection value is
              not available.
    -->
    <!--
    <add key="Use_SQLiteConvert_DefaultDbType" value="Object" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteConvert class as the default
              type name that should be used when a per-connection value is not
              available.
    -->
    <!--
    <add key="Use_SQLiteConvert_DefaultTypeName" value="" />
    -->
  </appSettings>
</configuration>
