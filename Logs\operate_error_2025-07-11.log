2025-07-11 09:15:43.6197|TRACE|MyCommon.util.LogsHelper|  message:发票号码格式不正确|System.Exception: 发票号码格式不正确
   在 ess_zbfz_main.baseInfoFrm.FrmBillInfoV2.BatchBillImages(String file, List`1 billList, List`1 fileList, String relateKey, Int32& handerCount)
   在 ess_zbfz_main.baseInfoFrm.FrmBillInfoV2.ExportBillImages_Click(Object sender, EventArgs e)
2025-07-11 10:00:17.2541|TRACE|MyCommon.util.LogsHelper|  message:发票号码格式不正确|System.Exception: 发票号码格式不正确
   在 ess_zbfz_main.baseInfoFrm.FrmBillInfoV2.BatchBillImages(String file, List`1 billList, List`1 fileList, String relateKey, Int32& handerCount)
   在 ess_zbfz_main.baseInfoFrm.FrmBillInfoV2.ExportBillImages_Click(Object sender, EventArgs e)
2025-07-11 11:04:35.3147|TRACE|MyCommon.util.LogsHelper|  message:第10行，开票日期格式不正确

|System.Exception: 第10行，开票日期格式不正确


   在 MyCommon.Repository.common.BaseAbstractRepository`1.ImportExcel(IList`1& entities, Int32 titleRow, Func`3 CheckAction, String businessId, Int32 startSheet)
   在 ess_zbfz_main.baseInfoFrm.FrmBillInfoV2.btnImport_Click(Object sender, EventArgs e)
