我理解您的需求是将处理后的表一/表二和表三分别拆分到两个文档中，并根据原始文件名的一部分进行重命名。以下是完整的解决方案：

Python
from docx import Document
import os
import re

def fill_and_split_tables(input_path, output_dir):
    """处理Word文档表格并拆分到两个文件中"""
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取基础文件名（不含扩展名）
    base_name = os.path.basename(input_path)
    project_name = base_name.split('.')[0]
    
    # 处理文档并填充表格
    doc = Document(input_path)
    mapping_dict = process_tables(doc)
    
    # 创建拆分后的文档
    doc_table12 = Document()
    doc_table3 = Document()
    
    # 复制非表格内容（段落和图片）
    copy_non_table_content(doc, doc_table12)
    copy_non_table_content(doc, doc_table3)
    
    # 提取表格并添加到新文档
    extract_tables_to_docs(doc, doc_table12, doc_table3)
    
    # 生成输出文件名
    output_table12 = os.path.join(output_dir, f"{project_name}_报价表.docx")
    output_table3 = os.path.join(output_dir, f"{project_name}_明细表.docx")
    
    # 保存文档
    doc_table12.save(output_table12)
    doc_table3.save(output_table3)
    
    print(f"拆分完成: \n表一表二 -> {output_table12} \n表三 -> {output_table3}")
    return output_table12, output_table3

def process_tables(doc):
    """处理文档中的所有表格并返回表一映射字典"""
    # 构建表一的映射字典
    mapping_dict = {}
    if len(doc.tables) > 0:
        table1 = doc.tables[0]
        for row in table1.rows:
            cols = row.cells
            if len(cols) >= 2:
                key = cols[1].text.strip()
                value = cols[-1].text.strip()
                if key:
                    mapping_dict[key] = value
    
    # 处理表一和表二
    for table_idx, table in enumerate(doc.tables):
        # 只处理前两个表格（表一和表二）
        if table_idx < 2:
            for row in table.rows:
                cells = row.cells
                for i in range(len(cells) - 1, 0, -1):
                    if "(投标人填写)" in cells[i].text:
                        left_value = cells[i-1].text.strip()
                        clear_cell(cells[i])
                        cells[i].text = left_value
    
    # 处理表三
    if len(doc.tables) > 2:
        table3 = doc.tables[2]
        for row in table3.rows:
            cols = row.cells
            if len(cols) >= 2:
                keyword = cols[1].text.strip()
                if keyword and "(投标人填写)" in cols[-1].text:
                    fill_value = mapping_dict.get(keyword, "")
                    clear_cell(cols[-1])
                    cols[-1].text = fill_value
    
    return mapping_dict

def clear_cell(cell):
    """清空单元格内容"""
    for paragraph in cell.paragraphs:
        for run in paragraph.runs:
            run.text = ""
    cell.text = ''

def copy_non_table_content(source_doc, target_doc):
    """复制非表格内容（段落和图片）到目标文档"""
    for element in source_doc.element.body:
        # 复制段落
        if element.tag.endswith('p'):
            new_para = target_doc.add_paragraph()
            new_para._p = element
            
        # 复制图片（简化处理，实际可能需要更复杂的处理）
        elif element.tag.endswith('pict') or element.tag.endswith('drawing'):
            # 这里简化处理，实际应用中需要更完整的图片复制逻辑
            new_para = target_doc.add_paragraph()
            new_para._p = element

def extract_tables_to_docs(source_doc, doc_table12, doc_table3):
    """提取表格到对应的文档"""
    for table_idx, table in enumerate(source_doc.tables):
        # 前两个表格（表一和表二）添加到第一个文档
        if table_idx < 2:
            new_table = doc_table12.add_table(rows=0, cols=len(table.columns))
            copy_table(table, new_table)
            doc_table12.add_paragraph()  # 添加空白行分隔表格
        
        # 第三个表格（表三）添加到第二个文档
        elif table_idx == 2:
            new_table = doc_table3.add_table(rows=0, cols=len(table.columns))
            copy_table(table, new_table)
            doc_table3.add_paragraph()  # 添加空白行分隔表格

def copy_table(source_table, target_table):
    """复制表格内容和格式"""
    for row in source_table.rows:
        new_row = target_table.add_row()
        for cell_idx, cell in enumerate(row.cells):
            # 复制单元格文本
            new_row.cells[cell_idx].text = cell.text
            
            # 复制单元格格式（简化版本）
            if cell._tc.tcPr is not None:
                new_row.cells[cell_idx]._tc.tcPr = cell._tc.tcPr

# 使用示例
if __name__ == "__main__":
    input_file = "项目投标文件.docx"  # 您的原始文档
    output_directory = "拆分结果"    # 输出目录
    
    # 处理并拆分文档
    table12_path, table3_path = fill_and_split_tables(input_file, output_directory)
功能说明：
表格处理：

表一和表二：自动填充"(投标人填写)"为左侧单元格的值
表三：根据表一的五金件等映射关系自动填充
文档拆分：

将所有表一和表二提取到项目名_报价表.docx
将表三提取到项目名_明细表.docx
保留原始文档的非表格内容（段落、图片等）
命名规则：

基于原始文件名自动生成新文件名
例如：原始文件为"ABC项目投标文件.docx"，则生成：
"ABC项目_报价表.docx"（包含表一和表二）
"ABC项目_明细表.docx"（包含表三）
使用说明：
设置输入文件路径和输出目录：
Python
   input_file = "项目投标文件.docx"  # 修改为您的文件路径
   output_directory = "输出结果"     # 修改为您想要的输出目录
运行脚本：
Bash
   python split_tables.py
输出结果：
在指定输出目录生成两个新文档
控制台显示处理完成的文件路径
注意事项：
表格位置：

脚本假设表一是第一个表格，表二是第二个表格，表三是第三个表格
如果实际文档结构不同，请调整table_idx的判断逻辑
格式保留：

脚本会尽量保留原始表格格式
复杂格式（如合并单元格、特殊样式）可能需要额外处理
图片处理：

脚本包含基础的图片复制功能
对于复杂文档中的图片，可能需要更完善的图片处理逻辑
文档完整性：

拆分后的文档包含原始文档的所有非表格内容
表格之间会自动添加空白行分隔
此解决方案完整实现了您的需求，能够自动处理表格内容并按照指定规则拆分文档和命名文件。