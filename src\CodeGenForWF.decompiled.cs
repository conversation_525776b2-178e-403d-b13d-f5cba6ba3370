using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using CodeGenForWF.config;
using CodeGenForWF.extendmethods;
using MyCommon.Model.dto;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("CodeGenForWF")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("CodeGenForWF")]
[assembly: AssemblyCopyright("Copyright ©  2022")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("f0887ce0-3a82-4551-aee3-0242a4ab5011")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace CodeGenForWF
{
	public class CodeGenHelper
	{
		private static IDictionary<Type, MyTable> cacheDict = (IDictionary<Type, MyTable>)new ConcurrentDictionary<Type, MyTable>();

		public static void GenDetailWF<Table, Column>(string nameSpace, string saveFolder, params Type[] types)
		{
			GenDetailWF<Table, Column>(types, nameSpace, saveFolder);
		}

		public static void GenListWF<Table, Column>(string nameSpace, string saveFolder, params Type[] types)
		{
			GenListWF<Table, Column>(types, nameSpace, saveFolder);
		}

		public static void GenDetailWF<Table, Column>(Type[] types, string nameSpace, string saveFolder, int maxColumns = 2, GenConfig genConfig = null)
		{
			BindEnum bindEnum = genConfig.BindEnum;
			ScopeEnum scopeEnum = genConfig.ScopeEnum;
			string tableNameField = genConfig.TableNameField;
			string columnNameField = genConfig.ColumnNameField;
			string columnExistField = genConfig.ColumnExistField;
			string columnCnTextField = genConfig.ColumnCnTextField;
			if (types == null)
			{
				throw new Exception("参数types不得为空");
			}
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			string name = executingAssembly.GetName().Name;
			bool flag = scopeEnum.Equals(ScopeEnum.Field);
			XmlDocument xmlDocument = new XmlDocument();
			XmlReaderSettings xmlReaderSettings = new XmlReaderSettings();
			xmlReaderSettings.IgnoreComments = true;
			xmlReaderSettings.DtdProcessing = DtdProcessing.Ignore;
			using (Stream input = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.detail.Designer.xml"))
			{
				XmlReader reader = XmlReader.Create(input, xmlReaderSettings);
				xmlDocument.Load(reader);
			}
			XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDocument.NameTable);
			XmlElement documentElement = xmlDocument.DocumentElement;
			XmlNode xmlNode = documentElement.SelectSingleNode("descendant::head", nsmgr);
			XmlNode xmlNode2 = documentElement.SelectSingleNode("descendant::body", nsmgr);
			XmlNode xmlNode3 = documentElement.SelectSingleNode("descendant::foot", nsmgr);
			XmlNode xmlNode4 = documentElement.SelectSingleNode("descendant::label", nsmgr);
			XmlNode xmlNode5 = documentElement.SelectSingleNode("descendant::textBox", nsmgr);
			XmlNode xmlNode6 = documentElement.SelectSingleNode("descendant::form", nsmgr);
			XmlNode xmlNode7 = documentElement.SelectSingleNode("descendant::initComponents", nsmgr);
			bool flag2 = bindEnum.Equals(BindEnum.DB);
			XmlDocument xmlDocument2 = new XmlDocument();
			using (Stream input2 = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.detail.code.xml"))
			{
				XmlReader reader2 = XmlReader.Create(input2, xmlReaderSettings);
				xmlDocument2.Load(reader2);
			}
			XmlNamespaceManager xmlNamespaceManager = new XmlNamespaceManager(xmlDocument2.NameTable);
			XmlElement documentElement2 = xmlDocument2.DocumentElement;
			Type typeFromHandle = typeof(Table);
			Type typeFromHandle2 = typeof(Column);
			foreach (Type type in types)
			{
				int num = 0;
				int num2 = 20;
				int num3 = 20;
				int num4 = 140;
				int num5 = 10;
				int num6 = 240;
				int num7 = 10;
				int num8 = (int)((float)(num6 * 2 * maxColumns) * 0.92f);
				string innerText = xmlNode.InnerText;
				string innerText2 = xmlNode2.InnerText;
				string innerText3 = xmlNode3.InnerText;
				string innerText4 = xmlNode7.InnerText;
				string innerText5 = xmlNode4.InnerText;
				string innerText6 = xmlNode5.InnerText;
				string innerText7 = xmlNode6.InnerText;
				string innerText8 = documentElement2.InnerText;
				StringBuilder stringBuilder = new StringBuilder();
				StringBuilder stringBuilder2 = new StringBuilder();
				StringBuilder stringBuilder3 = new StringBuilder();
				StringBuilder stringBuilder4 = new StringBuilder();
				nameSpace = (string.IsNullOrWhiteSpace(nameSpace) ? type.Namespace : nameSpace);
				string name2 = type.Name;
				string text = name2 + "Edit";
				Table val = type.GetCustomAttribute<Table>();
				if (val == null)
				{
					ConstructorInfo[] constructors = typeFromHandle.GetConstructors();
					val = (Table)((constructors != null) ? constructors[0].Invoke(new object[1] { "" }) : null);
				}
				string text2 = Convert.ToString(typeFromHandle.GetProperty(tableNameField)?.GetValue(val) ?? name2);
				List<MemberInfo> list;
				if (flag)
				{
					FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
					list = new List<MemberInfo>(fields);
				}
				else
				{
					PropertyInfo[] properties = type.GetProperties();
					list = new List<MemberInfo>(properties);
				}
				foreach (MemberInfo item in list)
				{
					Type type2 = (item as FieldInfo)?.FieldType ?? (item as PropertyInfo).PropertyType;
					if (type2 is IEnumerable)
					{
						continue;
					}
					int num9 = 0;
					Column val2 = item.GetCustomAttribute<Column>();
					if (val2 == null && genConfig.NeedColumAttribute)
					{
						continue;
					}
					if (val2 == null)
					{
						ConstructorInfo[] constructors2 = typeFromHandle2.GetConstructors();
						val2 = (Column)((constructors2 != null) ? constructors2[0].Invoke(new object[1] { "" }) : null);
					}
					bool flag3 = Convert.ToBoolean(typeFromHandle2.GetProperty(columnExistField)?.GetValue(val2) ?? ((object)true));
					string text3 = Convert.ToString(typeFromHandle2.GetProperty(columnNameField)?.GetValue(val2) ?? item.Name);
					string newValue = Convert.ToString(typeFromHandle2.GetProperty(columnCnTextField)?.GetValue(val2) ?? text3);
					string name3 = item.Name;
					string text4 = (flag2 ? text3 : item.Name);
					if (flag3)
					{
						string value = innerText5.Replace("{labelName}", "label_" + name3).Replace("{tabIndex}", num9.ToString() ?? "").Replace("{labelText}", newValue)
							.Replace("{x}", num2.ToString() ?? "")
							.Replace("{y}", num3.ToString() ?? "")
							.Replace("{width}", num4.ToString() ?? "")
							.Replace("{foreColor}", "System.Drawing.Color.Black");
						stringBuilder.Append(value);
						stringBuilder2.Append("private System.Windows.Forms.Label label_" + name3 + ";\r\n");
						stringBuilder3.Append("this.Controls.Add(this.label_" + name3 + ");\r\n");
						stringBuilder4.Append(innerText4.Replace("{componentName}", "label_" + name3).Replace("{component}", "System.Windows.Forms.Label") + "\r\n");
						num9++;
						string value2 = innerText5.Replace("{labelName}", "require_" + name3).Replace("{tabIndex}", num9.ToString() ?? "").Replace("{labelText}", "*")
							.Replace("{x}", (num2 + num4 + num7).ToString() ?? "")
							.Replace("{y}", num3.ToString() ?? "")
							.Replace("{width}", num5.ToString() ?? "")
							.Replace("{foreColor}", "System.Drawing.Color.Red");
						stringBuilder.Append(value2);
						stringBuilder2.Append("private System.Windows.Forms.Label require_" + name3 + ";\r\n");
						stringBuilder3.Append("this.Controls.Add(this.require_" + name3 + ");\r\n");
						stringBuilder4.Append(innerText4.Replace("{componentName}", "require_" + name3).Replace("{component}", "System.Windows.Forms.Label") + "\r\n");
						num9++;
						string value3 = innerText6.Replace("{textBoxName}", text4).Replace("{tabIndex}", num9.ToString() ?? "").Replace("{labelText}", newValue)
							.Replace("{x}", (num2 + num4 + num5 + num7 * 2).ToString() ?? "")
							.Replace("{y}", num3.ToString() ?? "")
							.Replace("{width}", num6.ToString() ?? "");
						stringBuilder.Append(value3);
						stringBuilder2.Append("private System.Windows.Forms.TextBox " + text4 + ";\r\n");
						stringBuilder3.Append("this.Controls.Add(this." + text4 + ");\r\n");
						stringBuilder4.Append(innerText4.Replace("{componentName}", text4).Replace("{component}", " System.Windows.Forms.TextBox") + "\r\n");
						num9++;
						num++;
						if (num != 0 && num % maxColumns == 0)
						{
							num2 = 20;
							num3 += 30;
						}
						else
						{
							num2 = num2 + num4 + num5 + num6 + num7 * 3;
						}
					}
				}
				string text5 = innerText7.Replace("{formName}", text).Replace("{subControls}", stringBuilder3.ToString()).Replace("{width}", num8.ToString() ?? "");
				stringBuilder.Append("\r\n" + text5);
				innerText = innerText.Replace("{namespace}", nameSpace).Replace("{formName}", text).Replace("&amp;&amp;", "&&");
				innerText2 = innerText2.Replace("{bodyContent}", stringBuilder.ToString());
				innerText3 = innerText3.Replace("{footContent}", stringBuilder2.ToString());
				string contents = innerText + stringBuilder4.Append("\r\nthis.SuspendLayout();").ToString() + innerText2 + innerText3;
				innerText8 = innerText8.Replace("{namespace}", nameSpace).Replace("{formName}", text).Replace("{entity}", type.FullName)
					.Replace("{tableName}", text2);
				string text6 = Path.Combine(saveFolder, text + ".Designer.cs");
				string text7 = Path.Combine(saveFolder, text + ".cs");
				string text8 = Path.Combine(saveFolder, text + ".resx");
				if ((File.Exists(text6) || File.Exists(text7) || File.Exists(text8)) && !MessageBox.Show("详情文件" + text + "已存在是否覆盖?", "提示", MessageBoxButtons.YesNoCancel).Equals(DialogResult.Yes))
				{
					break;
				}
				File.WriteAllText(text6, contents);
				Console.WriteLine("=========================表:" + text2 + ",详情窗体设计器生成成功,路径：" + text6 + "===========================");
				File.WriteAllText(text7, innerText8);
				Console.WriteLine("=========================表:" + text2 + ",详情窗体代码生成成功,路径：" + text7 + "===========================");
				using (Stream stream = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.detail.resx.info"))
				{
					using FileStream fileStream = new FileStream(text8, FileMode.OpenOrCreate);
					stream.CopyTo((Stream)fileStream);
				}
				Console.WriteLine("=========================表:" + text2 + ",详情窗体资源生成成功,路径：" + text8 + "===========================");
				Console.WriteLine("=========================****************************************************===========================");
			}
		}

		public static void GenListWF<Table, Column>(Type[] types, string nameSpace, string saveFolder, GenConfig genConfig = null)
		{
			BindEnum bindEnum = genConfig.BindEnum;
			ScopeEnum scopeEnum = genConfig.ScopeEnum;
			string tableNameField = genConfig.TableNameField;
			string columnNameField = genConfig.ColumnNameField;
			string columnExistField = genConfig.ColumnExistField;
			string columnCnTextField = genConfig.ColumnCnTextField;
			if (types == null)
			{
				throw new Exception("参数types不得为空");
			}
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			string name = executingAssembly.GetName().Name;
			bool flag = scopeEnum.Equals(ScopeEnum.Field);
			XmlDocument xmlDocument = new XmlDocument();
			XmlReaderSettings xmlReaderSettings = new XmlReaderSettings();
			xmlReaderSettings.IgnoreComments = true;
			xmlReaderSettings.DtdProcessing = DtdProcessing.Ignore;
			using (Stream input = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.list.Designer.xml"))
			{
				XmlReader reader = XmlReader.Create(input, xmlReaderSettings);
				xmlDocument.Load(reader);
			}
			XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDocument.NameTable);
			XmlElement documentElement = xmlDocument.DocumentElement;
			XmlNode xmlNode = documentElement.SelectSingleNode("descendant::head", nsmgr);
			XmlNode xmlNode2 = documentElement.SelectSingleNode("descendant::body", nsmgr);
			XmlNode xmlNode3 = documentElement.SelectSingleNode("descendant::column", nsmgr);
			XmlNode xmlNode4 = documentElement.SelectSingleNode("descendant::columnVar", nsmgr);
			XmlNode xmlNode5 = documentElement.SelectSingleNode("descendant::initColumn", nsmgr);
			bool flag2 = bindEnum.Equals(BindEnum.DB);
			XmlDocument xmlDocument2 = new XmlDocument();
			using (Stream input2 = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.list.code.xml"))
			{
				XmlReader reader2 = XmlReader.Create(input2, xmlReaderSettings);
				xmlDocument2.Load(reader2);
			}
			XmlElement documentElement2 = xmlDocument2.DocumentElement;
			Type typeFromHandle = typeof(Table);
			Type typeFromHandle2 = typeof(Column);
			foreach (Type type in types)
			{
				string innerText = xmlNode.InnerText;
				string innerText2 = xmlNode2.InnerText;
				string innerText3 = xmlNode3.InnerText;
				string innerText4 = xmlNode4.InnerText;
				string innerText5 = xmlNode5.InnerText;
				string innerText6 = documentElement2.InnerText;
				StringBuilder stringBuilder = new StringBuilder();
				StringBuilder stringBuilder2 = new StringBuilder();
				StringBuilder stringBuilder3 = new StringBuilder();
				StringBuilder stringBuilder4 = new StringBuilder();
				nameSpace = (string.IsNullOrWhiteSpace(nameSpace) ? type.Namespace : nameSpace);
				string name2 = type.Name;
				string text = "Form" + name2 + "List";
				Table val = type.GetCustomAttribute<Table>();
				if (val == null)
				{
					ConstructorInfo[] constructors = typeFromHandle.GetConstructors();
					val = (Table)((constructors != null) ? constructors[0].Invoke(new object[1] { "" }) : null);
				}
				string text2 = Convert.ToString(typeFromHandle.GetProperty(tableNameField)?.GetValue(val) ?? name2);
				List<MemberInfo> list;
				if (flag)
				{
					FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
					list = new List<MemberInfo>(fields);
				}
				else
				{
					PropertyInfo[] properties = type.GetProperties();
					list = new List<MemberInfo>(properties);
				}
				foreach (MemberInfo item in list)
				{
					Type type2 = (item as FieldInfo)?.FieldType ?? (item as PropertyInfo).PropertyType;
					if (type2 is IEnumerable)
					{
						continue;
					}
					Column val2 = item.GetCustomAttribute<Column>();
					if (val2 != null || !genConfig.NeedColumAttribute)
					{
						if (val2 == null)
						{
							ConstructorInfo[] constructors2 = typeFromHandle2.GetConstructors();
							val2 = (Column)((constructors2 != null) ? constructors2[0].Invoke(new object[1] { "" }) : null);
						}
						bool flag3 = Convert.ToBoolean(typeFromHandle2.GetProperty(columnExistField)?.GetValue(val2) ?? ((object)true));
						string text3 = Convert.ToString(typeFromHandle2.GetProperty(columnNameField)?.GetValue(val2) ?? item.Name);
						string newValue = Convert.ToString(typeFromHandle2.GetProperty(columnCnTextField)?.GetValue(val2) ?? text3);
						string name3 = item.Name;
						string newValue2 = (flag2 ? text3 : item.Name);
						if (flag3)
						{
							string text4 = innerText3.Replace("{columnName}", text3).Replace("{dataPropName}", newValue2).Replace("{headerText}", newValue);
							stringBuilder.Append(text4 + "\r\n");
							string text5 = innerText5.Replace("{columnName}", text3);
							stringBuilder2.Append(text5 + "\r\n");
							stringBuilder3.Append("," + text3);
							string text6 = innerText4.Replace("{columnName}", text3);
							stringBuilder4.Append(text6 + "\r\n");
						}
					}
				}
				innerText = innerText.Replace("{namespace}", nameSpace).Replace("{formName}", text).Replace("&amp;&amp;", "&&")
					.Replace("{initColumns}", stringBuilder2.ToString())
					.Replace("{columns}", stringBuilder3.ToString())
					.Replace("{columnsContent}", stringBuilder.ToString());
				innerText2 = innerText2.Replace("{bodyContent}", stringBuilder4.ToString());
				string contents = innerText + innerText2;
				innerText6 = innerText6.Replace("{namespace}", nameSpace).Replace("{formName}", text).Replace("{entity}", type.FullName)
					.Replace("{tableName}", text2)
					.Replace("{className}", name2);
				string text7 = Path.Combine(saveFolder, text + ".Designer.cs");
				string text8 = Path.Combine(saveFolder, text + ".cs");
				string text9 = Path.Combine(saveFolder, text + ".resx");
				if ((File.Exists(text7) || File.Exists(text8) || File.Exists(text9)) && !MessageBox.Show("列表文件" + text + "已存在是否覆盖?", "提示", MessageBoxButtons.YesNoCancel).Equals(DialogResult.Yes))
				{
					break;
				}
				File.WriteAllText(text7, contents);
				Console.WriteLine("=========================表:" + text2 + ",详情窗体设计器生成成功,路径：" + text7 + "===========================");
				File.WriteAllText(text8, innerText6);
				Console.WriteLine("=========================表:" + text2 + ",详情窗体代码生成成功,路径：" + text8 + "===========================");
				using (Stream stream = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.detail.resx.info"))
				{
					using FileStream fileStream = new FileStream(text9, FileMode.OpenOrCreate);
					stream.CopyTo((Stream)fileStream);
				}
				Console.WriteLine("=========================表:" + text2 + ",详情窗体资源生成成功,路径：" + text9 + "===========================");
				Console.WriteLine("=========================****************************************************===========================");
			}
		}

		public static void GenCreateTableSql<Table, Column, PrimaryKey>(Type[] types, string folder, GenConfig genConfig = null)
		{
			DataBaseEnum dataBaseEnum = genConfig.DataBaseEnum;
			ScopeEnum scopeEnum = genConfig.ScopeEnum;
			string tableNameField = genConfig.TableNameField;
			string columnNameField = genConfig.ColumnNameField;
			string columnExistField = genConfig.ColumnExistField;
			string columnCnTextField = genConfig.ColumnCnTextField;
			if (types == null)
			{
				throw new Exception("参数types不得为空");
			}
			string text = "id";
			Dictionary<string, string> dbTypeDict = null;
			string text2;
			string text3;
			switch (dataBaseEnum)
			{
			case DataBaseEnum.Mysql:
				text2 = "mysql";
				text3 = " NOT NULL AUTO_INCREMENT ";
				dbTypeDict = DbColumnConfig.DbTypeDictByMysql;
				break;
			case DataBaseEnum.Sqlite:
				text2 = "sqlite";
				text3 = " NOT NULL PRIMARY KEY ";
				dbTypeDict = DbColumnConfig.DbTypeDictBySqlite;
				break;
			case DataBaseEnum.Oracle:
				text2 = "oracle";
				text3 = "";
				break;
			default:
				text2 = "sqlite";
				text3 = " NOT NULL PRIMARY KEY ";
				dbTypeDict = DbColumnConfig.DbTypeDictBySqlite;
				break;
			}
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			string name = executingAssembly.GetName().Name;
			bool flag = scopeEnum.Equals(ScopeEnum.Field);
			XmlDocument xmlDocument = new XmlDocument();
			XmlReaderSettings xmlReaderSettings = new XmlReaderSettings();
			xmlReaderSettings.IgnoreComments = true;
			xmlReaderSettings.DtdProcessing = DtdProcessing.Ignore;
			using (Stream input = executingAssembly.GetManifestResourceStream(name + ".codeGenTemps.create.table.xml"))
			{
				XmlReader reader = XmlReader.Create(input, xmlReaderSettings);
				xmlDocument.Load(reader);
			}
			XmlNamespaceManager nsmgr = new XmlNamespaceManager(xmlDocument.NameTable);
			XmlElement documentElement = xmlDocument.DocumentElement;
			XmlNode xmlNode = documentElement.SelectSingleNode("descendant::body[@id='" + text2 + "']", nsmgr);
			XmlNode xmlNode2 = documentElement.SelectSingleNode("descendant::column[@id='" + text2 + "']", nsmgr);
			XmlNode xmlNode3 = documentElement.SelectSingleNode("descendant::primaryKey[@id='" + text2 + "']", nsmgr);
			Type typeFromHandle = typeof(Table);
			Type typeFromHandle2 = typeof(Column);
			foreach (Type type in types)
			{
				string innerText = xmlNode.InnerText;
				string text4 = xmlNode2.InnerText.Trim();
				string innerText2 = xmlNode3.InnerText;
				StringBuilder stringBuilder = new StringBuilder();
				string name2 = type.Name;
				Table val = type.GetCustomAttribute<Table>();
				if (val == null)
				{
					ConstructorInfo[] constructors = typeFromHandle.GetConstructors();
					val = (Table)((constructors != null) ? constructors[0].Invoke(new object[1] { "" }) : null);
				}
				string text5 = Convert.ToString(typeFromHandle.GetProperty(tableNameField)?.GetValue(val) ?? name2);
				List<MemberInfo> list;
				if (flag)
				{
					FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
					list = new List<MemberInfo>(fields);
				}
				else
				{
					PropertyInfo[] properties = type.GetProperties();
					list = new List<MemberInfo>(properties);
				}
				foreach (MemberInfo item in list)
				{
					Type type2 = (item as FieldInfo)?.FieldType ?? (item as PropertyInfo).PropertyType;
					if (type2 is IEnumerable)
					{
						continue;
					}
					Column val2 = item.GetCustomAttribute<Column>();
					if (val2 != null || !genConfig.NeedColumAttribute)
					{
						if (val2 == null)
						{
							ConstructorInfo[] constructors2 = typeFromHandle2.GetConstructors();
							val2 = (Column)((constructors2 != null) ? constructors2[0].Invoke(new object[1] { "" }) : null);
						}
						PrimaryKey customAttribute = item.GetCustomAttribute<PrimaryKey>();
						bool flag2 = false;
						flag2 = ((customAttribute == null && text.Equals(item.Name, StringComparison.CurrentCultureIgnoreCase)) ? true : false);
						if (flag2)
						{
							text = item.Name;
						}
						bool flag3 = Convert.ToBoolean(typeFromHandle2.GetProperty(columnExistField)?.GetValue(val2) ?? ((object)true));
						string text6 = Convert.ToString(typeFromHandle2.GetProperty(columnNameField)?.GetValue(val2) ?? item.Name);
						string newValue = Convert.ToString(typeFromHandle2.GetProperty(columnCnTextField)?.GetValue(val2) ?? text6);
						if (flag3)
						{
							string text7 = text4.Replace("{columnName}", text6).Replace("{dataType}", GetDbType(dbTypeDict, item)).Replace("{COMMENT}", newValue)
								.Replace("{isPrimaryKey}", flag2 ? text3 : "");
							stringBuilder.Append(text7 + ",\r\n");
						}
					}
				}
				stringBuilder.Append(innerText2.Replace("{columnName}", text));
				string text8 = stringBuilder.ToString().Trim();
				text8 = (text8.EndsWith(",") ? text8.Substring(0, text8.Length - 1) : text8);
				string text9 = "--------创建时间:----" + DateTime.Now.ToString("yyyy-MM-dd HH:mm") + "----------------\r\n";
				text9 += innerText.Replace("{bodyContent}", text8).Replace("{tableName}", text5);
				string text10 = folder + "\\gen";
				string text11 = Path.Combine(text10, DateTime.Now.ToString("yyyyMM") + ".sql");
				Directory.CreateDirectory(text10);
				if (File.Exists(text11) && !MessageBox.Show("文件（" + Path.GetFileName(text11) + "）已存在是否覆盖?", "提示", MessageBoxButtons.YesNoCancel).Equals(DialogResult.Yes))
				{
					break;
				}
				File.AppendAllText(text11, text9);
				Console.WriteLine("=========================表:" + text5 + ",建表语句生成成功,路径：" + text11 + "===========================");
			}
		}

		private static string GetDbType(Dictionary<string, string> dbTypeDict, MemberInfo item)
		{
			if (item.MemberType.Equals(MemberTypes.Field))
			{
				string name = (item as FieldInfo).FieldType.Name;
				return dbTypeDict[name];
			}
			if (item.MemberType.Equals(MemberTypes.Property))
			{
				string name2 = (item as PropertyInfo).PropertyType.Name;
				return dbTypeDict[name2];
			}
			return "";
		}

		public static void GenContent<Table, Column>(Type type, string nameSpace, GenConfig genConfig, Action<MyTable> WriteAction)
		{
			BindEnum bindEnum = genConfig.BindEnum;
			ScopeEnum scopeEnum = genConfig.ScopeEnum;
			string tableNameField = genConfig.TableNameField;
			string columnNameField = genConfig.ColumnNameField;
			string columnExistField = genConfig.ColumnExistField;
			string columnCnTextField = genConfig.ColumnCnTextField;
			if (type == (Type)null)
			{
				throw new Exception("参数type不得为空");
			}
			Assembly executingAssembly = Assembly.GetExecutingAssembly();
			bool flag = scopeEnum.Equals(ScopeEnum.Field);
			bool flag2 = bindEnum.Equals(BindEnum.DB);
			Type typeFromHandle = typeof(Table);
			Type typeFromHandle2 = typeof(Column);
			nameSpace = (string.IsNullOrWhiteSpace(nameSpace) ? type.Namespace : nameSpace);
			string name = type.Name;
			Table val = type.GetCustomAttribute<Table>();
			if (val == null)
			{
				ConstructorInfo[] constructors = typeFromHandle.GetConstructors();
				val = (Table)((constructors != null) ? constructors[0].Invoke(new object[1] { "" }) : null);
			}
			string tableName = Convert.ToString(typeFromHandle.GetProperty(tableNameField)?.GetValue(val) ?? name);
			if (cacheDict.TryGetValue(type, out var value))
			{
				WriteAction?.Invoke(value);
				return;
			}
			List<MemberInfo> list = new List<MemberInfo>();
			FieldInfo[] fields = type.GetFields(BindingFlags.Instance | BindingFlags.Static | BindingFlags.NonPublic);
			PropertyInfo[] properties = type.GetProperties();
			list.AddRange(fields);
			list.AddRange(properties);
			MyTable myTable = new MyTable(name, tableName, nameSpace, new List<MyColumn>());
			cacheDict.Add(type, myTable);
			foreach (MemberInfo item in list)
			{
				Type type2 = (item as FieldInfo)?.FieldType ?? (item as PropertyInfo).PropertyType;
				if (type2 is IEnumerable)
				{
					continue;
				}
				Column val2 = item.GetCustomAttribute<Column>();
				if (val2 != null || !genConfig.NeedColumAttribute)
				{
					if (val2 == null)
					{
						ConstructorInfo[] constructors2 = typeFromHandle2.GetConstructors();
						val2 = (Column)((constructors2 != null) ? constructors2[0].Invoke(new object[1] { "" }) : null);
					}
					bool flag3 = Convert.ToBoolean(typeFromHandle2.GetProperty(columnExistField)?.GetValue(val2) ?? ((object)true));
					string text = Convert.ToString(typeFromHandle2.GetProperty(columnNameField)?.GetValue(val2) ?? item.Name);
					string cnText = Convert.ToString(typeFromHandle2.GetProperty(columnCnTextField)?.GetValue(val2) ?? text);
					string name2 = item.Name;
					string bindName = (flag2 ? text : (char.ToUpper(item.Name[0]) + item.Name.Substring(1)));
					if (flag3)
					{
						cacheDict[type].MyColumns.Add(new MyColumn(text, cnText, name2, bindName));
					}
				}
			}
			WriteAction?.Invoke(myTable);
		}
	}
	public enum BindEnum
	{
		DB,
		Entity
	}
	public enum ScopeEnum
	{
		Field,
		Property
	}
	public enum DataBaseEnum
	{
		Mysql,
		Sqlite,
		Oracle
	}
	internal class UnitTest
	{
	}
}
namespace CodeGenForWF.extendmethods
{
	public static class MyTypeExtend
	{
		public static T GetCustomAttribute<T>(this Type type)
		{
			object[] customAttributes = type.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static T GetCustomAttribute<T>(this MemberInfo member)
		{
			object[] customAttributes = member.GetCustomAttributes(typeof(T), inherit: false);
			return (T)((customAttributes.Length != 0) ? customAttributes[0] : null);
		}

		public static object GetValue<T>(this PropertyInfo prop, T t)
		{
			return prop.GetValue(t, null);
		}

		public static void SetValue<T>(this PropertyInfo prop, T t, object content)
		{
			prop.SetValue(t, content, null);
		}
	}
}
namespace CodeGenForWF.config
{
	public class DbColumnConfig
	{
		public static Dictionary<string, string> DbTypeDictBySqlite { get; private set; }

		public static Dictionary<string, string> DbTypeDictByMysql { get; private set; }

		static DbColumnConfig()
		{
			DbTypeDictBySqlite = new Dictionary<string, string>();
			DbTypeDictBySqlite.Add("String", "TEXT");
			DbTypeDictBySqlite.Add("int", "INT(10)");
			DbTypeDictBySqlite.Add("Int", "INT(10)");
			DbTypeDictBySqlite.Add("Int32", "int(10)");
			DbTypeDictBySqlite.Add("Int64", "int(10)");
			DbTypeDictBySqlite.Add("long", "INT(10)");
			DbTypeDictBySqlite.Add("Long", "INT(10)");
			DbTypeDictBySqlite.Add("bool", "INT(1)");
			DbTypeDictBySqlite.Add("Boolean", "INT(1)");
			DbTypeDictBySqlite.Add("float", "DECIMAL(12,2)");
			DbTypeDictBySqlite.Add("double", "DECIMAL(12,2)");
			DbTypeDictBySqlite.Add("Double", "DECIMAL(12,2)");
			DbTypeDictBySqlite.Add("DateTime", "datetime");
			DbTypeDictBySqlite.Add("Decimal", "DECIMAL(30,10) ");
			DbTypeDictByMysql = new Dictionary<string, string>();
			DbTypeDictByMysql.Add("String", "varchar(255)");
			DbTypeDictByMysql.Add("int", "int(10)");
			DbTypeDictByMysql.Add("Int", "int(10)");
			DbTypeDictByMysql.Add("Int32", "int(10)");
			DbTypeDictByMysql.Add("Int64", "int(10)");
			DbTypeDictByMysql.Add("long", "bigint(10)");
			DbTypeDictByMysql.Add("Long", "bigint(10)");
			DbTypeDictByMysql.Add("bool", "int(1)");
			DbTypeDictByMysql.Add("Boolean", "int(1)");
			DbTypeDictByMysql.Add("float", "decimal(12,2)");
			DbTypeDictByMysql.Add("double", "decimal(12,2)");
			DbTypeDictByMysql.Add("Double", "decimal(12,2)");
		}
	}
	public class GenConfig
	{
		private DataBaseEnum dataBaseEnum = DataBaseEnum.Sqlite;

		private BindEnum bindEnum = BindEnum.DB;

		private ScopeEnum scopeEnum = ScopeEnum.Field;

		private string tableNameField = "TableName";

		private string columnNameField = "ColumnName";

		private string columnExistField = "IsExist";

		private string columnCnTextField = "Cn_Text";

		private bool needColumAttribute = true;

		public BindEnum BindEnum
		{
			get
			{
				return bindEnum;
			}
			set
			{
				bindEnum = value;
			}
		}

		public ScopeEnum ScopeEnum
		{
			get
			{
				return scopeEnum;
			}
			set
			{
				scopeEnum = value;
			}
		}

		public DataBaseEnum DataBaseEnum
		{
			get
			{
				return dataBaseEnum;
			}
			set
			{
				dataBaseEnum = value;
			}
		}

		public string TableNameField
		{
			get
			{
				return tableNameField;
			}
			set
			{
				tableNameField = value;
			}
		}

		public string ColumnNameField
		{
			get
			{
				return columnNameField;
			}
			set
			{
				columnNameField = value;
			}
		}

		public string ColumnExistField
		{
			get
			{
				return columnExistField;
			}
			set
			{
				columnExistField = value;
			}
		}

		public string ColumnCnTextField
		{
			get
			{
				return columnCnTextField;
			}
			set
			{
				columnCnTextField = value;
			}
		}

		public bool NeedColumAttribute
		{
			get
			{
				return needColumAttribute;
			}
			set
			{
				needColumAttribute = value;
			}
		}
	}
}
