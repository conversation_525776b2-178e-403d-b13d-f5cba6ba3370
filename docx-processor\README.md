# Word文档处理器

这是一个用于处理Word文档的Python项目，主要功能包括：

## 功能特性

1. **表格填充**：自动填充Word文档中的表格内容
2. **文档拆分**：将处理后的文档拆分为多个独立文档
3. **批量处理**：支持批量处理多个文档
4. **智能命名**：根据项目ID自动生成文件名

## 安装和使用

### 环境要求

- Python 3.8+
- uv (用于依赖管理)

### 安装依赖

```bash
uv sync
```

### 使用方法

1. 配置输入目录路径
2. 运行处理脚本：

```bash
uv run python main.py
```

## 项目结构

```
docx-processor/
├── src/
│   ├── __init__.py
│   ├── processor.py     # 核心处理逻辑
│   └── utils.py         # 工具函数
├── tests/
│   └── test_processor.py
├── main.py              # 主程序入口
├── pyproject.toml       # 项目配置
└── README.md           # 项目说明
```

## 处理流程

1. **读取原始文档**：从指定目录读取Word文档
2. **填充表格内容**：
   - 表一、表二：自动填充"(投标人填写)"为左侧单元格的值
   - 表三：根据表一的映射关系自动填充
3. **保存处理后的文档**：按指定格式重命名并保存
4. **拆分文档**：
   - 技术参数特征表：包含表一、表二
   - 组件材料配置表：包含表三

## 输出文件命名规则

- 主文档：`{项目ID}.docx`
- 技术参数表：`{项目ID}-技术参数特征表.docx`
- 组件配置表：`{项目ID}-组件材料配置表.docx`

## 注意事项

1. 确保输入文档格式正确
2. 项目ID从文件夹名称中提取
3. 处理过程中会创建备份文件
## 目前的难题
1.新生成的文档格式混乱，丢失
2.表格三里的内容没有被正确填充