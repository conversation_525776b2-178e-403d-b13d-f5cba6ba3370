using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Data.SQLite;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Serialization;
using CommonApi.Util;
using Newtonsoft.Json;
using SharpCompress.Archives;
using SharpCompress.Common;
using SharpCompress.Readers;
using online_bid_autoUpdate.Properties;
using online_bid_autoUpdate.dto;
using online_bid_autoUpdate.util;
using updated_version.dto;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: AssemblyTitle("updated_version")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("updated_version")]
[assembly: AssemblyCopyright("Copyright ©  2020")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("136f1cac-c7ad-4e7a-a1db-679b6d01a170")]
[assembly: AssemblyFileVersion("*******")]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyVersion("*******")]
namespace updated_version.dto
{
	public class UpdateFileDto
	{
		public string FullPath { get; set; }

		public string FileName { get; set; }

		public bool IsDirectory { get; set; }

		public bool IsExist { get; set; }
	}
	public class ZbToolVersionInfo
	{
		[JsonProperty("id")]
		public string Id { get; set; }

		[JsonProperty("fileName")]
		public string FileName { get; set; }

		[JsonProperty("filePath")]
		public string FilePath { get; set; }

		[JsonProperty("versionNo")]
		public string VersionNo { get; set; }

		[JsonProperty("versionV")]
		public string VersionV { get; set; }

		[JsonProperty("remark")]
		public string Remark { get; set; }

		[JsonProperty("ext1")]
		public string Ext1 { get; set; }

		[JsonProperty("ext2")]
		public string Ext2 { get; set; }

		[JsonProperty("createTime")]
		public string CreateTime { get; set; }

		[JsonProperty("createUser")]
		public string CreateUser { get; set; }

		[JsonProperty("updateTime")]
		public string UpdateTime { get; set; }

		[JsonProperty("updateUser")]
		public string UpdateUser { get; set; }
	}
}
namespace online_bid_autoUpdate
{
	public class AutoUpdateFrm2 : Form
	{
		private delegate void AsynUpdateUI(int step);

		private static string upgradeDb = ApiConfigHelper.GetValueByKey("upgradeDb");

		private static string sqliteDbName = ApiConfigHelper.GetValueByKey("sqliteDBName");

		private static string mainexe = ConfigurationManager.AppSettings["mainexe"].ToString();

		private string zip = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "updateFile.zip");

		public static string onlineVersion = ApiConfigHelper.GetValueByKey("onlineVersion");

		public static string dbLoaction = ApiConfigHelper.GetValueByKey("sqliteDBLocation");

		public static string dbName = ApiConfigHelper.GetValueByKey("sqliteDBName");

		private int process = 0;

		private IContainer components = null;

		private ProgressBar progressBarUpdate;

		private Label lblPercent;

		private Button btnAllCheckedCancle;

		private Button btnAllCheck;

		private Button buttonOver;

		private System.Windows.Forms.Timer timer1;

		private Label labelProcess;

		public AutoUpdateFrm2()
		{
			InitializeComponent();
			Control.CheckForIllegalCrossThreadCalls = false;
			base.ControlBox = false;
			base.ShowInTaskbar = false;
		}

		private void AutoUpdateFrm2_Load(object sender, EventArgs e)
		{
			//IL_0069: Unknown result type (might be due to invalid IL or missing references)
			//IL_0073: Expected O, but got Unknown
			Thread.Sleep(3000);
			Process[] processesByName = Process.GetProcessesByName("bid-anhui");
			if (processesByName.Length != 0)
			{
				Process[] array = processesByName;
				foreach (Process process in array)
				{
					process.Kill();
				}
			}
			progressBarUpdate.Minimum = 0;
			progressBarUpdate.Maximum = 100;
			Task.Factory.StartNew((Action)delegate
			{
				ZipToCompress();
			});
		}

		private void CheckVersion()
		{
		}

		private void Process_Tick(object sender, EventArgs e)
		{
			process += 5;
			if (process > 94)
			{
				process = 99;
			}
			else
			{
				process += 3;
			}
			labelProcess.Text = process + "%";
			progressBarUpdate.Value = process;
		}

		private void btnAllCheckedCancle_Click(object sender, EventArgs e)
		{
			Hide();
			Close();
		}

		private void btnAllCheck_Click(object sender, EventArgs e)
		{
			ZipToCompress();
		}

		public void ZipToCompress()
		{
			//IL_03a3: Unknown result type (might be due to invalid IL or missing references)
			//IL_03a8: Unknown result type (might be due to invalid IL or missing references)
			//IL_03ab: Expected O, but got Unknown
			//IL_03b0: Expected O, but got Unknown
			//IL_0374->IL0374: Incompatible stack heights: 0 vs 1
			string text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "updateFile.zip");
			string text2 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database\\update-" + upgradeDb);
			string text3 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database\\zbfz.sqlite");
			ZbToolVersionInfo zbToolVersionInfo = null;
			try
			{
				try
				{
					string text4 = ApiConfigHelper.GetValueByKey("dataApiPrefixUrl") + onlineVersion;
					LogsHelper.addLog("在线版本url：" + text4);
					Result result = WebRequestUtil.PostBasicByUrl(text4, null, 10000);
					if (result.Code == 0 && result.Successful)
					{
						StringBuilder stringBuilder = new StringBuilder("CREATE TABLE IF NOT EXISTS ZB_TOOL_VERSION_INFO(");
						stringBuilder.Append(" ID TEXT PRIMARY KEY NOT NULL,");
						stringBuilder.Append("  FILE_NAME TEXT, FILE_PATH TEXT,");
						stringBuilder.Append("   VERSION_NO TEXT, VERSION_V TEXT,REMARK TEXT, ");
						stringBuilder.Append("   EXT1 TEXT,EXT2 TEXT,CREATE_TIME TEXT, CREATE_USER TEXT, ");
						stringBuilder.Append("   UPDATE_TIME TEXT,UPDATE_USER TEXT );");
						SQLiteLibrary.ExecuteSqlByTransaction(dbLoaction, dbName, new string[1] { stringBuilder.ToString() });
						zbToolVersionInfo = JsonConvert.DeserializeObject<ZbToolVersionInfo>(result.ResultValue.ToString());
					}
				}
				catch (Exception ex)
				{
					LogsHelper.AddErrorLog(ex.StackTrace);
				}
				if (!File.Exists(text))
				{
					Close();
					return;
				}
				string directoryName = Path.GetDirectoryName(text);
				string text5 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dbLoaction, dbName);
				if (File.Exists(text5))
				{
					File.Copy(text5, Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dbLoaction, "数据备份.db"), overwrite: true);
				}
				ReaderOptions readerOptions = new ReaderOptions();
				_ = ((OptionsBase)readerOptions).ArchiveEncoding;
				ArchiveEncoding.Default = Encoding.GetEncoding("GBK");
				IArchive archive = null;
				try
				{
					archive = ArchiveFactory.Open(text, readerOptions);
					int totals = archive.Entries.Count();
					int currentCount = 0;
					Action val = default(Action);
					foreach (IArchiveEntry entry in archive.Entries)
					{
						string key = entry.Key;
						if (entry.IsDirectory)
						{
							entry.WriteToDirectory(Path.Combine(directoryName, key), new ExtractionOptions
							{
								Overwrite = false
							});
						}
						else
						{
							string text6 = Path.Combine(directoryName, key);
							string directoryName2 = Path.GetDirectoryName(text6);
							if (!Directory.Exists(directoryName2))
							{
								Directory.CreateDirectory(directoryName2);
							}
							if (text6.Contains("fujian") || text6.Contains("System.Data.SQLite") || text6.Contains("CommonApi.dll"))
							{
								continue;
							}
							if (text6.EndsWith("database/" + upgradeDb))
							{
								text6 = Path.Combine(directoryName2, "update-" + Path.GetFileName(text6));
							}
							try
							{
								if (File.Exists(text6))
								{
									new FileInfo(text6).IsReadOnly = false;
									File.Delete(text6);
								}
								entry.WriteToFile(text6, new ExtractionOptions
								{
									Overwrite = true
								});
							}
							catch (Exception ex2)
							{
								LogsHelper.AddErrorLog(key + "文件被占用," + ex2.StackTrace, "用户更新操作");
								throw ex2;
							}
						}
						currentCount++;
						ProgressBar progressBar = progressBarUpdate;
						Action obj = val;
						if (obj == null)
						{
							Action val2 = delegate
							{
								int num = currentCount * 100 / totals;
								num = ((num >= 100) ? 99 : num);
								progressBarUpdate.Value = num;
								labelProcess.Text = $"{num}%";
							};
							Action val3 = val2;
							val = val2;
							obj = val3;
						}
						progressBar.Invoke((Delegate)(object)obj);
					}
				}
				catch (Exception ex3)
				{
					throw ex3;
				}
				finally
				{
					archive?.Dispose();
				}
				if (File.Exists(text2) && File.Exists(text3))
				{
					SQLiteMerge(text3, text2);
				}
				if (zbToolVersionInfo != null)
				{
					string text7 = "delete from ZB_TOOL_VERSION_INFO";
					string text8 = "insert into ZB_TOOL_VERSION_INFO(ID,FILE_NAME, FILE_PATH,VERSION_NO,VERSION_V,CREATE_TIME) VALUES('" + Guid.NewGuid().ToString("N") + "','" + zbToolVersionInfo.FileName + "','" + zbToolVersionInfo.FilePath + "','" + zbToolVersionInfo.VersionNo + "','" + zbToolVersionInfo.VersionV + "',datetime('now', 'localtime'))";
					SQLiteLibrary.ExecuteSqlByTransaction(dbLoaction, dbName, new string[2] { text7, text8 });
					LogsHelper.addLog("本地软件版本已同步成功，版本号：" + zbToolVersionInfo.VersionV);
				}
				else
				{
					LogsHelper.AddErrorLog("线上版本获取异常。");
				}
				try
				{
					File.Delete(text);
				}
				catch (Exception)
				{
				}
				labelProcess.Text = "100%";
				progressBarUpdate.Value = 100;
				buttonOver.Visible = true;
			}
			catch (Exception ex5)
			{
				base.ControlBox = false;
				MessageBox.Show("更新异常:" + ex5.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Hand);
				Close();
			}
			Thread.Sleep(2000);
			buttonOver.Visible = true;
			buttonOver.Show();
			MessageBox.Show("更新成功!请重启主程序!");
			StartMainExe();
		}

		private void buttonOver_Click(object sender, EventArgs e)
		{
			Close();
		}

		private void AutoUpdateFrm2_FormClosing(object sender, FormClosingEventArgs e)
		{
			Process[] processesByName = Process.GetProcessesByName("bid_update");
			Process[] array = processesByName;
			int num = 0;
			if (num < array.Length)
			{
				Process process = array[num];
				process.Kill();
			}
			else
			{
				Application.Exit();
			}
		}

		private void StartMainExe()
		{
			Application.Exit();
		}

		public static void WriteContent(string content)
		{
			string path = AppDomain.CurrentDomain.BaseDirectory + "version.info.txt";
			if (!File.Exists(path))
			{
				FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write);
				StreamWriter streamWriter = new StreamWriter(fileStream);
				streamWriter.Write(content);
				streamWriter.Close();
				fileStream.Close();
			}
			else
			{
				FileStream fileStream2 = new FileStream(path, FileMode.Open, FileAccess.Write);
				StreamWriter streamWriter2 = new StreamWriter(fileStream2);
				streamWriter2.Write(content);
				streamWriter2.Close();
				fileStream2.Close();
			}
		}

		public static string GetContentByTxt()
		{
			StringBuilder stringBuilder = new StringBuilder();
			StreamReader streamReader = null;
			try
			{
				string path = AppDomain.CurrentDomain.BaseDirectory + "version.info.txt";
				if (File.Exists(path))
				{
					streamReader = new StreamReader(path, Encoding.Default);
					string value;
					while ((value = streamReader.ReadLine()) != null)
					{
						stringBuilder.Append(value);
					}
				}
			}
			finally
			{
				streamReader?.Close();
			}
			return stringBuilder.ToString();
		}

		private void SQLiteMerge(string oldDB, string newDB)
		{
			try
			{
				StringBuilder stringBuilder = new StringBuilder();
				string directoryName = Path.GetDirectoryName(oldDB);
				string fileName = Path.GetFileName(oldDB);
				string directoryName2 = Path.GetDirectoryName(newDB);
				string fileName2 = Path.GetFileName(newDB);
				string selectSql = "SELECT name  FROM sqlite_master WHERE type='table' and name!='sqlite_sequence'";
				DataTable dataTable = SQLiteLibrary.selectDataBySql(directoryName2, fileName2, selectSql);
				DataTable dataTable2 = SQLiteLibrary.selectDataBySql(directoryName, fileName, selectSql);
				Dictionary<string, string> dictionary = new Dictionary<string, string>();
				Dictionary<string, List<ColumInfo>> dictionary2 = new Dictionary<string, List<ColumInfo>>();
				Dictionary<string, List<ColumInfo>> dictionary3 = new Dictionary<string, List<ColumInfo>>();
				for (int i = 0; i < dataTable2.Rows.Count; i++)
				{
					string selectSql2 = string.Format("PRAGMA  table_info('{0}');", dataTable2.Rows[i]["name"]);
					DataTable dt = SQLiteLibrary.selectDataBySql(directoryName, fileName, selectSql2);
					List<ColumInfo> value = dt.ToDataList<ColumInfo>();
					dictionary.Add(dataTable2.Rows[i]["name"].ToString(), dataTable2.Rows[i]["name"].ToString());
					dictionary2.Add(dataTable2.Rows[i]["name"].ToString(), value);
				}
				for (int j = 0; j < dataTable.Rows.Count; j++)
				{
					Dictionary<string, string> dictionary4 = new Dictionary<string, string>();
					string selectSql3 = string.Format("PRAGMA  table_info('{0}');", dataTable.Rows[j]["name"]);
					DataTable dt = SQLiteLibrary.selectDataBySql(directoryName2, fileName2, selectSql3);
					List<ColumInfo> value = dt.ToDataList<ColumInfo>();
					dictionary3.Add(dataTable.Rows[j]["name"].ToString(), value);
					if (!dictionary.ContainsKey(dataTable.Rows[j]["name"].ToString()))
					{
						stringBuilder.Append(string.Format("CREATE TABLE {0}(", dataTable.Rows[j]["name"]));
						for (int k = 0; k < value.Count; k++)
						{
							string text = ((value[k].Pk == "1") ? " PRIMARY KEY " : "");
							string text2 = ((value[k].Notnull == "1") ? " NOT NULL " : "");
							string text3 = ((k == value.Count - 1) ? "" : ",");
							stringBuilder.Append(" " + value[k].Name + " " + value[k].Type + text + text2 + text3 + "\n");
						}
						stringBuilder.Append(");\n");
					}
					else
					{
						if (!dictionary.ContainsKey(dataTable.Rows[j]["name"].ToString()))
						{
							continue;
						}
						for (int l = 0; l < dictionary2[dataTable.Rows[j]["name"].ToString()].Count; l++)
						{
							dictionary4.Add(dictionary2[dataTable.Rows[j]["name"].ToString()][l].Name, "");
						}
						List<ColumInfo> list = dictionary3[dataTable.Rows[j]["name"].ToString()];
						for (int m = 0; m < list.Count; m++)
						{
							if (!dictionary4.ContainsKey(list[m].Name))
							{
								stringBuilder.Append(string.Format("ALTER TABLE {0} ADD COLUMN {1} {2};\n", dataTable.Rows[j]["name"], list[m].Name, list[m].Type));
							}
						}
					}
				}
				if (stringBuilder.Length > 0)
				{
					SQLiteLibrary.ExecuteSqlByTransaction(directoryName, fileName, new string[1] { stringBuilder.ToString() });
					Console.WriteLine("数据库更新成功");
					Console.WriteLine("更新的sql:" + stringBuilder.ToString());
					AddLog("数据库更新成功");
					AddLog("更新的sql:" + stringBuilder.ToString());
				}
				else
				{
					Console.WriteLine("数据库不存在更新的表和字段");
					AddLog("数据库不存在更新的表和字段");
				}
			}
			catch (Exception)
			{
				throw;
			}
		}

		public static void AddLog(string str, string name = "当前用户")
		{
			string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
			string path = AppDomain.CurrentDomain.BaseDirectory + "wcfLog.txt";
			if (!File.Exists(path))
			{
				FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write);
				StreamWriter streamWriter = new StreamWriter(fileStream);
				long length = fileStream.Length;
				fileStream.Seek(length, SeekOrigin.End);
				streamWriter.WriteLine("时间 :" + text + "   操作员姓名:" + name + "        操作:" + str + "\n");
				streamWriter.Close();
				fileStream.Close();
			}
			else
			{
				FileStream fileStream2 = new FileStream(path, FileMode.Open, FileAccess.Write);
				StreamWriter streamWriter2 = new StreamWriter(fileStream2);
				long length2 = fileStream2.Length;
				fileStream2.Seek(length2, SeekOrigin.Begin);
				streamWriter2.WriteLine("时间 :" + text + "   操作员姓名:" + name + "        操作:" + str + "\n");
				streamWriter2.Close();
				fileStream2.Close();
			}
		}

		public void ChangeVersion()
		{
			try
			{
				string text = ConfigurationManager.AppSettings["version"].ToString();
				string contentByTxt = GetContentByTxt();
				Configuration configuration = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
				text = ((!string.IsNullOrEmpty(contentByTxt)) ? contentByTxt : text);
				double num = Convert.ToDouble(Regex.Replace(text, "[^\\d\\.]", ""));
				num += 0.1;
				contentByTxt = Convert.ToString(num);
				configuration.AppSettings.Settings["version"].Value = contentByTxt;
				configuration.Save(ConfigurationSaveMode.Modified);
				ConfigurationManager.RefreshSection("appSettings");
				WriteContent(contentByTxt);
			}
			catch (Exception)
			{
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			this.progressBarUpdate = new System.Windows.Forms.ProgressBar();
			this.lblPercent = new System.Windows.Forms.Label();
			this.btnAllCheckedCancle = new System.Windows.Forms.Button();
			this.btnAllCheck = new System.Windows.Forms.Button();
			this.buttonOver = new System.Windows.Forms.Button();
			this.timer1 = new System.Windows.Forms.Timer(this.components);
			this.labelProcess = new System.Windows.Forms.Label();
			base.SuspendLayout();
			this.progressBarUpdate.Location = new System.Drawing.Point(12, 44);
			this.progressBarUpdate.Name = "progressBarUpdate";
			this.progressBarUpdate.Size = new System.Drawing.Size(426, 23);
			this.progressBarUpdate.TabIndex = 0;
			this.lblPercent.AutoSize = true;
			this.lblPercent.Location = new System.Drawing.Point(19, 13);
			this.lblPercent.Name = "lblPercent";
			this.lblPercent.Size = new System.Drawing.Size(35, 12);
			this.lblPercent.TabIndex = 1;
			this.lblPercent.Text = "进度:";
			this.btnAllCheckedCancle.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.btnAllCheckedCancle.Image = online_bid_autoUpdate.Properties.Resources.all_uncheck_20;
			this.btnAllCheckedCancle.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.btnAllCheckedCancle.Location = new System.Drawing.Point(255, 91);
			this.btnAllCheckedCancle.Name = "btnAllCheckedCancle";
			this.btnAllCheckedCancle.Size = new System.Drawing.Size(59, 30);
			this.btnAllCheckedCancle.TabIndex = 12;
			this.btnAllCheckedCancle.Text = "取消";
			this.btnAllCheckedCancle.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.btnAllCheckedCancle.UseVisualStyleBackColor = true;
			this.btnAllCheckedCancle.Visible = false;
			this.btnAllCheckedCancle.Click += new System.EventHandler(btnAllCheckedCancle_Click);
			this.btnAllCheck.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.btnAllCheck.Image = online_bid_autoUpdate.Properties.Resources.all_check_20;
			this.btnAllCheck.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.btnAllCheck.Location = new System.Drawing.Point(171, 91);
			this.btnAllCheck.Name = "btnAllCheck";
			this.btnAllCheck.Size = new System.Drawing.Size(63, 30);
			this.btnAllCheck.TabIndex = 11;
			this.btnAllCheck.Text = "更新";
			this.btnAllCheck.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.btnAllCheck.UseVisualStyleBackColor = true;
			this.btnAllCheck.Visible = false;
			this.btnAllCheck.Click += new System.EventHandler(btnAllCheck_Click);
			this.buttonOver.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonOver.Image = online_bid_autoUpdate.Properties.Resources.all_check_20;
			this.buttonOver.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.buttonOver.Location = new System.Drawing.Point(375, 91);
			this.buttonOver.Name = "buttonOver";
			this.buttonOver.Size = new System.Drawing.Size(63, 30);
			this.buttonOver.TabIndex = 11;
			this.buttonOver.Text = "完成";
			this.buttonOver.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
			this.buttonOver.UseVisualStyleBackColor = true;
			this.buttonOver.Visible = false;
			this.buttonOver.Click += new System.EventHandler(buttonOver_Click);
			this.labelProcess.AutoSize = true;
			this.labelProcess.Location = new System.Drawing.Point(52, 13);
			this.labelProcess.Name = "labelProcess";
			this.labelProcess.Size = new System.Drawing.Size(0, 12);
			this.labelProcess.TabIndex = 1;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(460, 133);
			base.Controls.Add(this.btnAllCheckedCancle);
			base.Controls.Add(this.buttonOver);
			base.Controls.Add(this.btnAllCheck);
			base.Controls.Add(this.labelProcess);
			base.Controls.Add(this.lblPercent);
			base.Controls.Add(this.progressBarUpdate);
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "AutoUpdateFrm2";
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "版本更新";
			base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(AutoUpdateFrm2_FormClosing);
			base.Load += new System.EventHandler(AutoUpdateFrm2_Load);
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	public class DataWrite
	{
		public delegate void UpdateUI(int step);

		public delegate void AccomplishTask();

		public UpdateUI UpdateUIDelegate;

		public AccomplishTask TaskCallBack;

		public void Write(object lineCount)
		{
			TaskCallBack();
			UpdateUIDelegate(1);
		}
	}
	internal static class Program
	{
		[STAThread]
		private static void Main()
		{
			if (CheckSafeSoftRunning(out var safeSbf))
			{
				MessageBox.Show(safeSbf.ToString());
				return;
			}
			Process[] processesByName = Process.GetProcessesByName("online_bid_autoUpdate");
			if (processesByName.Length > 1)
			{
				return;
			}
			Application.EnableVisualStyles();
			Application.SetCompatibleTextRenderingDefault(defaultValue: false);
			AppDomain.CurrentDomain.AssemblyResolve += delegate(object sender, ResolveEventArgs args)
			{
				string[] array = new string[4] { "online_bid_autoUpdate.dll.SharpCompress.dll", "online_bid_autoUpdate.dll.Newtonsoft.Json.dll", "online_bid_autoUpdate.dll.System.Data.SQLite2.dll", "online_bid_autoUpdate.dll.CommonApi.dll" };
				string text = null;
				if (args.Name.Contains("SharpCompress"))
				{
					text = array[0];
				}
				if (args.Name.Contains("Newtonsoft.Json"))
				{
					text = array[1];
				}
				if (text != null)
				{
					using (Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(text))
					{
						byte[] array2 = new byte[stream.Length];
						stream.Read(array2, 0, array2.Length);
						return Assembly.Load(array2);
					}
				}
				return (Assembly)null;
			};
			Application.Run(new AutoUpdateFrm2());
		}

		private static bool CheckSafeSoftRunning(out StringBuilder safeSbf)
		{
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			XmlSerializer xmlSerializer = new XmlSerializer(typeof(SoftInfo));
			using (FileStream stream = new FileStream("extraConfig.xml", FileMode.Open))
			{
				SoftInfo softInfo = xmlSerializer.Deserialize(stream) as SoftInfo;
				dictionary = softInfo.SafeSofts.ToDictionary<SafeSoft, string, string>((SafeSoft p) => p.Name, (SafeSoft p) => p.ProcessName);
			}
			safeSbf = new StringBuilder("先关闭");
			bool result = false;
			int num = 0;
			Process[] processes = Process.GetProcesses();
			Process[] array = processes;
			foreach (Process process in array)
			{
				if (dictionary.ContainsKey(process.ProcessName))
				{
					if (num != 0)
					{
						safeSbf.Append(" / ");
					}
					safeSbf.Append(dictionary[process.ProcessName]);
					result = true;
					num++;
				}
			}
			safeSbf.Append("，再操作");
			return result;
		}
	}
}
namespace online_bid_autoUpdate.Properties
{
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		private static ResourceManager resourceMan;

		private static CultureInfo resourceCulture;

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (resourceMan == null)
				{
					ResourceManager resourceManager = new ResourceManager("online_bid_autoUpdate.Properties.Resources", typeof(Resources).Assembly);
					resourceMan = resourceManager;
				}
				return resourceMan;
			}
		}

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return resourceCulture;
			}
			set
			{
				resourceCulture = value;
			}
		}

		internal static Bitmap _return
		{
			get
			{
				object @object = ResourceManager.GetObject("return", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap account_32
		{
			get
			{
				object @object = ResourceManager.GetObject("account_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap all_check_20
		{
			get
			{
				object @object = ResourceManager.GetObject("all_check_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap all_uncheck_20
		{
			get
			{
				object @object = ResourceManager.GetObject("all_uncheck_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap basic_info_80
		{
			get
			{
				object @object = ResourceManager.GetObject("basic_info_80", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap body
		{
			get
			{
				object @object = ResourceManager.GetObject("body", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap bs_monitor
		{
			get
			{
				object @object = ResourceManager.GetObject("bs_monitor", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap bs_return
		{
			get
			{
				object @object = ResourceManager.GetObject("bs_return", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap bs_upload
		{
			get
			{
				object @object = ResourceManager.GetObject("bs_upload", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap bszz
		{
			get
			{
				object @object = ResourceManager.GetObject("bszz", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap btn_bg
		{
			get
			{
				object @object = ResourceManager.GetObject("btn_bg", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap business_basic
		{
			get
			{
				object @object = ResourceManager.GetObject("business_basic", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap business_center
		{
			get
			{
				object @object = ResourceManager.GetObject("business_center", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap business_dzqz
		{
			get
			{
				object @object = ResourceManager.GetObject("business_dzqz", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap checkall_32
		{
			get
			{
				object @object = ResourceManager.GetObject("checkall_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap clear
		{
			get
			{
				object @object = ResourceManager.GetObject("clear", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap close
		{
			get
			{
				object @object = ResourceManager.GetObject("close", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap close_login
		{
			get
			{
				object @object = ResourceManager.GetObject("close_login", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap confirm_32
		{
			get
			{
				object @object = ResourceManager.GetObject("confirm_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap Create_Files_32
		{
			get
			{
				object @object = ResourceManager.GetObject("Create_Files_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap delete_20
		{
			get
			{
				object @object = ResourceManager.GetObject("delete_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap delete_32
		{
			get
			{
				object @object = ResourceManager.GetObject("delete_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap download_32
		{
			get
			{
				object @object = ResourceManager.GetObject("download_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap downtime_20
		{
			get
			{
				object @object = ResourceManager.GetObject("downtime_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap downtime_32
		{
			get
			{
				object @object = ResourceManager.GetObject("downtime_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap dzqz
		{
			get
			{
				object @object = ResourceManager.GetObject("dzqz", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap excel_32
		{
			get
			{
				object @object = ResourceManager.GetObject("excel_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap file_32_upload
		{
			get
			{
				object @object = ResourceManager.GetObject("file_32_upload", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap file_return
		{
			get
			{
				object @object = ResourceManager.GetObject("file_return", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap form_close
		{
			get
			{
				object @object = ResourceManager.GetObject("form_close", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap form_restore
		{
			get
			{
				object @object = ResourceManager.GetObject("form_restore", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap import_32
		{
			get
			{
				object @object = ResourceManager.GetObject("import_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap loading
		{
			get
			{
				object @object = ResourceManager.GetObject("loading", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap login_login
		{
			get
			{
				object @object = ResourceManager.GetObject("login_login", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap logo
		{
			get
			{
				object @object = ResourceManager.GetObject("logo", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap max
		{
			get
			{
				object @object = ResourceManager.GetObject("max", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap min
		{
			get
			{
				object @object = ResourceManager.GetObject("min", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap monitor
		{
			get
			{
				object @object = ResourceManager.GetObject("monitor", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap pdf_32
		{
			get
			{
				object @object = ResourceManager.GetObject("pdf_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap personcenter
		{
			get
			{
				object @object = ResourceManager.GetObject("personcenter", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap pwd_32
		{
			get
			{
				object @object = ResourceManager.GetObject("pwd_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap quit
		{
			get
			{
				object @object = ResourceManager.GetObject("quit", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap refresh_20
		{
			get
			{
				object @object = ResourceManager.GetObject("refresh_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap refresh_32
		{
			get
			{
				object @object = ResourceManager.GetObject("refresh_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap return_32
		{
			get
			{
				object @object = ResourceManager.GetObject("return_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap save_20
		{
			get
			{
				object @object = ResourceManager.GetObject("save_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap save_32
		{
			get
			{
				object @object = ResourceManager.GetObject("save_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap select_confirm
		{
			get
			{
				object @object = ResourceManager.GetObject("select_confirm", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap select_confirm_20
		{
			get
			{
				object @object = ResourceManager.GetObject("select_confirm_20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap select20
		{
			get
			{
				object @object = ResourceManager.GetObject("select20", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap set_relation_32
		{
			get
			{
				object @object = ResourceManager.GetObject("set_relation_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap system_quit
		{
			get
			{
				object @object = ResourceManager.GetObject("system_quit", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap uncheck_32
		{
			get
			{
				object @object = ResourceManager.GetObject("uncheck_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap upload
		{
			get
			{
				object @object = ResourceManager.GetObject("upload", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap upload_32
		{
			get
			{
				object @object = ResourceManager.GetObject("upload_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap word_32
		{
			get
			{
				object @object = ResourceManager.GetObject("word_32", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal static Bitmap zxtb
		{
			get
			{
				object @object = ResourceManager.GetObject("zxtb", resourceCulture);
				return (Bitmap)@object;
			}
		}

		internal Resources()
		{
		}
	}
	[CompilerGenerated]
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "16.10.0.0")]
	internal sealed class Settings : ApplicationSettingsBase
	{
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());

		public static Settings Default => defaultInstance;
	}
}
namespace online_bid_autoUpdate.util
{
	public static class DataTableUtils
	{
		public static List<T> ToDataList<T>(this DataTable dt)
		{
			List<T> list = new List<T>();
			List<PropertyInfo> list2 = new List<PropertyInfo>(typeof(T).GetProperties());
			foreach (DataRow row in dt.Rows)
			{
				T val = Activator.CreateInstance<T>();
				int i;
				for (i = 0; i < dt.Columns.Count; i++)
				{
					PropertyInfo propertyInfo = list2.Find((PropertyInfo p) => humpToUnderline(p.Name) == dt.Columns[i].ColumnName.ToUpper());
					if (!(propertyInfo != (PropertyInfo)null))
					{
						continue;
					}
					try
					{
						if (!Convert.IsDBNull(row[i]))
						{
							object obj = null;
							propertyInfo.SetValue(value: (!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(row[i], propertyInfo.PropertyType) : Convert.ChangeType(row[i], Nullable.GetUnderlyingType(propertyInfo.PropertyType)), obj: val, index: null);
						}
					}
					catch (Exception ex)
					{
						throw new Exception("字段[" + propertyInfo.Name + "]转换出错," + ex.Message);
					}
				}
				list.Add(val);
			}
			return list;
		}

		public static T ToDataDto<T>(this DataTable dt)
		{
			T val = Activator.CreateInstance<T>();
			if (dt == null || dt.Rows.Count == 0)
			{
				return val;
			}
			List<PropertyInfo> list = new List<PropertyInfo>(typeof(T).GetProperties());
			int i;
			for (i = 0; i < dt.Columns.Count; i++)
			{
				PropertyInfo propertyInfo = list.Find((PropertyInfo p) => humpToUnderline(p.Name) == dt.Columns[i].ColumnName.ToUpper());
				if (!(propertyInfo != (PropertyInfo)null))
				{
					continue;
				}
				try
				{
					if (!Convert.IsDBNull(dt.Rows[0][i]))
					{
						object obj = null;
						propertyInfo.SetValue(value: (!propertyInfo.PropertyType.ToString().Contains("System.Nullable")) ? Convert.ChangeType(dt.Rows[0][i], propertyInfo.PropertyType) : Convert.ChangeType(dt.Rows[0][i], Nullable.GetUnderlyingType(propertyInfo.PropertyType)), obj: val, index: null);
					}
				}
				catch (Exception ex)
				{
					throw new Exception("字段[" + propertyInfo.Name + "]转换出错," + ex.Message);
				}
			}
			return val;
		}

		public static DataTable ToDataTable<T>(List<T> entities)
		{
			DataTable dataTable = CreateTable<T>();
			FillData(dataTable, entities);
			return dataTable;
		}

		private static DataTable CreateTable<T>()
		{
			DataTable dataTable = new DataTable();
			Type typeFromHandle = typeof(T);
			PropertyInfo[] properties = typeFromHandle.GetProperties(BindingFlags.Instance | BindingFlags.Public);
			foreach (PropertyInfo propertyInfo in properties)
			{
				Type type = propertyInfo.PropertyType;
				if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
				{
					type = type.GetGenericArguments()[0];
				}
				dataTable.Columns.Add(propertyInfo.Name, type);
			}
			return dataTable;
		}

		private static void FillData<T>(DataTable dt, IEnumerable<T> entities)
		{
			foreach (T entity in entities)
			{
				dt.Rows.Add(CreateRow(dt, entity));
			}
		}

		private static DataRow CreateRow<T>(DataTable dt, T entity)
		{
			DataRow dataRow = dt.NewRow();
			Type typeFromHandle = typeof(T);
			PropertyInfo[] properties = typeFromHandle.GetProperties(BindingFlags.Instance | BindingFlags.Public);
			foreach (PropertyInfo propertyInfo in properties)
			{
				dataRow[propertyInfo.Name] = propertyInfo.GetValue(entity, null) ?? DBNull.Value;
			}
			return dataRow;
		}

		public static string humpToUnderline(string strItem)
		{
			if (string.IsNullOrEmpty(strItem))
			{
				return "";
			}
			string text = "";
			for (int i = 0; i < strItem.Length; i++)
			{
				string text2 = strItem[i].ToString();
				if (Regex.IsMatch(text2, "[A-Z]") && i != 0)
				{
					text2 = "_" + text2.ToLower();
				}
				text += text2;
			}
			return text.ToUpper();
		}
	}
	public class LogsHelper
	{
		public static string rootPath = AppDomain.CurrentDomain.BaseDirectory;

		public static void addLog(string str, string name = "管理员", string writeFileName = "Logs/upgrade.log")
		{
			InsertLog(str, name, writeFileName);
		}

		public static void AddErrorLog(string str, string name = "管理员", string writeFileName = "Logs/upgrade_error.log")
		{
			InsertLog(str, name, writeFileName);
		}

		public static void InsertLog(string str, string name, string writeFileName)
		{
			string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
			string path = rootPath + writeFileName;
			FileStream fileStream = null;
			StreamWriter streamWriter = null;
			try
			{
				Directory.CreateDirectory(Path.GetDirectoryName(path));
				if (!File.Exists(path))
				{
					fileStream = new FileStream(path, FileMode.Create, FileAccess.Write);
					streamWriter = new StreamWriter(fileStream);
					long length = fileStream.Length;
					fileStream.Seek(length, SeekOrigin.End);
					streamWriter.WriteLine("时间 :" + text + "   操作员姓名:" + name + "        操作:" + str + "\n");
				}
				else
				{
					fileStream = new FileStream(path, FileMode.Open, FileAccess.Write);
					streamWriter = new StreamWriter(fileStream);
					long length2 = fileStream.Length;
					fileStream.Seek(length2, SeekOrigin.Begin);
					streamWriter.WriteLine("时间 :" + text + "   操作员姓名:" + name + "        操作:" + str + "\n");
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine("日志输入异常:" + ex.Message);
			}
			finally
			{
				streamWriter?.Close();
				fileStream?.Close();
			}
		}
	}
	public class SqliteHelper
	{
		public static void UpgradeDB(string oldDB, string newDB)
		{
			StringBuilder stringBuilder = new StringBuilder();
			string directoryName = Path.GetDirectoryName(oldDB);
			string fileName = Path.GetFileName(oldDB);
			string directoryName2 = Path.GetDirectoryName(newDB);
			string fileName2 = Path.GetFileName(newDB);
			StringBuilder stringBuilder2 = new StringBuilder();
			stringBuilder2.Append("Attach database '" + newDB + "' as db1;\r\n");
			stringBuilder2.Append("SELECT name,'' as sql  FROM sqlite_master WHERE type = 'table' and name!= 'sqlite_sequence'\r\n");
			stringBuilder2.Append("and name in(SELECT name FROM db1.sqlite_master WHERE type = 'table' and name!= 'sqlite_sequence')\r\n");
			stringBuilder2.Append("union\r\n");
			stringBuilder2.Append("SELECT name,sql FROM db1.sqlite_master WHERE type = 'table' and name!= 'sqlite_sequence'\r\n");
			stringBuilder2.Append("and name not in(SELECT name FROM sqlite_master WHERE type = 'table' and name!= 'sqlite_sequence');\r\n");
			stringBuilder2.Append("Detach database db1;\r\n");
			DataTable dataTable = SQLiteLibrary.selectDataBySql(directoryName, fileName, stringBuilder2.ToString());
			DataRowCollection rows = dataTable.Rows;
			foreach (DataRow item in rows)
			{
				if (!string.IsNullOrEmpty(item.Field<string>("sql")))
				{
					stringBuilder.Append(item.Field<string>("sql") + ";\r\n");
				}
			}
			for (int i = 0; i < rows.Count; i++)
			{
				if (!string.IsNullOrEmpty(rows[i].Field<string>("sql")))
				{
					continue;
				}
				string selectSql = string.Format("PRAGMA  table_info('{0}');", rows[i]["name"]);
				DataTable dataTable2 = SQLiteLibrary.selectDataBySql(directoryName, fileName, selectSql);
				DataTable dataTable3 = SQLiteLibrary.selectDataBySql(directoryName2, fileName2, selectSql);
				DataRowCollection rows2 = dataTable2.Rows;
				DataRowCollection rows3 = dataTable3.Rows;
				foreach (DataRow item2 in rows3)
				{
					bool flag = false;
					foreach (DataRow item3 in rows2)
					{
						if (item2.Field<string>("NAME").Equals(item3.Field<string>("NAME")))
						{
							flag = true;
						}
					}
					if (!flag)
					{
						stringBuilder.Append(string.Format("ALTER TABLE {0} ADD COLUMN {1} {2};\n", rows[i]["name"], item2.Field<string>("NAME"), item2.Field<string>("TYPE")));
					}
				}
			}
			if (stringBuilder.Length > 0)
			{
				SQLiteLibrary.ExecuteSqlByTransaction(directoryName, fileName, new string[1] { stringBuilder.ToString() });
				Console.WriteLine("数据库更新成功");
				Console.WriteLine("更新的sql:" + stringBuilder.ToString());
			}
			else
			{
				Console.WriteLine("数据库不存在更新的表和字段");
			}
		}
	}
	public class SQLiteLibrary
	{
		public static DataTable selectDataBySql(string dbLocation, string dbName, string selectSql)
		{
			SQLiteConnection sQLiteConnection = null;
			SQLiteDataReader sQLiteDataReader = null;
			try
			{
				string text = dbLocation + "\\" + dbName;
				sQLiteConnection = new SQLiteConnection("data source=" + text);
				if (sQLiteConnection.State != ConnectionState.Open)
				{
					sQLiteConnection.Open();
				}
				SQLiteCommand sQLiteCommand = new SQLiteCommand(selectSql, sQLiteConnection);
				sQLiteCommand.CommandTimeout = 120;
				sQLiteDataReader = sQLiteCommand.ExecuteReader();
				DataTable dataTable = new DataTable();
				if (sQLiteDataReader != null)
				{
					dataTable.Load(sQLiteDataReader, LoadOption.PreserveChanges, null);
				}
				sQLiteCommand.Parameters.Clear();
				return dataTable;
			}
			catch (Exception ex)
			{
				Console.WriteLine(ex.Message);
				LogsHelper.AddErrorLog("sql:" + string.Join("\r\n", new string[1] { selectSql }) + "\r\nmessage:" + ex.Message + "\r\nstackTrace:" + ex.StackTrace);
			}
			finally
			{
				sQLiteDataReader?.Close();
				sQLiteConnection?.Dispose();
			}
			return null;
		}

		public static bool ExecuteSqlByTransaction(string dbLocation, string dbName, string[] sqlArr)
		{
			bool result = true;
			string connectionString = "data source=" + dbLocation + "\\" + dbName;
			SQLiteConnection sQLiteConnection = null;
			SQLiteTransaction sQLiteTransaction = null;
			SQLiteCommand sQLiteCommand = null;
			try
			{
				sQLiteConnection = new SQLiteConnection(connectionString);
				sQLiteConnection.Open();
				sQLiteTransaction = sQLiteConnection.BeginTransaction();
				sQLiteCommand = new SQLiteCommand();
				foreach (string cmdText in sqlArr)
				{
					PrepareCommand(sQLiteCommand, sQLiteConnection, sQLiteTransaction, cmdText, null);
					int num = sQLiteCommand.ExecuteNonQuery();
					sQLiteCommand.Parameters.Clear();
				}
				sQLiteTransaction.Commit();
			}
			catch (Exception ex)
			{
				result = false;
				sQLiteTransaction?.Rollback();
				LogsHelper.AddErrorLog("sql:" + string.Join("\r\n", sqlArr) + "\r\nmessage:" + ex.Message + "\r\nstackTrace:" + ex.StackTrace);
			}
			finally
			{
				sQLiteTransaction?.Dispose();
				sQLiteCommand?.Dispose();
				sQLiteTransaction?.Dispose();
				sQLiteConnection?.Dispose();
			}
			return result;
		}

		private static void PrepareCommand(SQLiteCommand cmd, SQLiteConnection conn, SQLiteTransaction trans, string cmdText, SQLiteParameter[] cmdParms)
		{
			if (conn.State != ConnectionState.Open)
			{
				conn.Open();
			}
			cmd.Connection = conn;
			cmd.CommandText = cmdText;
			if (trans != null)
			{
				cmd.Transaction = trans;
			}
			cmd.CommandType = CommandType.Text;
			if (cmdParms != null)
			{
				foreach (SQLiteParameter parameter in cmdParms)
				{
					cmd.Parameters.Add(parameter);
				}
			}
		}
	}
	public class WebRequestUtil
	{
		public enum HttpMethod
		{
			Post,
			Get,
			Delete,
			Push
		}

		public static Result PostBasicApi(string api, object entity, int Timeout = 100000)
		{
			try
			{
				string text = ConfigurationManager.AppSettings["dataApiPrefixUrl"].ToString();
				api = Regex.Replace(api, "^(\\\\|//)", "");
				string text2 = text + api;
				text2 = text2 + "?rnd=" + DateTime.Now.ToFileTimeUtc();
				string jsonStr = "";
				if (entity != null)
				{
					jsonStr = JsonConvert.SerializeObject(entity);
				}
				string value = DoHttpPost(text2, jsonStr, Timeout);
				return JsonConvert.DeserializeObject<Result>(value);
			}
			catch (Exception ex)
			{
				return new Result(9, success: false, "请求服务器异常,异常信息:" + ex.Message);
			}
		}

		public static Result PostBasicByUrl(string url, object entity, int Timeout = 100000)
		{
			try
			{
				url = url + "?rnd=" + DateTime.Now.ToFileTimeUtc();
				string jsonStr = "";
				if (entity != null)
				{
					jsonStr = JsonConvert.SerializeObject(entity);
				}
				string value = DoHttpPost(url, jsonStr, Timeout);
				return JsonConvert.DeserializeObject<Result>(value);
			}
			catch (Exception ex)
			{
				return new Result(9, success: false, "请求服务器异常,异常信息:" + ex.Message);
			}
		}

		public static string DoHttpPost(string Url, string jsonStr, int Timeout = 100000)
		{
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(Url);
			httpWebRequest.Method = "POST";
			httpWebRequest.ReadWriteTimeout = 5000;
			httpWebRequest.Timeout = Timeout;
			httpWebRequest.KeepAlive = false;
			byte[] bytes = Encoding.Default.GetBytes(jsonStr);
			httpWebRequest.ContentType = "application/json";
			httpWebRequest.ContentLength = bytes.Length;
			Stream stream = null;
			stream = httpWebRequest.GetRequestStream();
			stream.Write(bytes, 0, bytes.Length);
			HttpWebResponse httpWebResponse = null;
			try
			{
				httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
				Stream responseStream = httpWebResponse.GetResponseStream();
				StreamReader streamReader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
				string result = streamReader.ReadToEnd();
				streamReader.Close();
				responseStream.Close();
				return result;
			}
			catch (Exception ex)
			{
				Result value = new Result
				{
					Code = 502,
					Successful = false,
					ResultHint = ex.Message
				};
				return JsonConvert.SerializeObject(value);
			}
		}
	}
}
namespace online_bid_autoUpdate.dto
{
	public class ColumInfo
	{
		private string name;

		private string type;

		private string notnull;

		private string pk;

		public string Name
		{
			get
			{
				return name;
			}
			set
			{
				name = value;
			}
		}

		public string Type
		{
			get
			{
				return type;
			}
			set
			{
				type = value;
			}
		}

		public string Notnull
		{
			get
			{
				return notnull;
			}
			set
			{
				notnull = value;
			}
		}

		public string Pk
		{
			get
			{
				return pk;
			}
			set
			{
				pk = value;
			}
		}
	}
	public class Result
	{
		public int Code { get; set; }

		public string ResultHint { get; set; }

		public bool Successful { get; set; }

		public object ResultValue { get; set; }

		public Result(int _code, bool success, string resultHint)
		{
			Code = _code;
			Successful = success;
			ResultHint = resultHint;
		}

		public Result()
		{
		}
	}
	[XmlRoot("SafeSoft", Namespace = null, IsNullable = false)]
	public class SafeSoft
	{
		public string Name { get; set; }

		public string ProcessName { get; set; }

		public SafeSoft()
		{
		}

		public SafeSoft(string name, string processName)
		{
			Name = name;
			ProcessName = processName;
		}
	}
	[XmlRoot("SafeInfo", Namespace = null, IsNullable = false)]
	public class SoftInfo
	{
		[XmlArray("SafeSofts")]
		public SafeSoft[] SafeSofts;
	}
}
