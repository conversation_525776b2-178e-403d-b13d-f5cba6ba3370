﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>


	<!--
		查询列表信息
	-->
	<select id="selectList">
		SELECT OS_ZB_SUPPLIER_AUTH_PERSON_INFO.*,
		'已上传数('||count(authedFj1.id)||')' AS authfj,
		'已上传数('||count(legalPersonFj1.id)||')' AS legalPersonFj
		FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO
		LEFT JOIN
		sys_file_info as  authedFj1  ON authedFj1.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID AND
		authedFj1.RELATED_KEY = 'authfj'
		LEFT JOIN
		sys_file_info  as legalPersonFj1 ON legalPersonFj1.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID AND
		legalPersonFj1.RELATED_KEY = 'legalPersonFj' group by OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID
	</select>


	<!--新增空行使用-->
	<select id="addEmptyRow">
		select null ID,null PROJECT_NO,null MARK_NO,null PACK_NAME,'@supplierId' SUPPLIER_ID ,
		'@supplierName' SUPPLIER_NAME,
		null LEGAL_PERSON,
		null LEGAL_PERSON_ID_CARD,
		null AUTHED_PERSON,
		null AUTHED_PERSON_ID_CARD,
		null GRANT_AUTH_PROXIES,
		null CREATE_USER,
		null CREATE_TIME,
		null UPDATE_USER,
		null UPDATE_TIME,
		null LEGAL_CARD_TYPE,
		null AUTHED_CARD_TYPE,
		null CONTACT_TEL,
		null CONTACT_PERSON,
		'@attachId' ATTACH_ID
	</select>
	
</mapper>
