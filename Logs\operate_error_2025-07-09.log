2025-07-09 14:35:42.2393|TRACE|MyCommon.util.LogsHelper|  message:第3行，计算方式数据不得为空
第3行，投运时间 数据格式不正确
第4行，计算方式数据不得为空
第4行，投运时间 数据格式不正确
第5行，计算方式数据不得为空
第5行，投运时间 数据格式不正确
第6行，计算方式数据不得为空
第6行，投运时间 数据格式不正确
第7行，计算方式数据不得为空
第7行，投运时间 数据格式不正确
|System.Exception: 第3行，计算方式数据不得为空
第3行，投运时间 数据格式不正确
第4行，计算方式数据不得为空
第4行，投运时间 数据格式不正确
第5行，计算方式数据不得为空
第5行，投运时间 数据格式不正确
第6行，计算方式数据不得为空
第6行，投运时间 数据格式不正确
第7行，计算方式数据不得为空
第7行，投运时间 数据格式不正确

   在 MyCommon.Repository.common.BaseAbstractRepository`1.ImportExcel(IList`1& entities, Int32 titleRow, Func`3 CheckAction, String businessId, Int32 startSheet)
   在 ess_zbfz_main.baseInfoFrm.FrmSalesInfo.btnImport_Click(Object sender, EventArgs e)
2025-07-09 14:36:05.2028|TRACE|MyCommon.util.LogsHelper|  message:第3行，计算方式数据不得为空
第3行，投运时间 数据格式不正确
第4行，计算方式数据不得为空
第4行，投运时间 数据格式不正确
第5行，计算方式数据不得为空
第5行，投运时间 数据格式不正确
第6行，计算方式数据不得为空
第6行，投运时间 数据格式不正确
第7行，计算方式数据不得为空
第7行，投运时间 数据格式不正确
|System.Exception: 第3行，计算方式数据不得为空
第3行，投运时间 数据格式不正确
第4行，计算方式数据不得为空
第4行，投运时间 数据格式不正确
第5行，计算方式数据不得为空
第5行，投运时间 数据格式不正确
第6行，计算方式数据不得为空
第6行，投运时间 数据格式不正确
第7行，计算方式数据不得为空
第7行，投运时间 数据格式不正确

   在 MyCommon.Repository.common.BaseAbstractRepository`1.ImportExcel(IList`1& entities, Int32 titleRow, Func`3 CheckAction, String businessId, Int32 startSheet)
   在 ess_zbfz_main.baseInfoFrm.FrmSalesInfo.btnImport_Click(Object sender, EventArgs e)
