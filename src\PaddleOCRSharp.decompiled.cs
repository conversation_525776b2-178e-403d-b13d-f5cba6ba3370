using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using Newtonsoft.Json;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.Default | DebuggableAttribute.DebuggingModes.DisableOptimizations | DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints | DebuggableAttribute.DebuggingModes.EnableEditAndContinue)]
[assembly: TargetFramework(".NETFramework,Version=v4.0", FrameworkDisplayName = ".NET Framework 4")]
[assembly: AssemblyCompany("广州英田信息科技有限公司")]
[assembly: AssemblyConfiguration("Debug")]
[assembly: AssemblyCopyright("Copyright (C) 2023 Guangzhou Yingtian Information Technology Co., Ltd")]
[assembly: AssemblyDescription("This project is based on Baidu PaddleOCR, Including character recognition, text detection and table OCR recognition based on statistical analysis of text detection results. It can be used without network and has high recognition accuracy.")]
[assembly: AssemblyFileVersion("4.0.2")]
[assembly: AssemblyInformationalVersion("4.0.2")]
[assembly: AssemblyProduct("PaddleOCRSharp")]
[assembly: AssemblyTitle("PaddleOCRSharp")]
[assembly: AssemblyVersion("*******")]
namespace PaddleOCRSharp;

/// <summary>
/// PaddleOCR识别引擎对象
/// </summary>
public abstract class EngineBase : IDisposable
{
	internal const string PaddleOCRdllName = "PaddleOCR.dll";

	/// <summary>
	/// PaddleOCR.dll自定义加载路径，默认为空，如果指定则需在引擎实例化前赋值。
	/// </summary>
	public static string PaddleOCRdllPath { get; set; }

	[DllImport("kernel32.dll")]
	private static extern IntPtr LoadLibrary(string path);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr GetError();

	/// <summary>
	/// 初始化
	/// </summary>
	public EngineBase()
	{
		TextBlock textBlock = JsonHelper.DeserializeObject<TextBlock>("{}");
		try
		{
			string text = $"{Environment.OSVersion.Version.Major}.{Environment.OSVersion.Version.Minor}";
			if (text == "6.1")
			{
				try
				{
					string rootDirectory = GetRootDirectory();
					string text2 = rootDirectory + "\\inference\\win7_dll\\";
					if (Directory.Exists(text2))
					{
						string environmentVariable = Environment.GetEnvironmentVariable("path", EnvironmentVariableTarget.Process);
						if (!string.IsNullOrEmpty(environmentVariable))
						{
							Environment.SetEnvironmentVariable("path", environmentVariable + ";" + text2, EnvironmentVariableTarget.Process);
						}
					}
				}
				catch
				{
					throw new Exception("Win7依赖dll动态加载失败。请手动复制文件夹【inference\\win7_dll】文件到PaddleOCR.dll目录。");
				}
			}
			if (string.IsNullOrEmpty(PaddleOCRdllPath))
			{
				PaddleOCRdllPath = GetDllDirectory();
			}
			if (!string.IsNullOrEmpty(PaddleOCRdllPath))
			{
				string environmentVariable2 = Environment.GetEnvironmentVariable("path", EnvironmentVariableTarget.Process);
				if (!string.IsNullOrEmpty(environmentVariable2))
				{
					Environment.SetEnvironmentVariable("path", environmentVariable2 + ";" + PaddleOCRdllPath, EnvironmentVariableTarget.Process);
					LoadLibrary(Path.Combine(PaddleOCRdllPath, "PaddleOCR.dll"));
					LoadLibrary(Path.Combine(PaddleOCRdllPath, "onnxruntime.dll"));
				}
			}
		}
		catch (Exception ex)
		{
			throw new Exception("设置自定义加载路径失败。" + ex.Message);
		}
	}

	/// <summary>
	/// 获取程序的当前路径;
	/// </summary>
	/// <returns></returns>
	private static string GetDllDirectory()
	{
		string text = GetRootDirectory();
		FileInfo[] files = new DirectoryInfo(text).GetFiles("PaddleOCR.dll", SearchOption.AllDirectories);
		if (files != null && files.Length != 0)
		{
			text = files.First().DirectoryName;
		}
		return text;
	}

	/// <summary>
	/// 获取程序的当前路径;
	/// </summary>
	/// <returns></returns>
	public static string GetRootDirectory()
	{
		return AppDomain.CurrentDomain.BaseDirectory;
	}

	/// <summary>
	/// 环境监测
	/// </summary>
	protected internal void CheckEnvironment()
	{
		if (!Environment.Is64BitProcess)
		{
			throw new Exception("暂不支持32位程序使用本OCR");
		}
	}

	/// <summary>
	/// Convert Image to Byte[]
	/// </summary>
	/// <param name="image"></param>
	/// <returns></returns>
	protected internal byte[] ImageToBytes(Image image)
	{
		ImageFormat rawFormat = image.RawFormat;
		using MemoryStream memoryStream = new MemoryStream();
		if (rawFormat.Guid == ImageFormat.Jpeg.Guid)
		{
			image.Save(memoryStream, ImageFormat.Jpeg);
		}
		else if (rawFormat.Guid == ImageFormat.Png.Guid)
		{
			image.Save(memoryStream, ImageFormat.Png);
		}
		else if (rawFormat.Guid == ImageFormat.Bmp.Guid)
		{
			image.Save(memoryStream, ImageFormat.Bmp);
		}
		else if (rawFormat.Guid == ImageFormat.Gif.Guid)
		{
			image.Save(memoryStream, ImageFormat.Gif);
		}
		else if (rawFormat.Guid == ImageFormat.Icon.Guid)
		{
			image.Save(memoryStream, ImageFormat.Icon);
		}
		else
		{
			image.Save(memoryStream, ImageFormat.Png);
		}
		byte[] array = new byte[memoryStream.Length];
		memoryStream.Seek(0L, SeekOrigin.Begin);
		memoryStream.Read(array, 0, array.Length);
		return array;
	}

	/// <summary>
	/// 释放内存
	/// </summary>
	public virtual void Dispose()
	{
	}

	/// <summary>
	/// 获取底层错误信息
	/// </summary>
	/// <returns></returns>
	public virtual string GetLastError()
	{
		string result = "";
		try
		{
			IntPtr error = GetError();
			if (error != IntPtr.Zero)
			{
				result = Marshal.PtrToStringAnsi(error);
				Marshal.FreeHGlobal(error);
			}
		}
		catch (Exception ex)
		{
			result = ex.Message;
		}
		return result;
	}
}
/// <summary>
/// Json帮助类
/// </summary>
public class JsonHelper
{
	/// <summary>
	/// Json序列化
	/// </summary>
	/// <param name="obj"></param>
	/// <returns></returns>
	public static string SerializeObject(object obj)
	{
		if (obj == null)
		{
			return null;
		}
		JsonSerializerSettings settings = new JsonSerializerSettings
		{
			TypeNameHandling = TypeNameHandling.Auto
		};
		return JsonConvert.SerializeObject(obj, settings);
	}

	/// <summary>
	/// Json反序列化
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="json"></param>
	/// <returns></returns>
	public static T DeserializeObject<T>(string json)
	{
		if (string.IsNullOrEmpty(json))
		{
			return default(T);
		}
		JsonSerializerSettings settings = new JsonSerializerSettings
		{
			TypeNameHandling = TypeNameHandling.Auto
		};
		return (T)JsonConvert.DeserializeObject(json, typeof(T), settings);
	}
}
/// <summary>
/// 模型配置对象
/// </summary>
public class OCRModelConfig
{
	/// <summary>
	/// det_infer模型路径
	/// </summary>
	public string det_infer { get; set; }

	/// <summary>
	/// cls_infer模型路径
	/// </summary>
	public string cls_infer { get; set; }

	/// <summary>
	/// rec_infer模型路径
	/// </summary>
	public string rec_infer { get; set; }

	/// <summary>
	/// ppocr_keys.txt文件名全路径
	/// </summary>
	public string keys { get; set; }
}
/// <summary>
/// 表格模型配置对象
/// </summary>
public class StructureModelConfig : OCRModelConfig
{
	/// <summary>
	/// table_model_dir模型路径
	/// </summary>
	public string table_model_dir { get; set; }

	/// <summary>
	/// 表格识别字典
	/// </summary>
	public string table_char_dict_path { get; set; }
}
/// <summary>
/// OCR识别参数
/// </summary>
[StructLayout(LayoutKind.Sequential, Pack = 1)]
public class OCRParameter
{
	/// <summary>
	/// 是否使用GPU；默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool use_gpu { get; set; } = false;


	/// <summary>
	/// GPU id，使用GPU时有效；默认0;
	/// </summary>
	public int gpu_id { get; set; } = 0;


	/// <summary>
	/// 申请的GPU内存;默认4000
	/// </summary>
	public int gpu_mem { get; set; } = 4000;


	/// <summary>
	/// CPU预测时的线程数，在机器核数充足的情况下，该值越大，预测速度越快；默认10
	/// </summary>
	public int cpu_math_library_num_threads { get; set; } = 10;


	/// <summary>
	/// 是否使用mkldnn库；默认true
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool enable_mkldnn { get; set; } = true;


	/// <summary>
	///             是否执行文字检测；默认true 
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool det { get; set; } = true;


	/// <summary>
	/// 是否执行文字识别；默认true
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool rec { get; set; } = true;


	/// <summary>
	/// 是否执行文字方向分类；默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool cls { get; set; } = false;


	/// <summary>
	/// 输入图像长宽大于960时，等比例缩放图像，使得图像最长边为960,；默认960
	/// </summary>
	public int max_side_len { get; set; } = 960;


	/// <summary>
	/// 用于过滤DB预测的二值化图像，设置为0.-0.3对结果影响不明显；默认0.3
	/// </summary>
	public float det_db_thresh { get; set; } = 0.3f;


	/// <summary>
	/// DB后处理过滤box的阈值，如果检测存在漏框情况，可酌情减小；默认0.5
	/// </summary>
	public float det_db_box_thresh { get; set; } = 0.5f;


	/// <summary>
	/// 表示文本框的紧致程度，越小则文本框更靠近文本;默认1.6
	/// </summary>
	public float det_db_unclip_ratio { get; set; } = 1.6f;


	/// <summary>
	/// 是否在输出映射上使用膨胀,默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool use_dilation { get; set; } = false;


	/// <summary>
	/// 1:使用多边形框计算bbox score，0:使用矩形框计算。矩形框计算速度更快，多边形框对弯曲文本区域计算更准确。
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool det_db_score_mode { get; set; } = true;


	/// <summary>
	/// 是否对结果进行可视化，为1时，预测结果会保存在output字段指定的文件夹下和输入图像同名的图像上。默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool visualize { get; set; } = false;


	/// <summary>
	/// 是否使用方向分类器,默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool use_angle_cls { get; set; } = false;


	/// <summary>
	/// 方向分类器的得分阈值，默认0.9
	/// </summary>
	public float cls_thresh { get; set; } = 0.9f;


	/// <summary>
	/// 方向分类器batchsize，默认1
	/// </summary>
	public int cls_batch_num { get; set; } = 1;


	/// <summary>
	/// 识别模型batchsize，默认6
	/// </summary>
	public int rec_batch_num { get; set; } = 6;


	/// <summary>
	/// 识别模型输入图像高度，默认48
	/// </summary>
	public int rec_img_h { get; set; } = 32;


	/// <summary>
	/// 识别模型输入图像宽度，默认320
	/// </summary>
	public int rec_img_w { get; set; } = 320;


	/// <summary>
	/// 是否显示预测结果，默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool show_img_vis { get; set; } = false;


	/// <summary>
	/// 使用GPU预测时，是否启动tensorrt，默认false
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool use_tensorrt { get; set; } = false;

}
/// <summary>
/// OCR识别结果
/// </summary>
[StructLayout(LayoutKind.Sequential, Pack = 1)]
public class OCRResult
{
	/// <summary>
	/// 文本块列表
	/// </summary>
	public List<TextBlock> TextBlocks { get; set; } = new List<TextBlock>();


	/// <summary>
	/// 识别结果文本
	/// </summary>
	public string Text => ToString();

	/// <summary>
	/// 识别结果文本Json格式
	/// </summary>
	public string JsonText { get; set; }

	/// <summary>
	/// 返回字符串格式
	/// </summary>
	public override string ToString()
	{
		if (TextBlocks == null)
		{
			return "";
		}
		return string.Join("", ((IEnumerable<TextBlock>)TextBlocks).Select<TextBlock, string>((Func<TextBlock, string>)((TextBlock x) => x.Text)).ToArray());
	}
}
/// <summary>
/// 识别的文本块
/// </summary>
public class TextBlock
{
	/// <summary>
	/// 文本块四周顶点坐标列表
	/// </summary>
	public List<OCRPoint> BoxPoints { get; set; } = new List<OCRPoint>();


	/// <summary>
	/// 文本块文本
	/// </summary>
	public string Text { get; set; }

	/// <summary>
	///             文本识别置信度
	/// </summary>
	public float Score { get; set; }

	/// <summary>
	///             角度分类置信度
	/// </summary>
	public float cls_score { get; set; }

	/// <summary>
	///             角度分类标签
	/// </summary>
	public int cls_label { get; set; }

	/// <summary>
	/// 返回字符串格式
	/// </summary>
	public override string ToString()
	{
		if (BoxPoints == null)
		{
			return "";
		}
		string text = string.Join(",", ((IEnumerable<OCRPoint>)BoxPoints).Select<OCRPoint, string>((Func<OCRPoint, string>)((OCRPoint x) => x.ToString())).ToArray());
		return $"{Text},Score:{Score},[{text}],cls_label:{cls_label},cls_score:{cls_score}";
	}
}
/// <summary>
/// 点对象
/// </summary>
[StructLayout(LayoutKind.Sequential, Pack = 1)]
public class OCRPoint
{
	/// <summary>
	/// X坐标，单位像素
	/// </summary>
	public int X;

	/// <summary>
	/// Y坐标，单位像素
	/// </summary>
	public int Y;

	/// <summary>
	///             默认构造函数
	/// </summary>
	public OCRPoint()
	{
	}

	/// <summary>
	/// 构造函数
	/// </summary>
	/// <param name="x"></param>
	/// <param name="y"></param>
	public OCRPoint(int x, int y)
	{
		X = x;
		Y = y;
	}

	/// <summary>
	/// 返回字符串格式
	/// </summary>
	public override string ToString()
	{
		return $"({X},{Y})";
	}
}
/// <summary>
/// OCR结构化识别结果
/// </summary>
public sealed class OCRStructureResult
{
	/// <summary>
	/// 行数
	/// </summary>
	public int RowCount { get; set; }

	/// <summary>
	/// 列数
	/// </summary>
	public int ColCount { get; set; }

	/// <summary>
	/// 单元格 列表
	/// </summary>
	public List<StructureCells> Cells { get; set; }

	/// <summary>
	/// 文本块列表
	/// </summary>
	public List<TextBlock> TextBlocks { get; set; }

	/// <summary>
	/// 表格识别结果
	/// </summary>
	public OCRStructureResult()
	{
		Cells = new List<StructureCells>();
		TextBlocks = new List<TextBlock>();
	}
}
/// <summary>
/// 单元格
/// </summary>
public sealed class StructureCells
{
	/// <summary>
	/// 行数
	/// </summary>
	public int Row { get; set; }

	/// <summary>
	/// 列数
	/// </summary>
	public int Col { get; set; }

	/// <summary>
	/// 文本块
	/// </summary>
	public List<TextBlock> TextBlocks { get; set; }

	/// <summary>
	/// 识别文本
	/// </summary>
	public string Text { get; set; }

	/// <summary>
	/// 单元格构造函数
	/// </summary>
	public StructureCells()
	{
		TextBlocks = new List<TextBlock>();
	}
}
/// <summary>
/// PaddleOCR识别引擎对象
/// </summary>
public class PaddleOCREngine : EngineBase
{
	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern void Initialize(string det_infer, string cls_infer, string rec_infer, string keys, OCRParameter parameter);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern void Initializejson(string det_infer, string cls_infer, string rec_infer, string keys, string parameterjson);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr Detect(string imagefile);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr DetectByte(byte[] imagebytedata, long size);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr DetectBase64(string imagebase64);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern int FreeEngine();

	/// <summary>
	/// PaddleOCR识别引擎对象初始化
	/// </summary>
	/// <param name="config">模型配置对象，如果为空则按默认值</param>
	/// <param name="parameter">识别参数，为空均按缺省值</param>
	public PaddleOCREngine(OCRModelConfig config, OCRParameter parameter = null)
	{
		CheckEnvironment();
		if (parameter == null)
		{
			parameter = new OCRParameter();
		}
		if (config == null)
		{
			string rootDirectory = EngineBase.GetRootDirectory();
			config = new OCRModelConfig();
			string text = rootDirectory + "\\inference";
			config.det_infer = text + "\\ch_PP-OCRv4_det_infer";
			config.cls_infer = text + "\\ch_ppocr_mobile_v2.0_cls_infer";
			config.rec_infer = text + "\\ch_PP-OCRv4_rec_infer";
			config.keys = text + "\\ppocr_keys.txt";
		}
		if (!Directory.Exists(config.det_infer))
		{
			throw new DirectoryNotFoundException(config.det_infer);
		}
		if (!Directory.Exists(config.cls_infer))
		{
			throw new DirectoryNotFoundException(config.cls_infer);
		}
		if (!Directory.Exists(config.rec_infer))
		{
			throw new DirectoryNotFoundException(config.rec_infer);
		}
		if (!File.Exists(config.keys))
		{
			throw new FileNotFoundException(config.keys);
		}
		Initialize(config.det_infer, config.cls_infer, config.rec_infer, config.keys, parameter);
	}

	/// <summary>
	/// PaddleOCR识别引擎对象初始化
	/// </summary>
	/// <param name="config">模型配置对象，如果为空则按默认值</param>
	/// <param name="parameterjson">识别参数json字符串</param>
	public PaddleOCREngine(OCRModelConfig config, string parameterjson)
	{
		CheckEnvironment();
		if (config == null)
		{
			string rootDirectory = EngineBase.GetRootDirectory();
			config = new OCRModelConfig();
			string text = rootDirectory + "\\inference";
			config.det_infer = text + "\\ch_PP-OCRv4_det_infer";
			config.cls_infer = text + "\\ch_ppocr_mobile_v2.0_cls_infer";
			config.rec_infer = text + "\\ch_PP-OCRv4_rec_infer";
			config.keys = text + "\\ppocr_keys.txt";
		}
		if (string.IsNullOrEmpty(parameterjson))
		{
			parameterjson = EngineBase.GetRootDirectory();
			parameterjson += "\\inference\\PaddleOCR.config.json";
			if (!File.Exists(parameterjson))
			{
				throw new FileNotFoundException(parameterjson);
			}
			parameterjson = File.ReadAllText(parameterjson);
		}
		if (!Directory.Exists(config.det_infer))
		{
			throw new DirectoryNotFoundException(config.det_infer);
		}
		if (!Directory.Exists(config.cls_infer))
		{
			throw new DirectoryNotFoundException(config.cls_infer);
		}
		if (!Directory.Exists(config.rec_infer))
		{
			throw new DirectoryNotFoundException(config.rec_infer);
		}
		if (!File.Exists(config.keys))
		{
			throw new FileNotFoundException(config.keys);
		}
		Initializejson(config.det_infer, config.cls_infer, config.rec_infer, config.keys, parameterjson);
	}

	/// <summary>
	/// 对图像文件进行文本识别
	/// </summary>
	/// <param name="imagefile">图像文件</param>
	/// <returns>OCR识别结果</returns>
	public OCRResult DetectText(string imagefile)
	{
		if (!File.Exists(imagefile))
		{
			throw new Exception("文件" + imagefile + "不存在");
		}
		byte[] imagebyte = File.ReadAllBytes(imagefile);
		OCRResult result = DetectText(imagebyte);
		imagebyte = null;
		return result;
	}

	/// <summary>
	///             对图像对象进行文本识别
	/// </summary>
	/// <param name="image">图像</param>
	/// <returns>OCR识别结果</returns>
	public OCRResult DetectText(Image image)
	{
		if (image == null)
		{
			throw new ArgumentNullException("image");
		}
		byte[] imagebyte = ImageToBytes(image);
		OCRResult result = DetectText(imagebyte);
		imagebyte = null;
		return result;
	}

	/// <summary>
	///             文本识别
	/// </summary>
	/// <param name="imagebyte">图像内存流</param>
	/// <returns>OCR识别结果</returns>
	public OCRResult DetectText(byte[] imagebyte)
	{
		if (imagebyte == null)
		{
			throw new ArgumentNullException("imagebyte");
		}
		IntPtr ptrResult = DetectByte(imagebyte, imagebyte.LongLength);
		return ConvertResult(ptrResult);
	}

	/// <summary>
	///             文本识别
	/// </summary>
	/// <param name="imagebase64">图像base64</param>
	/// <returns>OCR识别结果</returns>
	public OCRResult DetectTextBase64(string imagebase64)
	{
		if (imagebase64 == null || imagebase64 == "")
		{
			throw new ArgumentNullException("imagebase64");
		}
		IntPtr ptrResult = DetectBase64(imagebase64);
		return ConvertResult(ptrResult);
	}

	/// <summary>
	/// 结果解析
	/// </summary>
	/// <param name="ptrResult"></param>
	/// <returns></returns>
	private OCRResult ConvertResult(IntPtr ptrResult)
	{
		OCRResult oCRResult = new OCRResult();
		try
		{
			if (ptrResult == IntPtr.Zero)
			{
				string lastError = GetLastError();
				if (lastError != "")
				{
					throw new Exception("内部遇到错误：" + lastError);
				}
				return oCRResult;
			}
			string text = Marshal.PtrToStringUni(ptrResult);
			List<TextBlock> textBlocks = JsonHelper.DeserializeObject<List<TextBlock>>(text);
			oCRResult.JsonText = text;
			oCRResult.TextBlocks = textBlocks;
			Marshal.FreeHGlobal(ptrResult);
		}
		catch (Exception innerException)
		{
			throw new Exception("OCR结果Json反序列化失败。", innerException);
		}
		return oCRResult;
	}

	/// <summary>
	///             结构化文本识别
	/// </summary>
	/// <param name="image">图像</param>
	/// <returns>表格识别结果</returns>
	public OCRStructureResult DetectStructure(Image image)
	{
		if (image == null)
		{
			throw new ArgumentNullException("image");
		}
		byte[] imagebyte = ImageToBytes(image);
		OCRResult oCRResult = DetectText(imagebyte);
		List<TextBlock> textBlocks = oCRResult.TextBlocks;
		if (textBlocks == null || textBlocks.Count == 0)
		{
			return new OCRStructureResult();
		}
		List<int> list = getzeroindexs(((IEnumerable<TextBlock>)textBlocks.OrderBy<TextBlock, int>((TextBlock x) => x.BoxPoints[0].Y)).Select<TextBlock, int>((Func<TextBlock, int>)((TextBlock x) => x.BoxPoints[0].Y)).ToArray());
		List<int> list2 = getzeroindexs(((IEnumerable<TextBlock>)textBlocks.OrderBy<TextBlock, int>((TextBlock x) => x.BoxPoints[0].X)).Select<TextBlock, int>((Func<TextBlock, int>)((TextBlock x) => x.BoxPoints[0].X)).ToArray());
		int count = list.Count;
		int count2 = list2.Count;
		OCRStructureResult oCRStructureResult = new OCRStructureResult();
		oCRStructureResult.TextBlocks = textBlocks;
		oCRStructureResult.RowCount = count;
		oCRStructureResult.ColCount = count2;
		oCRStructureResult.Cells = new List<StructureCells>();
		for (int i = 0; i < count; i++)
		{
			int y_min = (from x in textBlocks
				orderby x.BoxPoints[0].Y
				orderby x.BoxPoints[0].Y
				select x).ToList()[list[i]].BoxPoints[0].Y;
			int y_max = 99999;
			if (i < count - 1)
			{
				y_max = textBlocks.OrderBy<TextBlock, int>((TextBlock x) => x.BoxPoints[0].Y).ToList()[list[i + 1]].BoxPoints[0].Y;
			}
			for (int j = 0; j < count2; j++)
			{
				int x_min = textBlocks.OrderBy<TextBlock, int>((TextBlock x) => x.BoxPoints[0].X).ToList()[list2[j]].BoxPoints[0].X;
				int x_max = 99999;
				if (j < count2 - 1)
				{
					x_max = textBlocks.OrderBy<TextBlock, int>((TextBlock x) => x.BoxPoints[0].X).ToList()[list2[j + 1]].BoxPoints[0].X;
				}
				IOrderedEnumerable<TextBlock> source = from u in ((IEnumerable<TextBlock>)textBlocks).Where((Func<TextBlock, bool>)((TextBlock x) => x.BoxPoints[0].X < x_max && x.BoxPoints[0].X >= x_min && x.BoxPoints[0].Y < y_max && x.BoxPoints[0].Y >= y_min))
					orderby u.BoxPoints[0].X
					select u;
				string[] array = ((IEnumerable<TextBlock>)source).Select<TextBlock, string>((Func<TextBlock, string>)((TextBlock x) => x.Text)).ToArray();
				StructureCells structureCells = new StructureCells();
				structureCells.Row = i;
				structureCells.Col = j;
				structureCells.Text = string.Join<string>("", (IEnumerable<string>)array);
				structureCells.TextBlocks = source.ToList();
				oCRStructureResult.Cells.Add(structureCells);
			}
		}
		return oCRStructureResult;
	}

	/// <summary>
	/// 计算表格分割
	/// </summary>
	/// <param name="pixellist"></param>
	/// <param name="thresholdtozero"></param>
	/// <returns></returns>
	private List<int> getzeroindexs(int[] pixellist, int thresholdtozero = 10)
	{
		List<int> list = new List<int>();
		list.Add(0);
		for (int i = 0; i < pixellist.Length; i++)
		{
			if (i < pixellist.Length - 1 && Math.Abs(pixellist[i + 1] - pixellist[i]) > thresholdtozero)
			{
				list.Add(i + 1);
			}
		}
		return list;
	}

	/// <summary>
	/// 释放对象
	/// </summary>
	public override void Dispose()
	{
		FreeEngine();
	}
}
/// <summary>
/// PaddleOCR NET帮助类
/// </summary>
public class PaddleStructureEngine : EngineBase
{
	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern void StructureInitialize(string det_infer, string rec_infer, string keys, string table_model_dir, string table_char_dict_path, StructureParameter parameter);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern void StructureInitializejson(string det_infer, string rec_infer, string keys, string table_model_dir, string table_char_dict_path, string parameter);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr GetStructureDetectFile(string imagefile);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr GetStructureDetectByte(byte[] imagebytedata, long size);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern IntPtr GetStructureDetectBase64(string imagebase64);

	[DllImport("PaddleOCR.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	internal static extern void FreeStructureEngine();

	/// <summary>
	/// PaddleStructureEngine识别引擎对象初始化
	/// </summary>
	/// <param name="config">模型配置对象，如果为空则按默认值</param>
	/// <param name="parameter">识别参数，为空均按缺省值</param>
	public PaddleStructureEngine(StructureModelConfig config, StructureParameter parameter)
	{
		CheckEnvironment();
		if (parameter == null)
		{
			parameter = new StructureParameter();
		}
		if (config == null)
		{
			string rootDirectory = EngineBase.GetRootDirectory();
			config = new StructureModelConfig();
			string text = rootDirectory + "\\inference";
			config.det_infer = text + "\\ch_PP-OCRv4_det_infer";
			config.rec_infer = text + "\\ch_PP-OCRv4_rec_infer";
			config.keys = text + "\\ppocr_keys.txt";
			config.table_model_dir = text + "\\ch_ppstructure_mobile_v2.0_SLANet_infer";
			config.table_char_dict_path = text + "\\table_structure_dict_ch.txt";
		}
		StructureInitialize(config.det_infer, config.rec_infer, config.keys, config.table_model_dir, config.table_char_dict_path, parameter);
	}

	/// <summary>
	/// PaddleStructureEngine识别引擎对象初始化
	/// </summary>
	/// <param name="config">模型配置对象，如果为空则按默认值</param>
	/// <param name="parameterjson">识别参数Json格式，为空均按缺省值</param>
	public PaddleStructureEngine(StructureModelConfig config, string parameterjson)
	{
		CheckEnvironment();
		if (config == null)
		{
			string rootDirectory = EngineBase.GetRootDirectory();
			config = new StructureModelConfig();
			string text = rootDirectory + "\\inference";
			config.det_infer = text + "\\ch_PP-OCRv4_det_infer";
			config.rec_infer = text + "\\ch_PP-OCRv4_rec_infer";
			config.keys = text + "\\ppocr_keys.txt";
			config.table_model_dir = text + "\\ch_ppstructure_mobile_v2.0_SLANet_infer";
			config.table_char_dict_path = text + "\\table_structure_dict_ch.txt";
		}
		if (string.IsNullOrEmpty(parameterjson))
		{
			parameterjson = EngineBase.GetRootDirectory();
			parameterjson += "\\inference\\PaddleOCRStructure.config.json";
			if (!File.Exists(parameterjson))
			{
				throw new FileNotFoundException(parameterjson);
			}
			parameterjson = File.ReadAllText(parameterjson);
		}
		StructureInitializejson(config.det_infer, config.rec_infer, config.keys, config.table_model_dir, config.table_char_dict_path, parameterjson);
	}

	/// <summary>
	/// 对图像文件进行表格文本识别
	/// </summary>
	/// <param name="imagefile">图像文件</param>
	/// <returns>表格识别结果</returns>
	public string StructureDetectFile(string imagefile)
	{
		if (!File.Exists(imagefile))
		{
			throw new Exception("文件" + imagefile + "不存在");
		}
		IntPtr structureDetectFile = GetStructureDetectFile(imagefile);
		string result = Marshal.PtrToStringUni(structureDetectFile);
		Marshal.FreeHGlobal(structureDetectFile);
		return result;
	}

	/// <summary>
	///             对图像对象进行表格文本识别
	/// </summary>
	/// <param name="image">图像</param>
	/// <returns>表格识别结果</returns>
	public string StructureDetect(Image image)
	{
		if (image == null)
		{
			throw new ArgumentNullException("image");
		}
		byte[] imagebyte = ImageToBytes(image);
		string result = StructureDetect(imagebyte);
		imagebyte = null;
		return result;
	}

	/// <summary>
	/// 对图像Byte数组进行表格文本识别
	/// </summary>
	/// <param name="imagebyte">图像字节数组</param>
	/// <returns>表格识别结果</returns>
	public string StructureDetect(byte[] imagebyte)
	{
		if (imagebyte == null)
		{
			throw new ArgumentNullException("imagebyte");
		}
		IntPtr structureDetectByte = GetStructureDetectByte(imagebyte, imagebyte.LongLength);
		string result = Marshal.PtrToStringUni(structureDetectByte);
		Marshal.FreeHGlobal(structureDetectByte);
		return result;
	}

	/// <summary>
	/// 对图像Base64进行表格文本识别
	/// </summary>
	/// <param name="imagebase64">图像Base64</param>
	/// <returns>表格识别结果</returns>
	public string StructureDetectBase64(string imagebase64)
	{
		if (imagebase64 == null || imagebase64 == "")
		{
			throw new ArgumentNullException("imagebase64");
		}
		IntPtr structureDetectBase = GetStructureDetectBase64(imagebase64);
		string result = Marshal.PtrToStringUni(structureDetectBase);
		Marshal.FreeHGlobal(structureDetectBase);
		return result;
	}

	/// <summary>
	/// 释放对象
	/// </summary>
	public override void Dispose()
	{
		FreeStructureEngine();
	}
}
/// <summary>
/// OCR识别参数
/// </summary>
[StructLayout(LayoutKind.Sequential, Pack = 1)]
public class StructureParameter : OCRParameter
{
	/// <summary>
	/// 输入图像长宽大于488时，等比例缩放图像,默认488
	/// </summary>
	public int table_max_len { get; set; } = 488;


	/// <summary>
	/// 是否合并空单元格
	/// </summary>
	[field: MarshalAs(UnmanagedType.I1)]
	public bool merge_no_span_structure { get; set; } = true;


	/// <summary>
	/// 批量识别数量
	/// </summary>
	public int table_batch_num { get; set; } = 1;

}
