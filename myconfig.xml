<?xml version="1.0" encoding="utf-8"?>
<configuration>

	<!--上传配置-->
	<UpLoadConfigs>
		<!--财务信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_FINANCE_INFO" pdfCount="1" pdfSize="30" imgCount="10" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		<!--业绩文件信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_SALES_INFO" pdfCount="1" pdfSize="30" imgCount="10" imgSize="0.5" totalRecord="9999"></UpLoadConfig>
		<!--试验报告信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_REPORT_INFO" pdfCount="1" pdfSize="40" imgCount="10" imgSize="0.5" totalRecord="9999"></UpLoadConfig>
		
		<!--产品信息（集成证明文件）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PRODUCT_INFO_agentFj" pdfCount="1" pdfSize="10" imgCount="10" imgSize="0.5" totalRecord="600"></UpLoadConfig>
				
		<!--产品信息（产品外观）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PRODUCT_INFO_cpwg" pdfCount="0" pdfSize="10" imgCount="7" imgSize="0.5" totalRecord="600"></UpLoadConfig>

		<!--产品信息（主图）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PRODUCT_INFO_main_image" pdfCount="0" pdfSize="10" imgCount="1" imgSize="0.5" totalRecord="600"></UpLoadConfig>

		<!--产品信息（副图）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PRODUCT_INFO_other_image" pdfCount="0" pdfSize="10" imgCount="7" imgSize="0.5" totalRecord="600"></UpLoadConfig>

		<!--产品组部件-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PARTS_INFO" pdfCount="1" pdfSize="5" imgCount="10" imgSize="0.5" totalRecord="600"></UpLoadConfig>

		<!--投标授权人-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_AUTH_PERSON_INFO" pdfCount="1" pdfSize="5" imgCount="1" imgSize="0.5" totalRecord="10"></UpLoadConfig>

		<!--试验设备信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_LABEQP_INFO" pdfCount="1" pdfSize="10" imgCount="15" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		<!--生产装备信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_MEQP_INFO" pdfCount="1" pdfSize="10" imgCount="15" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		<!--认证证书信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_CERT_INFO" pdfCount="1" pdfSize="10" imgCount="15" imgSize="0.5" totalRecord="50"></UpLoadConfig>
		<!--产品产能信息-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_CAPACITY_INFO" pdfCount="0" pdfSize="10" imgCount="10" imgSize="0.5" totalRecord="10"></UpLoadConfig>

		<!--配送方案-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_ASERVICE_INFO" pdfCount="1" pdfSize="10" imgCount="1" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		<!--资信证明-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_ZIXIN_INFO" pdfCount="1" pdfSize="10" imgCount="1" imgSize="0.5" totalRecord="10"></UpLoadConfig>

		<!--股权结构-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_EQUITY_INFO" pdfCount="1" pdfSize="10" imgCount="1" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		
		<!--股权证明材料-->
		<UpLoadConfig nodeCode="tag_equity_files" pdfCount="1" pdfSize="5" imgCount="10" imgSize="0.5" totalRecord="10"></UpLoadConfig>	

		<!--保证金附件-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_DEPOSIT_ATTACH" pdfCount="1" pdfSize="5" imgCount="0" imgSize="0.5" totalRecord="10"></UpLoadConfig>
		
		<!--投标保证金明细表上传及查看--><!--
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_DEPOSIT_bidDetailsInfoAttach" pdfCount="3" pdfSize="5" imgCount="0" imgSize="0.5" totalRecord="0"></UpLoadConfig>-->
		
		<!--投标保证金明细表上传及查看 + 投标保证金:汇款账户证明/保险购买账户证明/保函查验单上传及查看-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_DEPOSIT" pdfCount="5" pdfSize="99" imgCount="0" imgSize="0.5" totalRecord="0"></UpLoadConfig>

		<!--生产能力-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_PRODUCT_PLACE" pdfCount="1" pdfSize="5" imgCount="0" imgSize="0.5" totalRecord="10"></UpLoadConfig>

		<!--运行评价-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE" pdfCount="1" pdfSize="5" imgCount="10" imgSize="0.5" totalRecord="10"></UpLoadConfig>


		<!--服务批次其他相关内容-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS" pdfCount="1" pdfSize="80" imgCount="10" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!--企业类似项目业绩和实施经验（针对设计、施工等）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD" pdfCount="1" pdfSize="15" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!--企业类似项目业绩和实施经验（针对其他服务）-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE" pdfCount="1" pdfSize="15" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!--项目管理机构组成表-->
		<UpLoadConfig nodeCode="PM_ORG_TABLE_pm_org_file" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!--主要人员简历表及证明文件-->
		<UpLoadConfig nodeCode="CORE_RESUME_TABLE_core_resume_file" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!--工作大纲、工作方案及服务承诺-->
		<UpLoadConfig nodeCode="WORK_LINE_TABLE_work_line_file" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!-- 勘察设计负责人（项目经理/设总）及主要设计人员情况表 &&勘察设计负责人（项目经理/设总）情况表及相关证明材料-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL_designer_info" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 勘察设计负责人（项目经理/设总）及主要设计人员情况表 &&主要设计人员表及相关证明材料-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL_core_peopel_info" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 勘察设计负责人（项目经理/设总）及主要设计人员情况表 &&拟投入设备软件情况表（仅三维设计标包适用）及相关证明材料-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL_software_info" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 勘察设计负责人（项目经理/设总）及主要设计人员情况表 &&勘察设计主要人员承诺函-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL_core_peopel_commit" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 勘察设计负责人（项目经理/设总）及主要设计人员情况表&&拟投入设备软件情况表（仅三维设计标包适用）及相关证明材料-->

		<!-- 拟分包项目情况表 &&勘察设计负责人（项目经理/设总）情况表及相关证明材料-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK_propose_subpack" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 拟分包项目情况表 &&勘察设计主要人员承诺函-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK_worker_commit" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!-- 拟派总监及主要监理人员情况表、施工项目管理关键人员承诺函 &&勘察设计负责人（项目经理/设总）情况表及相关证明材料-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER_propose_chiefStaffer" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>
		<!-- 拟派总监及主要监理人员情况表、施工项目管理关键人员承诺函 &&勘察设计主要人员承诺函-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_CHIEF_STAFFER_core_listener_commit" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!-- 联合体协议书-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT_complex_fj" pdfCount="1" pdfSize="10" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!-- 上传安全、质量、进度等保障措施及方案-->
		<UpLoadConfig nodeCode="OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS_safe_to_progress" pdfCount="1" pdfSize="50" imgCount="0" imgSize="0.5" totalRecord="99"></UpLoadConfig>

		<!-- 资格预审-->
		<UpLoadConfig nodeCode="OS_ZB_BIAO_PRE_QUALIFICATION" pdfCount="1" pdfSize="10" imgCount="0" imgSize="0.5" totalRecord="3"></UpLoadConfig>
		
		<!-- 发票附件-->
		<UpLoadConfig nodeCode="OS_ZB_BILL_INFO" pdfCount="0" pdfSize="10" imgCount="100" imgSize="2" imgMinSize="0.01" totalRecord="100" ></UpLoadConfig>

		<!--物资批次其他模块 附件上传-->
		<UpLoadConfig nodeCode="Other_Tab" pdfCount="1" pdfSize="100" imgCount="15" imgSize="1"  totalRecord="60" ></UpLoadConfig>
		
		<!--服务批次其他关键信息 附件上传 仅支持pdf-->
		<UpLoadConfig nodeCode="OtherKey" pdfCount="1" pdfSize="50" ></UpLoadConfig>		
		
		<!--履约评价证明-->
		<UpLoadConfig nodeCode="OS_ZB_MODEL_FILE_INFO#CAPACITY_REVIEW" pdfCount="1" pdfSize="200" ></UpLoadConfig>		
		
		<!--高质量发展评价-->
		<UpLoadConfig nodeCode="OS_ZB_MODEL_FILE_INFO#FAST_GROW_REVIEW" pdfCount="1" pdfSize="10" ></UpLoadConfig>
		<!--绿色回收-->
		<UpLoadConfig nodeCode="SYS_MODEL_CONFIG#绿色回收" pdfCount="1" pdfSize="100" ></UpLoadConfig>
		<!--绿色低碳生产-->
		<UpLoadConfig nodeCode="SYS_MODEL_CONFIG#绿色低碳生产" pdfCount="1" pdfSize="100" ></UpLoadConfig>
		<!--“技术评分标准”涉及的其他支撑材料-->
		<UpLoadConfig nodeCode="SYS_MODEL_CONFIG#“技术评分标准”涉及的其他支撑材料" pdfCount="1" pdfSize="100" ></UpLoadConfig>
	</UpLoadConfigs>
	
	<!--tabPage信息-->
	<!--业绩文件、试验报告、认证证书、试验设备、生产装备移除  资信证明（不需要了）-->
	<TabPageConfigs columName="PURCHASE_TYPE" tableName="OS_ZB_PURCHASE_PROJECT_INFO">
		<!--基础信息模块（物资招标）-->
		<BaseInfo columName="PURCHASE_TYPE" tableName="OS_ZB_PURCHASE_PROJECT_INFO">
			<TabPage name="tabPageBaseInfo" text="投标人概况" showCondition="*" ></TabPage>
			<TabPage name="tabPageCredit" text="信用信息" showCondition="物资" ></TabPage>
			<TabPage name="tabPageFinace" text="财务数据" showCondition="物资" ></TabPage>
			<TabPage name="tabPageEquity" text="股权结构" showCondition="物资"  ></TabPage>
			<TabPage name="tabPageCert"   text="认证证书" showCondition="物资" ></TabPage>
			<TabPage name="tabPageSccd"   text="生产能力" showCondition="物资" ></TabPage>
			<TabPage name="tabPageYxpj"   text="运行评价" showCondition="物资" ></TabPage>
			<TabPage name="tabPageProcess"   text="制造工艺" showCondition="物资" ></TabPage>
			<!--<TabPage name="tabPreNotice" text="资格预审合格通知书" showCondition="物资" ></TabPage>-->
			<!--<TabPage name="tabBill" text="发票信息" showCondition="物资" ></TabPage>-->
			<TabPage name="tabPageChangeFile" text="企业变更证明材料" showCondition="物资" ></TabPage>
			<!--新增 addTime 20240506-->
			<TabPage name="tabPageSales"   text="业绩" showCondition="物资" ></TabPage>
			<TabPage name="tabPageLabReport"   text="检测报告" showCondition="物资" ></TabPage>
			<TabPage name="tabPageLabEqp" text="试验设备" showCondition="物资" ></TabPage>
			<TabPage name="tabPageMEqp" text="生产装备" showCondition="物资" ></TabPage>
			<TabPage name="tabCoreStaff" text="主要人员" showCondition="物资" ></TabPage>
			<TabPage name="tabPageGwGx" text="与国家电网公司系统人员的关系" showCondition="物资" ></TabPage>
			<TabPage name="tabPageCapacityReview" text="履约能力评价" showCondition="物资" ></TabPage>
			<TabPage name="tabPageFastGrowReview" text="高质量发展评价" showCondition="物资" ></TabPage>
		</BaseInfo>

		<!--基础信息模块(服务招标)-->
		<BaseServiceInfo columName="PURCHASE_TYPE" tableName="OS_ZB_PURCHASE_PROJECT_INFO">
			<TabPage name="tabPageBaseInfo" text="投标人概况" showCondition="*" ></TabPage>
			<TabPage name="tabPageFinace" text="年度财务报告" showCondition="服务" ></TabPage>
			<TabPage name="tabAuthPerson" text="投标授权人" showCondition="服务" ></TabPage>
			<TabPage name="tabQualifyCert" text="企业资质等级证书原件影印件" showCondition="服务" ></TabPage>
			<TabPage name="tabPerformanceCredit" text="企业履约及信誉" showCondition="服务" ></TabPage>
			<!--<TabPage name="tabBidderProfile" text="投标人概况表" showCondition="服务" ></TabPage>-->
			<TabPage name="tabPageCredit" text="信用信息" showCondition="服务" ></TabPage>
			<TabPage name="tabPageGwGx" text="与国家电网公司系统人员的关系" showCondition="服务" ></TabPage>
		</BaseServiceInfo>

		<!--投标文件制作模块 没啥作用 -->
		<GenBidInfo columName="PURCHASE_TYPE" tableName="OS_ZB_PURCHASE_PROJECT_INFO">
			<TabPage name="businessPdfFile" text="商务支持文件" showCondition="*" ></TabPage>
			<TabPage name="techPdfFile" text="技术支持文件" showCondition="*" ></TabPage>
		</GenBidInfo>
		
	</TabPageConfigs>

	<!--标段分类-->
	<FormTypes>
		<!--
			hideTabless（隐藏表格）： firstTable /secordTable 以及 firstTable,secordTable
			tableTitles（表格标题列）：xxx,xxx 
		-->
		<FormType type="设计" form="窗体1" hideTables="firstTable" tableTitles="*,工程业绩清单" >
			<MarkTypes>
				<!--hideTables="firstTable" tableTitles="*,近年承揽的类似项目情况表"-->
				<MarkType name="电网工程勘察设计-基建" />	
				<MarkType name="电网工程勘察设计-基建框架" />
				<MarkType name="可研设计一体化" />
			</MarkTypes>
		</FormType>
		
		<FormType type="施工" form="窗体1" hideTables="*" tableTitles="工程业绩清单（已投产）,工程业绩清单（未投产）">
			<MarkTypes>
				<MarkType name="电网工程施工-基建"/>	
				<MarkType name="电网工程施工-基建框架"/>
			</MarkTypes>
		</FormType>

		<FormType type="监理" form="窗体1" hideTables="firstTable" tableTitles="*,工程业绩清单">
			<MarkTypes>
				<MarkType name="电网工程设计监理-基建"/>	
				<MarkType name="电网工程施工监理-基建"/>
				<MarkType name="电网工程施工监理-基建框架"/>
			</MarkTypes>
		</FormType>

		<FormType type="电网工程总承包" form="窗体1" hideTables="*" tableTitles="已完成工程业绩清单,正在设计和新承接的项目情况表">
			<MarkTypes>
				<MarkType name="电网工程总承包"/>
			</MarkTypes>
		</FormType>
	
		<!--“*”只能在其他服务里面配-->
		<FormType type="其他服务" form="窗体2" hideTables="firstTable" tableTitles="*,近年承揽的类似项目情况表">
			<MarkTypes>
				<MarkType name="*"/>
			</MarkTypes>
		</FormType>
	</FormTypes>
	
	<ResponseTables>
		<!--
			转义字符: 
			< === &lt;
			> === &gt;
			\r === &#xD;
			\n === &#xA;
			\r\n === &#xD;&#xA;
		-->

		<!--响应情况配置(商务-addTime0912)-->
		<ResponseTable type="shangwu" info="本次通用" condition='PurchaseType=="服务招标"' form="FrmResposeBusinessV3">
		</ResponseTable>

		<!--响应情况配置(技术)-->
		<ResponseTable type="jishu" info="本次通用" condition='PurchaseType=="服务招标"' form="FrmResposeTechV3">
		</ResponseTable>

		<!--响应情况配置(商务)-->
		<ResponseTable type="shangwu" info="服务框架" condition='IsServiceFrame=="是" and PurchaseType=="服务招标"' form="FrmResposeBusinessFs">
			<ModelConfigs>
				<!--服务框架-->
				<ModelConfig  type="服务框架-设计" name="电网工程勘察设计-110kV及以下" form="QsDocControl_1"></ModelConfig>
				<ModelConfig  type="服务框架-监理" name="电网工程施工监理-220kV及以下" form="QsDocControl_2"></ModelConfig>
				<ModelConfig  type="服务框架-其他" name="人力资源服务-培训服务,中介服务-审计服务,职工疗养服务" form="QsDocControl_4"></ModelConfig>
				<ModelConfig  type="服务框架-施工" name="*" form="QsDocControl_3"></ModelConfig>
			</ModelConfigs>
		</ResponseTable>

		<ResponseTable type="shangwu" info="公开招标" condition='IsServiceFrame!="是" and PurchaseType=="服务招标"' form="FrmResposeBusiness">
			<!--公开招标-->
			<ModelConfigs>

				<ModelConfig  type="设计"  name="电网工程勘察设计-基建,电网工程勘察设计-基建框架,可研设计一体化" modelTitles="输变电工程设计资质,勘察资质"  showComplexAgreement="true"></ModelConfig>

				<ModelConfig  type="施工"  name="电网工程施工-基建,电网工程施工-基建框架,电网工程施工-技改大修,电网工程施工-技改大修框架"
							  modelTitles="电力工程施工总承包或送变电&#xD;&#xA;（输变电）工程专业承包资质,承装（修、试）电力设施许可证,安全生产许可证"></ModelConfig>

				<ModelConfig  type="监理"  name="电网工程设计监理-基建,电网工程施工监理-基建,电网工程施工监理-基建框架" modelTitles="电力工程监理企业资质或&#xD;&#xD;工程监理综合资质" ></ModelConfig>

				<ModelConfig  type="生产辅助等"  name="生产辅助技改大修工程施工,小型基建工程施工" modelTitles="安全生产许可证(若有要求）,招标文件要求的相关资质" ></ModelConfig>

				<ModelConfig  type="电网工程总承包"  name="电网工程总承包"
							  modelTitles="输变电工程设计资质,勘察资质,电力工程施工总承包或送变电&#xD;&#xA;（输变电）工程专业承包资质,承装（修、试）电力设施许可证,安全生产许可证" ></ModelConfig>

				<ModelConfig  type="其他服务"  name="*" modelTitles="招标文件要求的相关资质" showComplexAgreement="true"></ModelConfig>
			</ModelConfigs>
		</ResponseTable>

		<!--响应情况配置(技术)-->
		<ResponseTable type="jishu" info="服务招标" condition='IsServiceFrame=="是" and PurchaseType=="服务招标"' form="FrmResposeTechFs">
			<!--服务框架-->
			<ModelConfigs>
				<ModelConfig  type="目前所有" name="*" form="窗体2"></ModelConfig>
			</ModelConfigs>
		</ResponseTable>

		<ResponseTable type="jishu" info="公开招标"  condition='IsServiceFrame!="是" and PurchaseType=="服务招标"' form="FrmResposeTech">
			<!--公开招标-->
			<ModelConfigs>
				<ModelConfig  type="设计"  name="电网工程勘察设计-基建,电网工程勘察设计-基建框架,可研设计一体化" form="BaseDesignControl"></ModelConfig>
				<ModelConfig  type="施工"  name="电网工程施工-基建,电网工程施工-基建框架" form="BaseWorkControl"></ModelConfig>
				<ModelConfig  type="监理"  name="电网工程设计监理-基建,电网工程施工监理-基建,电网工程施工监理-基建框架" form="BaseSupervisorControl"></ModelConfig>
				<ModelConfig  type="电网工程总承包"  name="电网工程总承包" form="StateGridEPCControl"></ModelConfig>
				<ModelConfig  type="其他服务"  name="*" form="OtherServicesControl"></ModelConfig>
			</ModelConfigs>
		</ResponseTable>
		
	</ResponseTables>

	<!--数据拆分 导出或云上传 配置-->
	<CloudUploads>

		<!--【物资/服务】 商务和技术公共部分  项目信息表 供应商基本信息  -->
		<CloudUpload condition="" type="*" diff="*" tables="OS_ZB_SUPPLIER_INFO"></CloudUpload>

		<!--【物资/服务】 项目信息-->
		<CloudUpload condition=" AND MARK_NO='{markNo}' AND SELECTED='1' {??packName}" type="*" diff="*" tables="OS_ZB_PURCHASE_PROJECT_INFO"></CloudUpload>
		
		<!--【物资/服务】 商务和技术公共部分  导入的签章文件信息-->
		<CloudUpload condition=" AND MARK_NO='{markNo}' AND SUPPLIER_ID ='{supplierId}' AND FILE_TYPE='{fileType}' {??packName}" type="*" diff="*" tables="OS_ZB_SUPPLIER_BID_ATTACHMENT"></CloudUpload>

		<!--【物资/服务&&商务部分】  保证金主表信息-->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}'" type="*" diff="商务" tables="OS_ZB_SUPPLIER_DEPOSIT"></CloudUpload>
		
		<!--【物资/服务&&商务部分】  保证金详情跟商务-->
		<CloudUpload condition=" AND MARK_NO='{markNo}' AND SUPPLIER_ID ='{supplierId}' {??packName}" type="*" diff="商务" tables="OS_ZB_SUPPLIER_DEPOSIT_DETAIL,OS_ZB_BIAO_PERSON_RELATIONS"></CloudUpload>

		<!--【物资&&技术部分】  业绩文件  试验报告 认证证书 试验设备 制造工艺 产品信息 产品部件 生产能力  保证金详情  技术响应情况信息(物资和服务)
		主要人员 生产装备-->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND MARK_NO='{markNo}' {??packName}" type="物资" diff="技术"
					tables="OS_ZB_SUPPLIER_BIAO_SALES_INFO,OS_ZB_SUPPLIER_BIAO_REPORT_INFO,OS_ZB_SUPPLIER_BIAO_CERT_INFO,
					OS_ZB_SUPPLIER_BIAO_LABEQP_INFO,OS_ZB_SUPPLIER_BIAO_PROCESS_INFO,OS_ZB_SUPPLIER_PRODUCT_INFO
					,OS_ZB_SUPPLIER_PARTS_INFO,OS_ZB_SUPPLIER_BIAO_PRODUCT_PLACE,OS_ZB_SUPPLIER_BIAO_EXECUTE_EVALUATE
					,OS_ZB_SUPPLIER_BIAO_PRODUCT_COST_PARAMS,OS_ZB_SUPPLIER_RESPONSE_INFO_TECH,OS_ZB_BIAO_CORE_STAFF,OS_ZB_SUPPLIER_BIAO_MEQP_INFO,OS_ZB_SUPPLIER_BIAO_CHANGE_FILE
					,OS_ZB_SUPPLIER_BIAO_CRUX_EQUIP"
					></CloudUpload>

		<!--【物资&&商务部分】  供应商配送方案及售后服务  股权信息 财务信息  信用信息 资质业绩核实 资格预审 商务响应情况信息(物资和服务) 商务响应Mode表
			当是物资招标时  仅用用了OS_ZB_SUPPLIER_AUTH_PERSON_INFO
		-->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND MARK_NO='{markNo}' {??packName}" type="物资" diff="商务"
					tables="OS_ZB_SUPPLIER_BIAO_ASERVICE_INFO,OS_ZB_SUPPLIER_BIAO_EQUITY_INFO
					,OS_ZB_SUPPLIER_BIAO_FINANCE_INFO,OS_ZB_SUPPLIER_BIAO_CREDIT_INFO
					,OS_ZB_SUPPLIER_QUALIFY_INFO,OS_ZB_SUPPLIER_RESPONSE_INFO_BUS,OS_ZB_SUPPLIER_BIAO_CHANGE_FILE"
					></CloudUpload>
		
		<!--物资 (商务/技术)通过type进行区分-->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND TYPE='{type}' AND MARK_NO='{markNo}' {??packName}" type="物资" diff="*"
					tables="OS_ZB_BIAO_PRE_QUALIFICATION"></CloudUpload>		
		
		<!--公共的响应数据【包含商务和技术】-->
		<CloudUpload condition="  AND FLAG='{type}' AND MARK_NO='{markNo}' {??packName}" type="*" diff="*"
					tables="OS_ZB_SUPPLIER_COMMON_RESPONSE_INFO"></CloudUpload>
		
		<!--【物资&&技术部分】  发票信息 -->	
		<CloudUpload type="物资" diff="技术"
					tables="OS_ZB_BILL_INFO" condition=" AND MARK_NO='{markNo}'" useSql="true">
			INSERT INTO db1.OS_ZB_BILL_INFO (
			ID,
			BILL_CODE,
			BILL_NUMBER,
			BILL_DATE,
			BILL_AMOUNT,
			BUYER_NAME,
			PRODUCT_NAME,
			UNIT,
			QUANTITY,
			UNIT_PRICE,
			AMOUNT,
			TAX_RATE,
			TAX_AMOUNT,
			MARK_NO,
			CREATE_TIME,
			UPDATE_TIME,
			PARENT_ID
			) SELECT
			substr( RANDOM(), 2, 7 ) || substr( RANDOM(), 2, 7 ) || t0.BILL_NUMBER AS ID,
			t0.BILL_CODE,
			t0.BILL_NUMBER,
			t0.BILL_DATE,
			t0.BILL_AMOUNT,
			t0.BUYER_NAME,
			t0.PRODUCT_NAME,
			t0.UNIT,
			t0.QUANTITY,
			t0.UNIT_PRICE,
			t0.AMOUNT,
			t0.TAX_RATE,
			t0.TAX_AMOUNT,
			t0.MARK_NO,
			t0.CREATE_TIME,
			t0.UPDATE_TIME,
			t0.PARENT_ID
			FROM
			OS_ZB_BILL_INFO t0
			INNER JOIN OS_ZB_SUPPLIER_BIAO_SALES_INFO s1 ON t0.PARENT_ID = s1.RELATE_ID
			WHERE
			s1.MARK_NO ='{markNo}';
		</CloudUpload>

		<!--【服务&&技术部分】 -->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND MARK_NO='{markNo}' {??packName}" type="服务" diff="技术"
					tables="OS_ZB_SUPPLIER_BIAO_DESIGNER_CORE_PEOPEL,OS_ZB_SUPPLIER_BIAO_PROPOSE_SUBPACK
					,OS_ZB_SUPPLIER_BIAO_SAFE_QUALITY_PROGRESS,OS_ZB_SUPPLIER_BIAO_DURATION_RESPONSE,OS_ZB_STECH_RESPONSE_INFO,
					OS_BIAO_PROPOSED_SUBPACK_INFO,OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE,OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD"
					></CloudUpload>
		
		<!--【服务&&技术【技术规范书 工程量清单】 -->
		<CloudUpload condition=" AND MARK_NO='{markNo}' {??packName}" type="服务" diff="技术" 
					 tables="SYS_MODEL_CONFIG"></CloudUpload>

		<!--【服务&&商务部分】 -->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND MARK_NO='{markNo}' {??packName}" type="服务" diff="商务"
					tables="OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO,OS_ZB_SUPPLIER_BIAO_QUALIFY_CERT,OS_ZB_SUPPLIER_BIAO_FINANCE_INFO,OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT
		,OS_ZB_SBUSINESS_RESPONSE_INFO,OS_ZB_SBUSINESS_MODE,OS_ZB_SUPPLIER_BIAO_IMP_EXPERIENCE,OS_ZB_SUPPLIER_BIAO_PROJECT_RECORD"></CloudUpload>

		<!--【服务&&（技术+商务)】 -->
		<CloudUpload condition=" AND SUPPLIER_ID='{supplierId}' AND MARK_NO='{markNo}' AND TYPE='{type}' {??packName}" type="服务" diff="*"
					tables="OS_ZB_SUPPLIER_BIAO_OTHER_CONTENTS"></CloudUpload>

		<!--【公共不迁移的表1】 -->
		<CloudUpload type="*" diff="*"
					ingoreTables="OS_ZB_AUTH_PERSON_RELATE,OS_ZB_SUPPLIER_ASERVICE_INFO,OS_ZB_SUPPLIER_BRAND_AGENT
		,OS_ZB_SUPPLIER_CAPACITY_INFO,OS_ZB_SUPPLIER_CERT_INFO,OS_ZB_SUPPLIER_CREDIT_INFO,OS_ZB_SUPPLIER_EQUITY_INFO
		,OS_ZB_SUPPLIER_FINANCE_INFO,OS_ZB_SUPPLIER_LABEQP_INFO,OS_ZB_SUPPLIER_MEQP_INFO,OS_ZB_SUPPLIER_PARTS_INFO
		,OS_ZB_SUPPLIER_PROCESS_INFO,OS_ZB_SUPPLIER_PRODUCT_PLACE,OS_ZB_SUPPLIER_QUALIFY_INFO
		,OS_ZB_SUPPLIER_REPORT_INFO,OS_ZB_SUPPLIER_RESPONSE_INFO,OS_ZB_SUPPLIER_SALES_INFO
		,SYS_UPLOAD_INFO,ZB_TOOL_VERSION_INFO,SYS_UPLOAD_CONFIG_INFO,TABLE01,TABLE02,TABLE03,TABLE04,TABLE_TEST" ></CloudUpload>
		
		<!--【公共部分(商务+技术)】 上传的附件部分 isXXX 为true 后面的条件才会匹配 
		系统附件
		-->
		<CloudUpload type="*" diff="*" tables="SYS_FILE_INFO"  useSql="true">
			insert into db1.SYS_FILE_INFO
			--pdf文件/word文件
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			INNER JOIN
			OS_ZB_SUPPLIER_BID_ATTACHMENT ON OS_ZB_SUPPLIER_BID_ATTACHMENT.ID = SYS_FILE_INFO.RELATED_ID
			WHERE OS_ZB_SUPPLIER_BID_ATTACHMENT.MARK_NO = '{markNo}' AND OS_ZB_SUPPLIER_BID_ATTACHMENT.FILE_TYPE='{fileType}'
			{OS_ZB_SUPPLIER_BID_ATTACHMENT.PACK_NAME}
			union
			--ecp压缩文件
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}'
			AND RELATED_KEY='{relatedKey}'
			union
			--价格投标文件压缩包+报价文件压缩包+上架商品压缩包
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			where {isMatchJishu} AND RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' and RELATED_ID = '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}'
			AND RELATED_KEY in ('jishuBidOpenZip','jishuPriceZip','jishuGoods')
			union
			--授权人信息
			select SYS_FILE_INFO.*
			FROM OS_ZB_SUPPLIER_AUTH_PERSON_INFO inner JOIN SYS_FILE_INFO ON SYS_FILE_INFO.RELATED_ID = OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ID
			where {isMatchShangwu}
			union
			--保证金附件部分(按项目来)
			select  SYS_FILE_INFO.*  from SYS_FILE_INFO
			inner join OS_ZB_SUPPLIER_DEPOSIT on OS_ZB_SUPPLIER_DEPOSIT.ID = SYS_FILE_INFO.RELATED_ID
			where {isMatchShangwu} AND  RELATED_KEY in('accountBaseInfoAttach','purchaseAccountInfoAttach','bidDetailsInfoAttach') and RELATED_PAGE='OS_ZB_SUPPLIER_DEPOSIT'
			and OS_ZB_SUPPLIER_DEPOSIT.PROJECT_NO='{projectNo}'
			union
			--发票图片+结果截图
			select distinct SYS_FILE_INFO.*  from SYS_FILE_INFO
			inner join OS_ZB_BILL_INFO on SYS_FILE_INFO.RELATED_ID =OS_ZB_BILL_INFO.BILL_CODE ||'_'||OS_ZB_BILL_INFO.BILL_NUMBER
			inner join OS_ZB_SUPPLIER_BIAO_SALES_INFO on OS_ZB_SUPPLIER_BIAO_SALES_INFO.RELATE_ID =OS_ZB_BILL_INFO.PARENT_ID
			where OS_ZB_SUPPLIER_BIAO_SALES_INFO.MARK_NO='{markNo}' {OS_ZB_SUPPLIER_BIAO_SALES_INFO.PACK_NAME} and {isMatchJishu}
			union
			--资格预审(不区分商务技术)
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			where RELATED_PAGE ='OS_ZB_SUPPLIER_BID_ATTACHMENT' AND RELATED_ID = '{projectNo}&amp;&amp;{markNo}&amp;&amp;{packName}'
			AND RELATED_KEY ='ZgysZip'
			union
			--工程量清单(技术规范书）
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			WHERE {isMatchJishu} AND RELATED_PAGE ='OS_TECH_SPECIFY_BOOK' AND RELATED_ID LIKE '{markNo}&amp;&amp;{packName}&amp;&amp;%'
			AND RELATED_KEY IN ('gclqc-pdf','gclqc-excel')
			union
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			WHERE {isMatchJishu} AND
			RELATED_PAGE ='OS_TECH_SPECIFY_BOOK' AND RELATED_KEY LIKE '{markNo}_{packName}_%'
			--不良行为信息附件
			union
			SELECT SYS_FILE_INFO.*
			FROM SYS_FILE_INFO
			WHERE RELATED_PAGE ='OS_ZB_BAD_BEHAVIOR_INFO' AND  RELATED_KEY ='proveCorrect';
		</CloudUpload>

		<!--不良行为信息-->
		<CloudUpload type="*" diff="*" tables="OS_ZB_BAD_BEHAVIOR_INFO"  useSql="true">
			insert into db1.OS_ZB_BAD_BEHAVIOR_INFO
			select * from OS_ZB_BAD_BEHAVIOR_INFO;
		</CloudUpload>
		
	</CloudUploads>
	
</configuration>