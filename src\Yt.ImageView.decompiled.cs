using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints)]
[assembly: TargetFramework(".NETFramework,Version=v4.6.1", FrameworkDisplayName = ".NET Framework 4.6.1")]
[assembly: AssemblyCompany("raoyutian")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Copyright (C) 2023 raoyutian")]
[assembly: AssemblyDescription("ImageView")]
[assembly: AssemblyFileVersion("1.2.0")]
[assembly: AssemblyInformationalVersion("1.2.0")]
[assembly: AssemblyProduct("Yt.ImageView")]
[assembly: AssemblyTitle("Yt.ImageView")]
[assembly: AssemblyVersion("*******")]
namespace System.Windows.Forms;

public class ImageView : UserControl
{
	private Image _Image;

	private Bitmap graphicsbmp;

	private Point mouseleftdown;

	private IContainer components;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem contextMenuItemZoomIn;

	private ToolStripMenuItem contextMenuItemZoomOut;

	private ToolStripMenuItem contextMenuItemRotationR;

	private ToolStripMenuItem contextMenuItemRotationL;

	private Panel viewpanel;

	private Panel panelparent;

	private VScrollBar vScrollBar;

	private HScrollBar hScrollBar;

	private Panel panelBom;

	private Panel panelBomRight;

	private Panel panelBomLeft;

	private Panel panelRight;

	private Label labelinfo;

	public Image Image
	{
		get
		{
			return _Image;
		}
		set
		{
			if (value != null)
			{
				_Image = value;
				viewpanel.Size = _Image.Size;
				imagew = viewpanel.Size.Width;
				imageh = viewpanel.Size.Height;
				graphicsbmp = new Bitmap(imagew, imageh);
				showinfo($"size:{_Image.Width}x{_Image.Height};dpi:{_Image.VerticalResolution}");
				viewpanel_BackgroundImageChanged(null, null);
				SetImageLocation();
			}
		}
	}

	public float ImageScale { get; set; }

	private int imagew { get; set; }

	private int imageh { get; set; }

	private int graphicsw { get; set; }

	private int graphicsh { get; set; }

	private void setDoubleBuffered(Control control)
	{
		control?.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic).SetValue(control, true, null);
	}

	public ImageView()
	{
		InitializeComponent();
		Application.EnableVisualStyles();
		SetStyle(ControlStyles.AllPaintingInWmPaint, value: true);
		SetStyle(ControlStyles.OptimizedDoubleBuffer, value: true);
		SetStyle(ControlStyles.UserPaint, value: true);
		DoubleBuffered = true;
		setDoubleBuffered(viewpanel);
		setDoubleBuffered(panelparent);
		UpdateStyles();
		viewpanel.MouseDown += viewpanel_MouseDown;
		viewpanel.MouseUp += viewpanel_MouseUp;
		viewpanel.MouseMove += viewpanel_MouseMove;
		viewpanel.MouseWheel += viewpanel_MouseWheel;
		base.MouseWheel += viewpanel_MouseWheel;
	}

	private void viewpanel_MouseWheel(object sender, MouseEventArgs e)
	{
		try
		{
			if (e.Delta > 0)
			{
				ZoomIn();
			}
			else
			{
				ZoomOut();
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	private void viewpanel_BackgroundImageChanged(object sender, EventArgs e)
	{
		if (Image != null)
		{
			int num = Math.Max(viewpanel.Width, viewpanel.Height);
			int num2 = Math.Min(base.Width, base.Height);
			if (num > num2)
			{
				ImageScale = (float)num2 / (float)num;
			}
			else
			{
				ImageScale = 1f;
			}
			Zoom(ImageScale);
		}
	}

	private void SetImageLocation()
	{
		if (Image == null)
		{
			return;
		}
		try
		{
			Point location = default(Point);
			int num = Math.Max(viewpanel.Width, viewpanel.Height);
			int num2 = Math.Min(base.Width, base.Height);
			if (num <= num2)
			{
				location.X = Convert.ToInt32((double)(panelparent.Width - viewpanel.Width) * 0.5);
				location.Y = Convert.ToInt32((double)(panelparent.Height - viewpanel.Height) * 0.5);
				viewpanel.Location = location;
			}
			else
			{
				location.X = Convert.ToInt32((double)(panelparent.Width - viewpanel.Width) * 0.5);
				location.Y = Convert.ToInt32((double)(panelparent.Height - viewpanel.Height) * 0.5);
				viewpanel.Location = location;
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.Message);
		}
	}

	public void ZoomIn()
	{
		Zoom(1.25f);
	}

	public void ZoomOut()
	{
		Zoom(0.8f);
	}

	public void Zoom(float scale = 1f)
	{
		if (Image == null)
		{
			return;
		}
		try
		{
			int num = Convert.ToInt32((float)viewpanel.Width * scale);
			int num2 = Convert.ToInt32((float)viewpanel.Height * scale);
			Rectangle bounds = Screen.PrimaryScreen.Bounds;
			double num3 = bounds.Width;
			double num4 = bounds.Height;
			if (!((double)num >= num3 * 15.0) && !((double)num2 >= num4 * 15.0))
			{
				viewpanel.BackgroundImage = Image;
				viewpanel.Size = new Size(num, num2);
				viewpanel.Visible = true;
			}
			panelBom.Visible = viewpanel.Width >= panelparent.Width;
			if (panelBom.Visible)
			{
				hScrollBar.Value = 0;
			}
			panelRight.Visible = viewpanel.Height >= panelparent.Height;
			if (panelRight.Visible)
			{
				vScrollBar.Value = 0;
			}
			ImageScale = scale;
			SetImageLocation();
		}
		catch (Exception)
		{
		}
	}

	public void Rotation(int deg = 90)
	{
		if (Image != null && viewpanel.BackgroundImage != null)
		{
			switch (deg)
			{
			case 0:
				return;
			case 90:
				Image.RotateFlip(RotateFlipType.Rotate90FlipNone);
				break;
			default:
				Image.RotateFlip(RotateFlipType.Rotate270FlipNone);
				break;
			}
			viewpanel.BackgroundImage = null;
			viewpanel.BackgroundImage = Image;
			int num = viewpanel.Width;
			viewpanel.Width = viewpanel.Height;
			viewpanel.Height = num;
			SetImageLocation();
		}
	}

	private void contextMenuItemR_Click(object sender, EventArgs e)
	{
		Rotation();
	}

	private void contextMenuItemL_Click(object sender, EventArgs e)
	{
		Rotation(270);
	}

	private void MenuItemRotationR_Click(object sender, EventArgs e)
	{
		contextMenuItemR_Click(null, null);
	}

	private void MenuItemRotationL_Click(object sender, EventArgs e)
	{
		contextMenuItemL_Click(null, null);
	}

	private void MenuItemZoomIn_Click(object sender, EventArgs e)
	{
		ZoomIn();
	}

	private void MenuItemZoomOut_Click(object sender, EventArgs e)
	{
		ZoomOut();
	}

	private void viewpanel_MouseMove(object sender, MouseEventArgs e)
	{
		if (e.Button != MouseButtons.Left)
		{
			return;
		}
		Point location = viewpanel.Location;
		if ((location.X < 0 || location.Y <= 0) && e.Button == MouseButtons.Left && (vScrollBar.Visible || hScrollBar.Visible))
		{
			location.Offset(e.X - mouseleftdown.X, e.Y - mouseleftdown.Y);
			if (location.X >= 0)
			{
				location.X = 0;
			}
			if (location.Y >= 0)
			{
				location.Y = 0;
			}
			if (location.X <= panelparent.Width - viewpanel.Width)
			{
				location.X = panelparent.Width - viewpanel.Width;
			}
			if (location.Y <= panelparent.Height - viewpanel.Height)
			{
				location.Y = panelparent.Height - viewpanel.Height;
			}
			viewpanel.Location = location;
		}
	}

	private void viewpanel_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			mouseleftdown = e.Location;
			Cursor.Current = Cursors.Hand;
		}
	}

	private void viewpanel_MouseUp(object sender, MouseEventArgs e)
	{
		Cursor.Current = Cursors.Default;
	}

	private void ImageView_SizeChanged(object sender, EventArgs e)
	{
		viewpanel_BackgroundImageChanged(null, null);
	}

	private void vScrollBar_ValueChanged(object sender, EventArgs e)
	{
		try
		{
			int num = (panelparent.Height - viewpanel.Height) / 2;
			int num2 = (int)((double)vScrollBar.Value * 0.01 * (double)(viewpanel.Height - panelparent.Height) * 0.5);
			viewpanel.Location = new Point(viewpanel.Location.X, num - num2);
		}
		catch (Exception)
		{
		}
	}

	private void hScrollBar_ValueChanged(object sender, EventArgs e)
	{
		try
		{
			int num = (panelparent.Width - viewpanel.Width) / 2;
			int num2 = (int)((double)hScrollBar.Value * 0.01 * (double)(viewpanel.Width - panelparent.Width) * 0.5);
			viewpanel.Location = new Point(num - num2, viewpanel.Location.Y);
		}
		catch (Exception)
		{
		}
	}

	private void showinfo(string info = "")
	{
		labelinfo.Text = info;
		panelBomLeft.Width = labelinfo.Width + 10;
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
		this.contextMenuItemZoomIn = new System.Windows.Forms.ToolStripMenuItem();
		this.contextMenuItemZoomOut = new System.Windows.Forms.ToolStripMenuItem();
		this.contextMenuItemRotationR = new System.Windows.Forms.ToolStripMenuItem();
		this.contextMenuItemRotationL = new System.Windows.Forms.ToolStripMenuItem();
		this.viewpanel = new System.Windows.Forms.Panel();
		this.panelparent = new System.Windows.Forms.Panel();
		this.hScrollBar = new System.Windows.Forms.HScrollBar();
		this.vScrollBar = new System.Windows.Forms.VScrollBar();
		this.panelBom = new System.Windows.Forms.Panel();
		this.panelBomRight = new System.Windows.Forms.Panel();
		this.panelBomLeft = new System.Windows.Forms.Panel();
		this.labelinfo = new System.Windows.Forms.Label();
		this.panelRight = new System.Windows.Forms.Panel();
		this.contextMenuStrip1.SuspendLayout();
		this.panelparent.SuspendLayout();
		this.panelBom.SuspendLayout();
		this.panelBomLeft.SuspendLayout();
		this.panelRight.SuspendLayout();
		base.SuspendLayout();
		this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[4] { this.contextMenuItemZoomIn, this.contextMenuItemZoomOut, this.contextMenuItemRotationR, this.contextMenuItemRotationL });
		this.contextMenuStrip1.Name = "contextMenuStrip1";
		this.contextMenuStrip1.Size = new System.Drawing.Size(182, 92);
		this.contextMenuStrip1.Text = "右键菜单";
		this.contextMenuItemZoomIn.Name = "contextMenuItemZoomIn";
		this.contextMenuItemZoomIn.ShortcutKeyDisplayString = "Ctrl++";
		this.contextMenuItemZoomIn.ShortcutKeys = System.Windows.Forms.Keys.Oemplus | System.Windows.Forms.Keys.Control;
		this.contextMenuItemZoomIn.Size = new System.Drawing.Size(181, 22);
		this.contextMenuItemZoomIn.Text = "放大";
		this.contextMenuItemZoomIn.Click += new System.EventHandler(MenuItemZoomIn_Click);
		this.contextMenuItemZoomOut.Name = "contextMenuItemZoomOut";
		this.contextMenuItemZoomOut.ShortcutKeyDisplayString = "Ctrl+-";
		this.contextMenuItemZoomOut.ShortcutKeys = System.Windows.Forms.Keys.OemMinus | System.Windows.Forms.Keys.Control;
		this.contextMenuItemZoomOut.Size = new System.Drawing.Size(181, 22);
		this.contextMenuItemZoomOut.Text = "缩小";
		this.contextMenuItemZoomOut.Click += new System.EventHandler(MenuItemZoomOut_Click);
		this.contextMenuItemRotationR.Name = "contextMenuItemRotationR";
		this.contextMenuItemRotationR.ShortcutKeyDisplayString = "";
		this.contextMenuItemRotationR.ShortcutKeys = System.Windows.Forms.Keys.R | System.Windows.Forms.Keys.Control;
		this.contextMenuItemRotationR.Size = new System.Drawing.Size(181, 22);
		this.contextMenuItemRotationR.Text = "瞬时针旋转";
		this.contextMenuItemRotationR.Click += new System.EventHandler(contextMenuItemR_Click);
		this.contextMenuItemRotationL.Name = "contextMenuItemRotationL";
		this.contextMenuItemRotationL.ShortcutKeyDisplayString = "";
		this.contextMenuItemRotationL.ShortcutKeys = System.Windows.Forms.Keys.L | System.Windows.Forms.Keys.Control;
		this.contextMenuItemRotationL.Size = new System.Drawing.Size(181, 22);
		this.contextMenuItemRotationL.Text = "逆时针旋转";
		this.contextMenuItemRotationL.Click += new System.EventHandler(contextMenuItemL_Click);
		this.viewpanel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
		this.viewpanel.Location = new System.Drawing.Point(80, 49);
		this.viewpanel.Name = "viewpanel";
		this.viewpanel.Size = new System.Drawing.Size(72, 57);
		this.viewpanel.TabIndex = 6;
		this.panelparent.BackColor = System.Drawing.Color.FromArgb(64, 64, 64);
		this.panelparent.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
		this.panelparent.Controls.Add(this.viewpanel);
		this.panelparent.Dock = System.Windows.Forms.DockStyle.Fill;
		this.panelparent.Location = new System.Drawing.Point(0, 0);
		this.panelparent.Name = "panelparent";
		this.panelparent.Size = new System.Drawing.Size(234, 163);
		this.panelparent.TabIndex = 7;
		this.hScrollBar.Dock = System.Windows.Forms.DockStyle.Fill;
		this.hScrollBar.Location = new System.Drawing.Point(80, 0);
		this.hScrollBar.Maximum = 120;
		this.hScrollBar.Minimum = -120;
		this.hScrollBar.Name = "hScrollBar";
		this.hScrollBar.Size = new System.Drawing.Size(156, 20);
		this.hScrollBar.TabIndex = 7;
		this.hScrollBar.ValueChanged += new System.EventHandler(hScrollBar_ValueChanged);
		this.vScrollBar.Dock = System.Windows.Forms.DockStyle.Fill;
		this.vScrollBar.Location = new System.Drawing.Point(0, 0);
		this.vScrollBar.Maximum = 120;
		this.vScrollBar.Minimum = -120;
		this.vScrollBar.Name = "vScrollBar";
		this.vScrollBar.Size = new System.Drawing.Size(18, 163);
		this.vScrollBar.TabIndex = 8;
		this.vScrollBar.ValueChanged += new System.EventHandler(vScrollBar_ValueChanged);
		this.panelBom.BackColor = System.Drawing.SystemColors.Control;
		this.panelBom.Controls.Add(this.hScrollBar);
		this.panelBom.Controls.Add(this.panelBomRight);
		this.panelBom.Controls.Add(this.panelBomLeft);
		this.panelBom.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.panelBom.Location = new System.Drawing.Point(0, 163);
		this.panelBom.Name = "panelBom";
		this.panelBom.Size = new System.Drawing.Size(252, 20);
		this.panelBom.TabIndex = 9;
		this.panelBom.Visible = false;
		this.panelBomRight.Dock = System.Windows.Forms.DockStyle.Right;
		this.panelBomRight.Location = new System.Drawing.Point(236, 0);
		this.panelBomRight.Name = "panelBomRight";
		this.panelBomRight.Size = new System.Drawing.Size(16, 20);
		this.panelBomRight.TabIndex = 1;
		this.panelBomLeft.Controls.Add(this.labelinfo);
		this.panelBomLeft.Dock = System.Windows.Forms.DockStyle.Left;
		this.panelBomLeft.ForeColor = System.Drawing.Color.Red;
		this.panelBomLeft.Location = new System.Drawing.Point(0, 0);
		this.panelBomLeft.MinimumSize = new System.Drawing.Size(80, 0);
		this.panelBomLeft.Name = "panelBomLeft";
		this.panelBomLeft.Size = new System.Drawing.Size(80, 20);
		this.panelBomLeft.TabIndex = 0;
		this.labelinfo.AutoSize = true;
		this.labelinfo.ForeColor = System.Drawing.Color.Black;
		this.labelinfo.Location = new System.Drawing.Point(10, 4);
		this.labelinfo.Name = "labelinfo";
		this.labelinfo.Size = new System.Drawing.Size(0, 12);
		this.labelinfo.TabIndex = 0;
		this.panelRight.Controls.Add(this.vScrollBar);
		this.panelRight.Dock = System.Windows.Forms.DockStyle.Right;
		this.panelRight.Location = new System.Drawing.Point(234, 0);
		this.panelRight.Name = "panelRight";
		this.panelRight.Size = new System.Drawing.Size(18, 163);
		this.panelRight.TabIndex = 10;
		this.panelRight.Visible = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 12f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.Silver;
		this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
		this.ContextMenuStrip = this.contextMenuStrip1;
		base.Controls.Add(this.panelparent);
		base.Controls.Add(this.panelRight);
		base.Controls.Add(this.panelBom);
		this.DoubleBuffered = true;
		base.Name = "ImageView";
		base.Size = new System.Drawing.Size(252, 183);
		base.SizeChanged += new System.EventHandler(ImageView_SizeChanged);
		this.contextMenuStrip1.ResumeLayout(false);
		this.panelparent.ResumeLayout(false);
		this.panelBom.ResumeLayout(false);
		this.panelBomLeft.ResumeLayout(false);
		this.panelBomLeft.PerformLayout();
		this.panelRight.ResumeLayout(false);
		base.ResumeLayout(false);
	}
}
