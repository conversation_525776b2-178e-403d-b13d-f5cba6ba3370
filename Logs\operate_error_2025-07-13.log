2025-07-13 10:18:19.6090|TRACE|MyCommon.util.LogsHelper|  message:第9行，设备型号数据不得为空
第9行，数量数据不得为空
|System.Exception: 第9行，设备型号数据不得为空
第9行，数量数据不得为空

   在 MyCommon.Repository.common.BaseAbstractRepository`1.ImportExcel(IList`1& entities, Int32 titleRow, Func`3 CheckAction, String businessId, Int32 startSheet)
   在 ess_zbfz_main.baseInfoFrm.FrmLabeqpInfo.btnImport_Click(Object sender, EventArgs e)
2025-07-13 10:47:27.5430|TRACE|MyCommon.util.LogsHelper|  message:第16行，设备名称数据不得为空
第16行，数量数据不得为空
第16行，使用年限(年)数据不得为空
第16行，制造商原产地数据不得为空
|System.Exception: 第16行，设备名称数据不得为空
第16行，数量数据不得为空
第16行，使用年限(年)数据不得为空
第16行，制造商原产地数据不得为空

   在 MyCommon.Repository.common.BaseAbstractRepository`1.ImportExcel(IList`1& entities, Int32 titleRow, Func`3 CheckAction, String businessId, Int32 startSheet)
   在 ess_zbfz_main.baseInfoFrm.FrmMeqpInfo.btnImport_Click(Object sender, EventArgs e)
