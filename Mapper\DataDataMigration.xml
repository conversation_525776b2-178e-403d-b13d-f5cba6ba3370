﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--迁移语句（不良行为） db是数据源-->
	<insert id="migrationWithBadBehivor">
		<!--删除非本批次的数据-->
		DELETE FROM OS_ZB_BAD_BEHAVIOR_INFO WHERE 1=1  AND PROJECT_NO  != '{projectNo}' ;
		INSERT INTO OS_ZB_BAD_BEHAVIOR_INFO (
		ID,
		PROCESSING_WAY,
		AGENCY,
		PROJECT_NO,
		PROJECT_NAME,
		MARK_NO,
		MARK_INFO,
		PACK_NAME,
		PROJECT_UNIT,
		ITEM_NAME,
		INVOLVED_TYPE,
		SPECIFIC_CONTENT,
		SUPPLIER_NAME,
		RELATIONSHIP_WITH_ANOTHER,
		SUPPLIER_CONTACT_PERSON,
		SUPPLIER_CONTACT_PHONE,
		SUPPLIER_EMAIL,
		VERIFICATION_DATE,
		CAUSE_OF_PROBLEM,
		RECTIFICATION_MEASURES,
		IS_INTERVIEW_SUGGESTED,
		INTERVIEW_DATE,
		PROCESSING_RESULT,
		REMARKS
		)
		SELECT t0.ID,
		t0.PROCESSING_WAY,
		t0.AGENCY,
		t0.PROJECT_NO,
		t0.PROJECT_NAME,
		t0.MARK_NO,
		t0.MARK_INFO,
		t0.PACK_NAME,
		t0.PROJECT_UNIT,
		t0.ITEM_NAME,
		t0.INVOLVED_TYPE,
		t0.SPECIFIC_CONTENT,
		t0.SUPPLIER_NAME,
		t0.RELATIONSHIP_WITH_ANOTHER,
		t0.SUPPLIER_CONTACT_PERSON,
		t0.SUPPLIER_CONTACT_PHONE,
		t0.SUPPLIER_EMAIL,
		t0.VERIFICATION_DATE,
		t0.CAUSE_OF_PROBLEM,
		t0.RECTIFICATION_MEASURES,
		t0.IS_INTERVIEW_SUGGESTED,
		t0.INTERVIEW_DATE,
		t0.PROCESSING_RESULT,
		t0.REMARKS
		FROM db1.OS_ZB_BAD_BEHAVIOR_INFO t0
		WHERE 1 = 1 AND ID NOT IN (select ID FROM OS_ZB_BAD_BEHAVIOR_INFO);
	</insert>




</mapper>