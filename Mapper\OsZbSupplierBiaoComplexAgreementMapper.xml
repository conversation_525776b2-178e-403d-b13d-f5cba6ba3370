﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>


	<!--
		查询列表信息
	-->
	<select id="selectList">
		SELECT OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.*,
		'已上传数(' || count(complex_fj.id) || ')' AS complex_fj
		FROM OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT
		left JOIN
		sys_file_info AS complex_fj ON complex_fj.RELATED_ID = OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID AND complex_fj.RELATED_KEY = 'complex_fj'
		group by OS_ZB_SUPPLIER_BIAO_COMPLEX_AGREEMENT.ID
	</select>
	
	

</mapper>
