#!/usr/bin/env python3
"""
验证处理结果的脚本

检查生成的文档质量和内容
"""

import sys
import os
from docx import Document

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def validate_document(doc_path):
    """
    验证单个文档
    
    Args:
        doc_path: 文档路径
    """
    try:
        doc = Document(doc_path)
        
        print(f"\n=== 验证文档: {os.path.basename(doc_path)} ===")
        print(f"段落数: {len(doc.paragraphs)}")
        print(f"表格数: {len(doc.tables)}")
        
        # 检查段落内容
        non_empty_paragraphs = [p for p in doc.paragraphs if p.text.strip()]
        print(f"非空段落数: {len(non_empty_paragraphs)}")
        
        if non_empty_paragraphs:
            print("前5个段落内容:")
            for i, paragraph in enumerate(non_empty_paragraphs[:5]):
                print(f"  {i+1}. {paragraph.text.strip()}")
        
        # 检查表格内容
        for table_idx, table in enumerate(doc.tables):
            print(f"\n表格 {table_idx + 1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 检查是否还有"(投标人填写)"
            fill_cells = []
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    if any(phrase in cell.text for phrase in ["(投标人填写)", "（投标人填写）", "投标人填写"]):
                        fill_cells.append((row_idx, cell_idx, cell.text.strip()))
            
            if fill_cells:
                print(f"  ⚠️  仍有{len(fill_cells)}个未填写单元格:")
                for row_idx, cell_idx, text in fill_cells[:3]:  # 只显示前3个
                    print(f"    位置({row_idx}, {cell_idx}): {text}")
                if len(fill_cells) > 3:
                    print(f"    ... 还有{len(fill_cells) - 3}个")
            else:
                print("  ✅ 所有单元格都已填写")
            
            # 显示前几行内容
            if table.rows:
                print("  前3行内容:")
                for row_idx, row in enumerate(table.rows[:3]):
                    row_text = " | ".join([cell.text.strip() for cell in row.cells])
                    print(f"    {row_idx + 1}: {row_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def main():
    """
    主函数
    """
    # 测试文档路径
    test_folder = r"F:\2025\7月\国网安徽\国网安徽电力2025年非电网及办公用品物资框架协议公开招标采购_招标文件包\办公家具\包1_完整招标文件_7534782003872905\包1_技术规范书_7534780372181727\国网安徽省电力有限公司_安徽公司零星物资框架协议采购非结构化固化ID编制（G00E-500024815-00005）"
    
    print("=" * 80)
    print("文档验证工具")
    print("=" * 80)
    
    # 查找生成的文档
    generated_files = []
    for file in os.listdir(test_folder):
        if file.startswith("G00E-500024815-00005") and file.endswith(".docx"):
            generated_files.append(os.path.join(test_folder, file))
    
    if not generated_files:
        print("❌ 未找到生成的文档")
        return
    
    print(f"✅ 找到 {len(generated_files)} 个生成的文档")
    
    # 验证每个文档
    for doc_path in generated_files:
        validate_document(doc_path)
    
    print("\n" + "=" * 80)
    print("验证完成")

if __name__ == "__main__":
    main()
