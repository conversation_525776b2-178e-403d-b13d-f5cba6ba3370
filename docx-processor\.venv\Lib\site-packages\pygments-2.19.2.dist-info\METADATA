Metadata-Version: 2.4
Name: Pygments
Version: 2.19.2
Summary: Pygments is a syntax highlighting package written in Python.
Project-URL: Homepage, https://pygments.org
Project-URL: Documentation, https://pygments.org/docs
Project-URL: Source, https://github.com/pygments/pygments
Project-URL: Bug Tracker, https://github.com/pygments/pygments/issues
Project-URL: Changelog, https://github.com/pygments/pygments/blob/master/CHANGES
Author-email: <PERSON> <<EMAIL>>
Maintainer: <PERSON><PERSON><PERSON><PERSON> <PERSON>
Maintainer-email: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
License: BSD-2-Clause
License-File: AUTHORS
License-File: LICENSE
Keywords: syntax highlighting
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Text Processing :: Filters
Classifier: Topic :: Utilities
Requires-Python: >=3.8
Provides-Extra: plugins
Provides-Extra: windows-terminal
Requires-Dist: colorama>=0.4.6; extra == 'windows-terminal'
Description-Content-Type: text/x-rst

Pygments
~~~~~~~~

Pygments is a syntax highlighting package written in Python.

It is a generic syntax highlighter suitable for use in code hosting, forums,
wikis or other applications that need to prettify source code.  Highlights
are:

* a wide range of over 500 languages and other text formats is supported
* special attention is paid to details, increasing quality by a fair amount
* support for new languages and formats are added easily
* a number of output formats, presently HTML, LaTeX, RTF, SVG, all image
  formats that PIL supports and ANSI sequences
* it is usable as a command-line tool and as a library

Copyright 2006-2025 by the Pygments team, see ``AUTHORS``.
Licensed under the BSD, see ``LICENSE`` for details.
