#!/usr/bin/env python3
"""
测试修复效果的脚本

验证两个主要问题是否得到解决：
1. 新生成的文档格式混乱，丢失
2. 表格三里的内容没有被正确填充
"""

import sys
import os
import logging
from docx import Document

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from processor import DocumentProcessor
from utils import get_test_folder, find_docx_files

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_fixes.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_format_preservation(doc_path):
    """测试格式保留功能"""
    print("\n=== 测试格式保留功能 ===")
    
    try:
        doc = Document(doc_path)
        
        # 检查原始文档的格式信息
        print(f"原始文档: {os.path.basename(doc_path)}")
        print(f"表格数量: {len(doc.tables)}")
        
        for table_idx, table in enumerate(doc.tables):
            print(f"\n表格 {table_idx + 1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 检查格式信息
            format_info = analyze_table_format(table)
            print(f"  格式信息: {format_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式检查失败: {e}")
        return False

def analyze_table_format(table):
    """分析表格格式信息"""
    format_info = {
        'has_bold': False,
        'has_color': False,
        'has_borders': False,
        'font_names': set(),
        'font_sizes': set()
    }
    
    try:
        for row in table.rows:
            for cell in row.cells:
                for para in cell.paragraphs:
                    for run in para.runs:
                        if run.bold:
                            format_info['has_bold'] = True
                        if run.font.name:
                            format_info['font_names'].add(run.font.name)
                        if run.font.size:
                            format_info['font_sizes'].add(str(run.font.size))
                        if run.font.color.rgb:
                            format_info['has_color'] = True
    except Exception as e:
        logger.warning(f"分析格式时出错: {e}")
    
    # 转换集合为列表以便显示
    format_info['font_names'] = list(format_info['font_names'])[:3]  # 只显示前3个
    format_info['font_sizes'] = list(format_info['font_sizes'])[:3]  # 只显示前3个
    
    return format_info

def test_table_filling(doc_path):
    """测试表格填充功能"""
    print("\n=== 测试表格填充功能 ===")
    
    try:
        # 创建处理器
        processor = DocumentProcessor(os.path.dirname(doc_path))
        
        # 处理文件
        result = processor.process_single_file(doc_path)
        
        if "error" in result:
            print(f"❌ 处理失败: {result['error']}")
            return False
        
        print("✅ 文件处理成功!")
        print(f"  项目ID: {result['project_id']}")
        print(f"  映射项数: {result['mapping_count']}")
        
        # 验证生成的文件
        generated_files = [
            result['main_doc'],
            result['tech_params'],
            result['component_config']
        ]
        
        for file_path in generated_files:
            if os.path.exists(file_path):
                print(f"✅ 文件存在: {os.path.basename(file_path)}")
                
                # 检查文档结构
                doc = Document(file_path)
                print(f"  段落数: {len(doc.paragraphs)}")
                print(f"  表格数: {len(doc.tables)}")
                
                # 检查表格填充情况
                unfilled_count = check_unfilled_cells(doc)
                if unfilled_count == 0:
                    print(f"  ✅ 所有单元格都已填充")
                else:
                    print(f"  ⚠️  还有 {unfilled_count} 个未填充单元格")
                    
            else:
                print(f"❌ 文件不存在: {os.path.basename(file_path)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"测试表格填充时出错: {e}", exc_info=True)
        return False

def check_unfilled_cells(doc):
    """检查未填充的单元格数量"""
    unfilled_count = 0
    
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                cell_text = cell.text.strip()
                if any(phrase in cell_text for phrase in [
                    "(投标人填写)", "（投标人填写）", "投标人填写"
                ]):
                    unfilled_count += 1
    
    return unfilled_count

def test_document_structure(file_path):
    """测试文档结构是否合理"""
    print(f"\n=== 测试文档结构: {os.path.basename(file_path)} ===")
    
    try:
        doc = Document(file_path)
        
        paragraph_count = len(doc.paragraphs)
        table_count = len(doc.tables)
        
        print(f"段落数: {paragraph_count}")
        print(f"表格数: {table_count}")
        
        # 检查段落数是否合理
        if "技术参数" in file_path:
            if paragraph_count <= 10:
                print("✅ 技术参数文档结构合理")
                return True
            else:
                print(f"⚠️  技术参数文档段落过多: {paragraph_count}")
                return False
        elif "组件材料" in file_path:
            if paragraph_count <= 5:
                print("✅ 组件配置文档结构合理")
                return True
            else:
                print(f"⚠️  组件配置文档段落过多: {paragraph_count}")
                return False
        else:
            print("✅ 主文档结构检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 结构检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 80)
    print("修复效果测试工具")
    print("=" * 80)
    
    # 测试文件路径 - 需要根据实际情况调整
    test_base_dir = r"F:\2025\7月\国网安徽\国网安徽电力2025年非电网及办公用品物资框架协议公开招标采购_招标文件包\办公家具\包1_完整招标文件_7534782003872905\包1_技术规范书_7534780372181727"
    
    # 查找测试文件夹
    test_folder = get_test_folder(test_base_dir)
    if not test_folder:
        print("❌ 未找到测试文件夹")
        return
    
    print(f"✅ 找到测试文件夹: {test_folder}")
    
    # 查找测试文件
    docx_files = find_docx_files(test_folder, "办公桌")
    if not docx_files:
        print("❌ 未找到测试文件")
        return
    
    test_file = docx_files[0]
    print(f"✅ 使用测试文件: {os.path.basename(test_file)}")
    
    # 执行测试
    tests_passed = 0
    total_tests = 0
    
    # 测试1: 格式保留
    total_tests += 1
    if test_format_preservation(test_file):
        tests_passed += 1
    
    # 测试2: 表格填充
    total_tests += 1
    if test_table_filling(test_file):
        tests_passed += 1
    
    # 测试3: 检查生成的文档结构
    project_id = "G00E-500024815-00005"  # 根据实际情况调整
    output_base = os.path.join(test_folder, project_id)
    
    generated_files = [
        f"{output_base}.docx",
        f"{output_base}-技术参数特征表.docx",
        f"{output_base}-组件材料配置表.docx"
    ]
    
    for file_path in generated_files:
        if os.path.exists(file_path):
            total_tests += 1
            if test_document_structure(file_path):
                tests_passed += 1
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {tests_passed}")
    print(f"失败测试: {total_tests - tests_passed}")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！修复效果良好！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    print("\n详细日志请查看: test_fixes.log")

if __name__ == "__main__":
    main()
