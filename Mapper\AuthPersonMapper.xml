<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper>

	<!--
		查询一个投标授权人信息 
	-->
	<select id="selectOneAuthPerson">
		select OS_ZB_SUPPLIER_AUTH_PERSON_INFO.* from OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO
		inner join OS_ZB_SUPPLIER_AUTH_PERSON_INFO on  OS_ZB_SUPPLIER_AUTH_PERSON_INFO.ATTACH_ID =OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.RELATE_ID
		where OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.MARK_NO='{markNo}' <if test="packName!=null" value=" and OS_ZB_SUPPLIER_BIAO_AUTH_PERSON_INFO.PACK_NAME='{packName}'"/>
		limit 0,1
	</select>


</mapper>